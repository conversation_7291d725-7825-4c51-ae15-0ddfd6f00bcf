��i m p o r t   p a n d a s   a s   p d 
 
 i m p o r t   n u m p y   a s   n p 
 
 i m p o r t   m a t p l o t l i b . p y p l o t   a s   p l t 
 
 i m p o r t   s e a b o r n   a s   s n s 
 
 f r o m   s k l e a r n . m e t r i c s . p a i r w i s e   i m p o r t   c o s i n e _ s i m i l a r i t y 
 
 f r o m   s k l e a r n . f e a t u r e _ e x t r a c t i o n . t e x t   i m p o r t   T f i d f V e c t o r i z e r 
 
 i m p o r t   j i e b a 
 
 i m p o r t   j i e b a . a n a l y s e 
 
 f r o m   w o r d c l o u d   i m p o r t   W o r d C l o u d 
 
 i m p o r t   o p e n a i 
 
 i m p o r t   j s o n 
 
 i m p o r t   r e 
 
 f r o m   t y p i n g   i m p o r t   L i s t ,   D i c t ,   T u p l e 
 
 i m p o r t   w a r n i n g s 
 
 w a r n i n g s . f i l t e r w a r n i n g s ( ' i g n o r e ' ) 
 
 
 
 #   ��n-N�eW[SO
 
 p l t . r c P a r a m s [ ' f o n t . s a n s - s e r i f ' ]   =   [ ' S i m H e i ' ,   ' M i c r o s o f t   Y a H e i ' ] 
 
 p l t . r c P a r a m s [ ' a x e s . u n i c o d e _ m i n u s ' ]   =   F a l s e 
 
 
 
 c l a s s   A n s w e r S i m i l a r i t y A n a l y z e r : 
 
         " " " T{Hh�v<O�^R�ghV" " " 
 
 
 
         d e f   _ _ i n i t _ _ ( s e l f ,   c s v _ p a t h :   s t r ,   o p e n a i _ a p i _ k e y :   s t r   =   N o n e ) : 
 
                 " " " 
 
                 R�YSR�ghV
 
 
 
                 A r g s : 
 
                         c s v _ p a t h :   C S V �e�N_
 
                         o p e n a i _ a p i _ k e y :   O p e n A I   A P I �[��
 
                 " " " 
 
                 s e l f . c s v _ p a t h   =   c s v _ p a t h 
 
                 s e l f . o p e n a i _ a p i _ k e y   =   o p e n a i _ a p i _ k e y 
 
                 s e l f . d f   =   N o n e 
 
                 s e l f . a n s w e r s   =   [ ] 
 
                 s e l f . a n s w e r _ e m b e d d i n g s   =   N o n e 
 
                 s e l f . s i m i l a r i t y _ m a t r i x   =   N o n e 
 
 
 
                 i f   o p e n a i _ a p i _ k e y : 
 
                         o p e n a i . a p i _ k e y   =   o p e n a i _ a p i _ k e y 
 
 
 
         d e f   l o a d _ a n d _ p r e p r o c e s s _ d a t a ( s e l f )   - >   L i s t [ s t r ] : 
 
                 " " " 
 
                 �R}��T��YtC S V penc
 
 
 
                 R e t u r n s : 
 
                         YtT�vT{HhRh�
 
                 " " " 
 
                 p r i n t ( " ck(W�R}�C S V penc. . . " ) 
 
 
 
                 #   ���SC S V �e�N
 
                 s e l f . d f   =   p d . r e a d _ c s v ( s e l f . c s v _ p a t h ,   e n c o d i n g = ' u t f - 8 ' ) 
 
                 p r i n t ( f " pencb_�r:   { s e l f . d f . s h a p e } " ) 
 
 
 
                 #   �h�ga n s _ c o n t e n t R
 
                 i f   ' a n s _ c o n t e n t '   n o t   i n   s e l f . d f . c o l u m n s : 
 
                         r a i s e   V a l u e E r r o r ( " C S V �e�N-N*g~b0R' a n s _ c o n t e n t ' R" ) 
 
 
 
                 #   Yta n s _ c o n t e n t R�Tv^YL��Q�[
 
                 a n s w e r s   =   [ ] 
 
                 c u r r e n t _ a n s w e r   =   " " 
 
                 c u r r e n t _ i d   =   N o n e 
 
 
 
                 f o r   i d x ,   r o w   i n   s e l f . d f . i t e r r o w s ( ) : 
 
                         #   ���SS_MRL��vI D �TT{Hh�Q�[
 
                         r o w _ i d   =   r o w . g e t ( ' i d ' ,   i d x ) 
 
                         a n s _ c o n t e n t   =   s t r ( r o w [ ' a n s _ c o n t e n t ' ] ) . s t r i p ( ) 
 
 
 
                         #   �Y�ga n s _ c o n t e n t 
N:NzzN
N/fN a N 
 
                         i f   a n s _ c o n t e n t   a n d   a n s _ c o n t e n t   ! =   ' n a n ' : 
 
                                 i f   c u r r e n t _ i d   i s   N o n e : 
 
                                         c u r r e n t _ i d   =   r o w _ i d 
 
                                         c u r r e n t _ a n s w e r   =   a n s _ c o n t e n t 
 
                                 e l i f   c u r r e n t _ i d   = =   r o w _ i d : 
 
                                         #   T N*NI D �Tv^�Q�[
 
                                         c u r r e n t _ a n s w e r   + =   " \ n "   +   a n s _ c o n t e n t 
 
                                 e l s e : 
 
                                         #   �e�vI D ��OX[MR N*NT{Hh
 
                                         i f   c u r r e n t _ a n s w e r . s t r i p ( ) : 
 
                                                 a n s w e r s . a p p e n d ( { 
 
                                                         ' i d ' :   c u r r e n t _ i d , 
 
                                                         ' c o n t e n t ' :   c u r r e n t _ a n s w e r . s t r i p ( ) , 
 
                                                         ' m e t a d a t a ' :   { 
 
                                                                 ' r o l e ' :   s e l f . d f [ s e l f . d f [ ' i d ' ]   = =   c u r r e n t _ i d ] [ ' R o l e ' ] . i l o c [ 0 ]   i f   ' R o l e '   i n   s e l f . d f . c o l u m n s   e l s e   N o n e , 
 
                                                                 ' g r o u p ' :   s e l f . d f [ s e l f . d f [ ' i d ' ]   = =   c u r r e n t _ i d ] [ ' G r o u p ' ] . i l o c [ 0 ]   i f   ' G r o u p '   i n   s e l f . d f . c o l u m n s   e l s e   N o n e 
 
                                                         } 
 
                                                 } ) 
 
                                         c u r r e n t _ i d   =   r o w _ i d 
 
                                         c u r r e n t _ a n s w e r   =   a n s _ c o n t e n t 
 
 
 
                 #   �m�R gT N*NT{Hh
 
                 i f   c u r r e n t _ a n s w e r . s t r i p ( ) : 
 
                         a n s w e r s . a p p e n d ( { 
 
                                 ' i d ' :   c u r r e n t _ i d , 
 
                                 ' c o n t e n t ' :   c u r r e n t _ a n s w e r . s t r i p ( ) , 
 
                                 ' m e t a d a t a ' :   { 
 
                                         ' r o l e ' :   s e l f . d f [ s e l f . d f [ ' i d ' ]   = =   c u r r e n t _ i d ] [ ' R o l e ' ] . i l o c [ 0 ]   i f   ' R o l e '   i n   s e l f . d f . c o l u m n s   e l s e   N o n e , 
 
                                         ' g r o u p ' :   s e l f . d f [ s e l f . d f [ ' i d ' ]   = =   c u r r e n t _ i d ] [ ' G r o u p ' ] . i l o c [ 0 ]   i f   ' G r o u p '   i n   s e l f . d f . c o l u m n s   e l s e   N o n e 
 
                                 } 
 
                         } ) 
 
 
 
                 s e l f . a n s w e r s   =   a n s w e r s 
 
                 p r i n t ( f " b�R�c�S  { l e n ( a n s w e r s ) }   *NT{Hh" ) 
 
 
 
                 #   >f:yMR�Q*NT{Hh�v��ȉ
 
                 p r i n t ( " \ n T{Hh��ȉ: " ) 
 
                 f o r   i ,   a n s w e r   i n   e n u m e r a t e ( a n s w e r s [ : 3 ] ) : 
 
                         p r i n t ( f " T{Hh  { i + 1 }   ( I D :   { a n s w e r [ ' i d ' ] } ) : " ) 
 
                         p r i n t ( f " �Q�[:   { a n s w e r [ ' c o n t e n t ' ] [ : 1 0 0 ] } . . . " ) 
 
                         p r i n t ( f " CQpenc:   { a n s w e r [ ' m e t a d a t a ' ] } " ) 
 
                         p r i n t ( " - "   *   5 0 ) 
 
 
 
                 r e t u r n   [ a n s w e r [ ' c o n t e n t ' ]   f o r   a n s w e r   i n   a n s w e r s ] 
 
 
 
         d e f   c l e a n _ t e x t ( s e l f ,   t e x t :   s t r )   - >   s t r : 
 
                 " " " 
 
                 nt�e,g�Q�[
 
 
 
                 A r g s : 
 
                         t e x t :   �S�Y�e,g
 
 
 
                 R e t u r n s : 
 
                         ntT�v�e,g
 
                 " " " 
 
                 #   �yd�YYO�vzz}vW[&{
 
                 t e x t   =   r e . s u b ( r ' \ s + ' ,   '   ' ,   t e x t ) 
 
                 #   �yd�yr�kW[&{FO�OYu-N�eh�p
 
                 t e x t   =   r e . s u b ( r ' [ ^ \ u 4 e 0 0 - \ u 9 f f f \ w \ s �0����" " ' ' �	�00] ' ,   ' ' ,   t e x t ) 
 
                 r e t u r n   t e x t . s t r i p ( ) 
 
 
 
         d e f   s e g m e n t _ t e x t ( s e l f ,   t e x t :   s t r )   - >   L i s t [ s t r ] : 
 
                 " " " 
 
                 -N�eR͋
 
 
 
                 A r g s : 
 
                         t e x t :   ��eQ�e,g
 
 
 
                 R e t u r n s : 
 
                         R͋�~�gRh�
 
                 " " " 
 
                 #   nt�e,g
 
                 c l e a n e d _ t e x t   =   s e l f . c l e a n _ t e x t ( t e x t ) 
 
 
 
                 #   O(uj i e b a R͋
 
                 w o r d s   =   j i e b a . l c u t ( c l e a n e d _ t e x t ) 
 
 
 
                 #   Ǐ�n\P(u͋�T�w͋
 
                 s t o p _ w o r d s   =   { ' �v' ,   ' �N' ,   ' (W' ,   ' /f' ,   ' b' ,   ' 	g' ,   ' �T' ,   ' 1\' ,   ' 
N' ,   ' �N' ,   ' ��' ,   '  N' ,   '  N*N' ,   ' 
N' ,   ' _N' ,   ' �_' ,   ' 0R' ,   ' �' ,   ' ��' ,   ' �S' ,   ' `O' ,   ' O' ,   ' @w' ,   ' �l	g' ,   ' w' ,   ' }Y' ,   ' ��]' ,   ' ُ' } 
 
                 f i l t e r e d _ w o r d s   =   [ w o r d   f o r   w o r d   i n   w o r d s   i f   l e n ( w o r d )   >   1   a n d   w o r d   n o t   i n   s t o p _ w o r d s ] 
 
 
 
                 r e t u r n   f i l t e r e d _ w o r d s 
 
 
 
         d e f   g e t _ b e r t _ e m b e d d i n g s ( s e l f ,   t e x t s :   L i s t [ s t r ] )   - >   n p . n d a r r a y : 
 
                 " " " 
 
                 O(uB E R T !j�W���S�e,gTϑ
 
 
 
                 A r g s : 
 
                         t e x t s :   �e,gRh�
 
 
 
                 R e t u r n s : 
 
                         B E R T Tϑ�w5�
 
                 " " " 
 
                 t r y : 
 
                         f r o m   t r a n s f o r m e r s   i m p o r t   A u t o T o k e n i z e r ,   A u t o M o d e l 
 
                         i m p o r t   t o r c h 
 
 
 
                         p r i n t ( " ck(W�R}�B E R T !j�W. . . " ) 
 
                         #   O(u-N�eB E R T !j�W
 
                         m o d e l _ n a m e   =   " b e r t - b a s e - c h i n e s e " 
 
                         t o k e n i z e r   =   A u t o T o k e n i z e r . f r o m _ p r e t r a i n e d ( m o d e l _ n a m e ) 
 
                         m o d e l   =   A u t o M o d e l . f r o m _ p r e t r a i n e d ( m o d e l _ n a m e ) 
 
 
 
                         e m b e d d i n g s   =   [ ] 
 
                         b a t c h _ s i z e   =   8     #   ybYt'Y\
 
 
 
                         p r i n t ( f " ck(WYt  { l e n ( t e x t s ) }   *N�e,g. . . " ) 
 
                         f o r   i   i n   r a n g e ( 0 ,   l e n ( t e x t s ) ,   b a t c h _ s i z e ) : 
 
                                 b a t c h _ t e x t s   =   t e x t s [ i : i + b a t c h _ s i z e ] 
 
 
 
                                 #   R͋�Tx
 
                                 i n p u t s   =   t o k e n i z e r ( b a t c h _ t e x t s , 
 
                                                                   p a d d i n g = T r u e , 
 
                                                                   t r u n c a t i o n = T r u e , 
 
                                                                   m a x _ l e n g t h = 5 1 2 , 
 
                                                                   r e t u r n _ t e n s o r s = " p t " ) 
 
 
 
                                 #   ���S!j�W���Q
 
                                 w i t h   t o r c h . n o _ g r a d ( ) : 
 
                                         o u t p u t s   =   m o d e l ( * * i n p u t s ) 
 
                                         #   O(u[ C L S ] h���vTϑ\O:N�SP[h�:y
 
                                         b a t c h _ e m b e d d i n g s   =   o u t p u t s . l a s t _ h i d d e n _ s t a t e [ : ,   0 ,   : ] . n u m p y ( ) 
 
                                         e m b e d d i n g s . e x t e n d ( b a t c h _ e m b e d d i n g s ) 
 
 
 
                                 p r i n t ( f " �]Yt  { m i n ( i + b a t c h _ s i z e ,   l e n ( t e x t s ) ) } / { l e n ( t e x t s ) }   *N�e,g" ) 
 
 
 
                         s e l f . a n s w e r _ e m b e d d i n g s   =   n p . a r r a y ( e m b e d d i n g s ) 
 
                         p r i n t ( f " B E R T TϑS�[b�Tϑ�~�^:   { s e l f . a n s w e r _ e m b e d d i n g s . s h a p e } " ) 
 
 
 
                         r e t u r n   s e l f . a n s w e r _ e m b e d d i n g s 
 
 
 
                 e x c e p t   I m p o r t E r r o r : 
 
                         p r i n t ( " *g�[ňt r a n s f o r m e r s �^�O(uT F - I D F \O:N�f�N�eHh. . . " ) 
 
                         r e t u r n   s e l f . g e t _ t f i d f _ e m b e d d i n g s ( t e x t s ) 
 
 
 
         d e f   g e t _ t f i d f _ e m b e d d i n g s ( s e l f ,   t e x t s :   L i s t [ s t r ] )   - >   n p . n d a r r a y : 
 
                 " " " 
 
                 O(uT F - I D F ���S�e,gTϑ�B E R T �v�f�N�eHh	�
 
 
 
                 A r g s : 
 
                         t e x t s :   �e,gRh�
 
 
 
                 R e t u r n s : 
 
                         T F - I D F Tϑ�w5�
 
                 " " " 
 
                 p r i n t ( " ck(WO(uT F - I D F ۏL�TϑS. . . " ) 
 
 
 
                 #   �[�e,gۏL�R͋
 
                 s e g m e n t e d _ t e x t s   =   [ ] 
 
                 f o r   t e x t   i n   t e x t s : 
 
                         w o r d s   =   s e l f . s e g m e n t _ t e x t ( t e x t ) 
 
                         s e g m e n t e d _ t e x t s . a p p e n d ( '   ' . j o i n ( w o r d s ) ) 
 
 
 
                 #   O(uT F - I D F TϑS
 
                 v e c t o r i z e r   =   T f i d f V e c t o r i z e r ( m a x _ f e a t u r e s = 1 0 0 0 ,   n g r a m _ r a n g e = ( 1 ,   2 ) ) 
 
                 t f i d f _ m a t r i x   =   v e c t o r i z e r . f i t _ t r a n s f o r m ( s e g m e n t e d _ t e x t s ) 
 
 
 
                 s e l f . a n s w e r _ e m b e d d i n g s   =   t f i d f _ m a t r i x . t o a r r a y ( ) 
 
                 p r i n t ( f " T F - I D F TϑS�[b�Tϑ�~�^:   { s e l f . a n s w e r _ e m b e d d i n g s . s h a p e } " ) 
 
 
 
                 r e t u r n   s e l f . a n s w e r _ e m b e d d i n g s 
 
 
 
         d e f   c a l c u l a t e _ s i m i l a r i t y _ m a t r i x ( s e l f )   - >   n p . n d a r r a y : 
 
                 " " " 
 
                 ���{�v<O�^�w5�
 
 
 
                 R e t u r n s : 
 
                         �v<O�^�w5�
 
                 " " " 
 
                 i f   s e l f . a n s w e r _ e m b e d d i n g s   i s   N o n e : 
 
                         r a i s e   V a l u e E r r o r ( " ��HQۏL�TϑS" ) 
 
 
 
                 p r i n t ( " ck(W���{�v<O�^�w5�. . . " ) 
 
                 s e l f . s i m i l a r i t y _ m a t r i x   =   c o s i n e _ s i m i l a r i t y ( s e l f . a n s w e r _ e m b e d d i n g s ) 
 
                 p r i n t ( f " �v<O�^�w5����{�[b�b_�r:   { s e l f . s i m i l a r i t y _ m a t r i x . s h a p e } " ) 
 
 
 
                 r e t u r n   s e l f . s i m i l a r i t y _ m a t r i x 
 
 
 
         d e f   v i s u a l i z e _ s i m i l a r i t y _ m a t r i x ( s e l f ,   s a v e _ p a t h :   s t r   =   " s i m i l a r i t y _ m a t r i x . p n g " ) : 
 
                 " " " 
 
                 �SƉS�v<O�^�w5�
 
 
 
                 A r g s : 
 
                         s a v e _ p a t h :   �OX[_
 
                 " " " 
 
                 i f   s e l f . s i m i l a r i t y _ m a t r i x   i s   N o n e : 
 
                         r a i s e   V a l u e E r r o r ( " ��HQ���{�v<O�^�w5�" ) 
 
 
 
                 p l t . f i g u r e ( f i g s i z e = ( 1 2 ,   1 0 ) ) 
 
 
 
                 #   R�^�p�R�V
 
                 m a s k   =   n p . t r i u ( n p . o n e s _ l i k e ( s e l f . s i m i l a r i t y _ m a t r i x ,   d t y p e = b o o l ) )     #   �S>f:yN	N҉
 
                 s n s . h e a t m a p ( s e l f . s i m i l a r i t y _ m a t r i x , 
 
                                       m a s k = m a s k , 
 
                                       a n n o t = F a l s e , 
 
                                       c m a p = ' c o o l w a r m ' , 
 
                                       c e n t e r = 0 , 
 
                                       s q u a r e = T r u e , 
 
                                       f m t = ' . 2 f ' , 
 
                                       c b a r _ k w s = { " s h r i n k " :   . 8 } ) 
 
 
 
                 p l t . t i t l e ( ' T{Hh�v<O�^�w5�' ,   f o n t s i z e = 1 6 ,   f o n t w e i g h t = ' b o l d ' ) 
 
                 p l t . x l a b e l ( ' T{Hh"}_' ,   f o n t s i z e = 1 2 ) 
 
                 p l t . y l a b e l ( ' T{Hh"}_' ,   f o n t s i z e = 1 2 ) 
 
                 p l t . t i g h t _ l a y o u t ( ) 
 
                 p l t . s a v e f i g ( s a v e _ p a t h ,   d p i = 3 0 0 ,   b b o x _ i n c h e s = ' t i g h t ' ) 
 
                 p l t . s h o w ( ) 
 
 
 
                 p r i n t ( f " �v<O�^�w5��SƉS�]�OX[0R:   { s a v e _ p a t h } " ) 
 
 
 
         d e f   f i n d _ m o s t _ s i m i l a r _ p a i r s ( s e l f ,   t o p _ k :   i n t   =   1 0 )   - >   L i s t [ T u p l e ] : 
 
                 " " " 
 
                 ~b�Q g�v<O�vT{Hh�[
 
 
 
                 A r g s : 
 
                         t o p _ k :   ԏ�VMRk *N g�v<O�v�[
 
 
 
                 R e t u r n s : 
 
                         �v<O�^ gؚ�vT{Hh�[Rh�
 
                 " " " 
 
                 i f   s e l f . s i m i l a r i t y _ m a t r i x   i s   N o n e : 
 
                         r a i s e   V a l u e E r r o r ( " ��HQ���{�v<O�^�w5�" ) 
 
 
 
                 #   ���S
N	N҉�w5��v"}_�T<P��cd��[҉�~	�
 
                 t r i u _ i n d i c e s   =   n p . t r i u _ i n d i c e s _ f r o m ( s e l f . s i m i l a r i t y _ m a t r i x ,   k = 1 ) 
 
                 s i m i l a r i t i e s   =   s e l f . s i m i l a r i t y _ m a t r i x [ t r i u _ i n d i c e s ] 
 
 
 
                 #   ���S g�v<O�v�[
 
                 t o p _ i n d i c e s   =   n p . a r g s o r t ( s i m i l a r i t i e s ) [ - t o p _ k : ] [ : : - 1 ] 
 
 
 
                 s i m i l a r _ p a i r s   =   [ ] 
 
                 f o r   i d x   i n   t o p _ i n d i c e s : 
 
                         i ,   j   =   t r i u _ i n d i c e s [ 0 ] [ i d x ] ,   t r i u _ i n d i c e s [ 1 ] [ i d x ] 
 
                         s i m i l a r i t y   =   s i m i l a r i t i e s [ i d x ] 
 
 
 
                         s i m i l a r _ p a i r s . a p p e n d ( { 
 
                                 ' a n s w e r 1 _ i d x ' :   i , 
 
                                 ' a n s w e r 2 _ i d x ' :   j , 
 
                                 ' a n s w e r 1 _ i d ' :   s e l f . a n s w e r s [ i ] [ ' i d ' ] , 
 
                                 ' a n s w e r 2 _ i d ' :   s e l f . a n s w e r s [ j ] [ ' i d ' ] , 
 
                                 ' s i m i l a r i t y ' :   s i m i l a r i t y , 
 
                                 ' a n s w e r 1 _ c o n t e n t ' :   s e l f . a n s w e r s [ i ] [ ' c o n t e n t ' ] [ : 1 0 0 ]   +   " . . . " , 
 
                                 ' a n s w e r 2 _ c o n t e n t ' :   s e l f . a n s w e r s [ j ] [ ' c o n t e n t ' ] [ : 1 0 0 ]   +   " . . . " 
 
                         } ) 
 
 
 
                 r e t u r n   s i m i l a r _ p a i r s 
 
 
 
         d e f   a n a l y z e _ t h e m e s _ w i t h _ o p e n a i ( s e l f ,   s a m p l e _ s i z e :   i n t   =   2 0 )   - >   D i c t : 
 
                 " " " 
 
                 O(uO p e n A I ۏL�;N��R�g
 
 
 
                 A r g s : 
 
                         s a m p l e _ s i z e :   R�g�v7h,gpeϑ
 
 
 
                 R e t u r n s : 
 
                         ;N��R�g�~�g
 
                 " " " 
 
                 i f   n o t   s e l f . o p e n a i _ a p i _ k e y : 
 
                         p r i n t ( " *g�c�OO p e n A I   A P I �[����Ǐ;N��R�g" ) 
 
                         r e t u r n   { } 
 
 
 
                 #   	��b7h,gۏL�R�g
 
                 s a m p l e _ a n s w e r s   =   s e l f . a n s w e r s [ : s a m p l e _ s i z e ]   i f   l e n ( s e l f . a n s w e r s )   >   s a m p l e _ s i z e   e l s e   s e l f . a n s w e r s 
 
                 c o m b i n e d _ t e x t   =   " \ n \ n " . j o i n ( [ f " T{Hh{ i + 1 } :   { a n s w e r [ ' c o n t e n t ' ] } "   f o r   i ,   a n s w e r   i n   e n u m e r a t e ( s a m p l e _ a n s w e r s ) ] ) 
 
 
 
                 t r y : 
 
                         p r i n t ( f " ck(WO(uO p e n A I R�g  { l e n ( s a m p l e _ a n s w e r s ) }   *NT{Hh�v;N��. . . " ) 
 
 
 
                         p r o m p t   =   f " " " 
 
                         ��R�g�NNT{Hh�v;N��;N���T�Q�[yr�p�
 
 
 
                         { c o m b i n e d _ t e x t } 
 
 
 
                         ���c�O�NNR�g�
 
                         1 .   ;N��;N���3 - 5 *N	�
 
                         2 .   sQ.�͋Gl�1 0 - 1 5 *N	�
 
                         3 .   T{Hh�vteSOyr�p
 
                         4 .   �Q�[R{|�^��
 
 
 
                         ��(uJ S O N <h_ԏ�V�~�g0
 
                         " " " 
 
 
 
                         r e s p o n s e   =   o p e n a i . C h a t C o m p l e t i o n . c r e a t e ( 
 
                                 m o d e l = " g p t - 3 . 5 - t u r b o " , 
 
                                 m e s s a g e s = [ 
 
                                         { " r o l e " :   " s y s t e m " ,   " c o n t e n t " :   " `O/f N*NNN�v�e,gR�gN�[��d�;N���c�S�T�Q�[R�g0" } , 
 
                                         { " r o l e " :   " u s e r " ,   " c o n t e n t " :   p r o m p t } 
 
                                 ] , 
 
                                 m a x _ t o k e n s = 1 0 0 0 , 
 
                                 t e m p e r a t u r e = 0 . 3 
 
                         ) 
 
 
 
                         r e s u l t   =   r e s p o n s e . c h o i c e s [ 0 ] . m e s s a g e . c o n t e n t 
 
 
 
                         t r y : 
 
                                 #   \Ջ㉐gJ S O N 
 
                                 t h e m e _ a n a l y s i s   =   j s o n . l o a d s ( r e s u l t ) 
 
                         e x c e p t   j s o n . J S O N D e c o d e E r r o r : 
 
                                 #   �Y�g
N/f	gHeJ S O N �ԏ�V�S�Y�e,g
 
                                 t h e m e _ a n a l y s i s   =   { " r a w _ a n a l y s i s " :   r e s u l t } 
 
 
 
                         p r i n t ( " O p e n A I ;N��R�g�[b" ) 
 
                         r e t u r n   t h e m e _ a n a l y s i s 
 
 
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         p r i n t ( f " O p e n A I ;N��R�g�Q�:   { e } " ) 
 
                         r e t u r n   { " e r r o r " :   s t r ( e ) } 
 
 
 
         d e f   g e n e r a t e _ w o r d c l o u d ( s e l f ,   s a v e _ p a t h :   s t r   =   " w o r d c l o u d . p n g " ,   m a x _ w o r d s :   i n t   =   1 0 0 ) : 
 
                 " " " 
 
                 ub͋�N
 
 
 
                 A r g s : 
 
                         s a v e _ p a t h :   �OX[_
 
                         m a x _ w o r d s :    g'Y͋pe
 
                 " " " 
 
                 p r i n t ( " ck(Wub͋�N. . . " ) 
 
 
 
                 #   Tv^@b	gT{Hh�e,g
 
                 a l l _ t e x t   =   "   " . j o i n ( [ a n s w e r [ ' c o n t e n t ' ]   f o r   a n s w e r   i n   s e l f . a n s w e r s ] ) 
 
 
 
                 #   R͋
 
                 w o r d s   =   s e l f . s e g m e n t _ t e x t ( a l l _ t e x t ) 
 
                 t e x t _ f o r _ w o r d c l o u d   =   "   " . j o i n ( w o r d s ) 
 
 
 
                 t r y : 
 
                         #   R�^͋�N
 
                         w o r d c l o u d   =   W o r d C l o u d ( 
 
                                 f o n t _ p a t h = ' s i m h e i . t t f ' ,     #   -N�eW[SO_��S�� ����te
 
                                 w i d t h = 8 0 0 , 
 
                                 h e i g h t = 6 0 0 , 
 
                                 b a c k g r o u n d _ c o l o r = ' w h i t e ' , 
 
                                 m a x _ w o r d s = m a x _ w o r d s , 
 
                                 c o l o r m a p = ' v i r i d i s ' , 
 
                                 r e l a t i v e _ s c a l i n g = 0 . 5 , 
 
                                 r a n d o m _ s t a t e = 4 2 
 
                         ) . g e n e r a t e ( t e x t _ f o r _ w o r d c l o u d ) 
 
 
 
                         #   >f:y͋�N
 
                         p l t . f i g u r e ( f i g s i z e = ( 1 2 ,   8 ) ) 
 
                         p l t . i m s h o w ( w o r d c l o u d ,   i n t e r p o l a t i o n = ' b i l i n e a r ' ) 
 
                         p l t . a x i s ( ' o f f ' ) 
 
                         p l t . t i t l e ( ' T{Hh�Q�[͋�N' ,   f o n t s i z e = 1 6 ,   f o n t w e i g h t = ' b o l d ' ) 
 
                         p l t . t i g h t _ l a y o u t ( ) 
 
                         p l t . s a v e f i g ( s a v e _ p a t h ,   d p i = 3 0 0 ,   b b o x _ i n c h e s = ' t i g h t ' ) 
 
                         p l t . s h o w ( ) 
 
 
 
                         p r i n t ( f " ͋�N�]�OX[0R:   { s a v e _ p a t h } " ) 
 
 
 
                 e x c e p t   E x c e p t i o n   a s   e : 
 
                         p r i n t ( f " ͋�Nub�Q�:   { e } " ) 
 
                         #   O(um a t p l o t l i b ub�{US�v͋���V\O:N�f�N
 
                         s e l f . g e n e r a t e _ w o r d _ f r e q u e n c y _ c h a r t ( w o r d s ,   s a v e _ p a t h . r e p l a c e ( ' . p n g ' ,   ' _ f r e q . p n g ' ) ) 
 
 
 
         d e f   g e n e r a t e _ w o r d _ f r e q u e n c y _ c h a r t ( s e l f ,   w o r d s :   L i s t [ s t r ] ,   s a v e _ p a t h :   s t r   =   " w o r d _ f r e q u e n c y . p n g " ) : 
 
                 " " " 
 
                 ub͋���Vh�
 
 
 
                 A r g s : 
 
                         w o r d s :   ͋GlRh�
 
                         s a v e _ p a t h :   �OX[_
 
                 " " " 
 
                 f r o m   c o l l e c t i o n s   i m p o r t   C o u n t e r 
 
 
 
                 #   �~��͋��
 
                 w o r d _ f r e q   =   C o u n t e r ( w o r d s ) 
 
                 t o p _ w o r d s   =   w o r d _ f r e q . m o s t _ c o m m o n ( 2 0 ) 
 
 
 
                 #   R�^agb_�V
 
                 w o r d s _ l i s t ,   f r e q s   =   z i p ( * t o p _ w o r d s ) 
 
 
 
                 p l t . f i g u r e ( f i g s i z e = ( 1 2 ,   8 ) ) 
 
                 b a r s   =   p l t . b a r h ( r a n g e ( l e n ( w o r d s _ l i s t ) ) ,   f r e q s ) 
 
                 p l t . y t i c k s ( r a n g e ( l e n ( w o r d s _ l i s t ) ) ,   w o r d s _ l i s t ) 
 
                 p l t . x l a b e l ( ' ��!k' ,   f o n t s i z e = 1 2 ) 
 
                 p l t . t i t l e ( ' ؚ��͋Gl�~��' ,   f o n t s i z e = 1 6 ,   f o n t w e i g h t = ' b o l d ' ) 
 
                 p l t . g c a ( ) . i n v e r t _ y a x i s ( ) 
 
 
 
                 #   �m�Rpe<Ph~{
 
                 f o r   i ,   b a r   i n   e n u m e r a t e ( b a r s ) : 
 
                         w i d t h   =   b a r . g e t _ w i d t h ( ) 
 
                         p l t . t e x t ( w i d t h   +   0 . 1 ,   b a r . g e t _ y ( )   +   b a r . g e t _ h e i g h t ( ) / 2 , 
 
                                         s t r ( f r e q s [ i ] ) ,   h a = ' l e f t ' ,   v a = ' c e n t e r ' ) 
 
 
 
                 p l t . t i g h t _ l a y o u t ( ) 
 
                 p l t . s a v e f i g ( s a v e _ p a t h ,   d p i = 3 0 0 ,   b b o x _ i n c h e s = ' t i g h t ' ) 
 
                 p l t . s h o w ( ) 
 
 
 
                 p r i n t ( f " ͋���Vh��]�OX[0R:   { s a v e _ p a t h } " ) 
 
 
 
         d e f   g e n e r a t e _ c o m p r e h e n s i v e _ r e p o r t ( s e l f ,   o u t p u t _ p a t h :   s t r   =   " s i m i l a r i t y _ a n a l y s i s _ r e p o r t . j s o n " ) : 
 
                 " " " 
 
                 ub�~TR�g�bJT
 
 
 
                 A r g s : 
 
                         o u t p u t _ p a t h :   ���Q_
 
                 " " " 
 
                 p r i n t ( " ck(Wub�~TR�g�bJT. . . " ) 
 
 
 
                 #   �W,g�~���Oo`
 
                 b a s i c _ s t a t s   =   { 
 
                         ' t o t a l _ a n s w e r s ' :   l e n ( s e l f . a n s w e r s ) , 
 
                         ' a v e r a g e _ l e n g t h ' :   n p . m e a n ( [ l e n ( a n s w e r [ ' c o n t e n t ' ] )   f o r   a n s w e r   i n   s e l f . a n s w e r s ] ) , 
 
                         ' m i n _ l e n g t h ' :   m i n ( [ l e n ( a n s w e r [ ' c o n t e n t ' ] )   f o r   a n s w e r   i n   s e l f . a n s w e r s ] ) , 
 
                         ' m a x _ l e n g t h ' :   m a x ( [ l e n ( a n s w e r [ ' c o n t e n t ' ] )   f o r   a n s w e r   i n   s e l f . a n s w e r s ] ) 
 
                 } 
 
 
 
                 #   �v<O�^�~��
 
                 s i m i l a r i t y _ s t a t s   =   { } 
 
                 i f   s e l f . s i m i l a r i t y _ m a t r i x   i s   n o t   N o n e : 
 
                         #   �cd��[҉�~CQ }
 
                         t r i u _ i n d i c e s   =   n p . t r i u _ i n d i c e s _ f r o m ( s e l f . s i m i l a r i t y _ m a t r i x ,   k = 1 ) 
 
                         s i m i l a r i t i e s   =   s e l f . s i m i l a r i t y _ m a t r i x [ t r i u _ i n d i c e s ] 
 
 
 
                         s i m i l a r i t y _ s t a t s   =   { 
 
                                 ' m e a n _ s i m i l a r i t y ' :   f l o a t ( n p . m e a n ( s i m i l a r i t i e s ) ) , 
 
                                 ' s t d _ s i m i l a r i t y ' :   f l o a t ( n p . s t d ( s i m i l a r i t i e s ) ) , 
 
                                 ' m i n _ s i m i l a r i t y ' :   f l o a t ( n p . m i n ( s i m i l a r i t i e s ) ) , 
 
                                 ' m a x _ s i m i l a r i t y ' :   f l o a t ( n p . m a x ( s i m i l a r i t i e s ) ) , 
 
                                 ' h i g h _ s i m i l a r i t y _ p a i r s ' :   i n t ( n p . s u m ( s i m i l a r i t i e s   >   0 . 8 ) ) , 
 
                                 ' m e d i u m _ s i m i l a r i t y _ p a i r s ' :   i n t ( n p . s u m ( ( s i m i l a r i t i e s   >   0 . 5 )   &   ( s i m i l a r i t i e s   < =   0 . 8 ) ) ) , 
 
                                 ' l o w _ s i m i l a r i t y _ p a i r s ' :   i n t ( n p . s u m ( s i m i l a r i t i e s   < =   0 . 5 ) ) 
 
                         } 
 
 
 
                 #    g�v<O�vT{Hh�[
 
                 m o s t _ s i m i l a r   =   s e l f . f i n d _ m o s t _ s i m i l a r _ p a i r s ( t o p _ k = 5 )   i f   s e l f . s i m i l a r i t y _ m a t r i x   i s   n o t   N o n e   e l s e   [ ] 
 
 
 
                 #   O p e n A I ;N��R�g
 
                 t h e m e _ a n a l y s i s   =   s e l f . a n a l y z e _ t h e m e s _ w i t h _ o p e n a i ( ) 
 
 
 
                 #   ub�bJT
 
                 r e p o r t   =   { 
 
                         ' a n a l y s i s _ t i m e s t a m p ' :   p d . T i m e s t a m p . n o w ( ) . i s o f o r m a t ( ) , 
 
                         ' b a s i c _ s t a t i s t i c s ' :   b a s i c _ s t a t s , 
 
                         ' s i m i l a r i t y _ s t a t i s t i c s ' :   s i m i l a r i t y _ s t a t s , 
 
                         ' m o s t _ s i m i l a r _ p a i r s ' :   m o s t _ s i m i l a r , 
 
                         ' t h e m e _ a n a l y s i s ' :   t h e m e _ a n a l y s i s , 
 
                         ' m e t h o d o l o g y ' :   { 
 
                                 ' v e c t o r i z a t i o n ' :   ' B E R T   ( b e r t - b a s e - c h i n e s e )   o r   T F - I D F   a s   f a l l b a c k ' , 
 
                                 ' s i m i l a r i t y _ m e t r i c ' :   ' C o s i n e   S i m i l a r i t y ' , 
 
                                 ' t e x t _ p r e p r o c e s s i n g ' :   ' C h i n e s e   w o r d   s e g m e n t a t i o n   w i t h   j i e b a ' 
 
                         } 
 
                 } 
 
 
 
                 #   �OX[�bJT
 
                 w i t h   o p e n ( o u t p u t _ p a t h ,   ' w ' ,   e n c o d i n g = ' u t f - 8 ' )   a s   f : 
 
                         j s o n . d u m p ( r e p o r t ,   f ,   e n s u r e _ a s c i i = F a l s e ,   i n d e n t = 2 ) 
 
 
 
                 p r i n t ( f " �~TR�g�bJT�]�OX[0R:   { o u t p u t _ p a t h } " ) 
 
                 r e t u r n   r e p o r t 
 
 
 
 d e f   m a i n ( ) : 
 
         " " " ;N�Qpe  -   �[te�vR�gAmz" " " 
 
         p r i n t ( " = "   *   6 0 ) 
 
         p r i n t ( " T{Hh�v<O�^R�g�|�~" ) 
 
         p r i n t ( " = "   *   6 0 ) 
 
 
 
         #   R�YSR�ghV
 
         #   �Y�g	gO p e n A I   A P I �[�����(Wُ̑��n
 
         o p e n a i _ a p i _ k e y   =   N o n e     #   ����n�`�vO p e n A I   A P I �[��
 
         a n a l y z e r   =   A n s w e r S i m i l a r i t y A n a l y z e r ( " a l l _ i n f o _ w i t h _ c o v e r _ m e t r i c s _ a i . c s v " ,   o p e n a i _ a p i _ k e y ) 
 
 
 
         t r y : 
 
                 #   ek��1 :   penc��Yt
 
                 p r i n t ( " \ n ek��1 :   penc��Yt" ) 
 
                 a n s w e r _ t e x t s   =   a n a l y z e r . l o a d _ a n d _ p r e p r o c e s s _ d a t a ( ) 
 
 
 
                 i f   l e n ( a n s w e r _ t e x t s )   = =   0 : 
 
                         p r i n t ( " *g~b0R	gHe�vT{Hhpenc" ) 
 
                         r e t u r n 
 
 
 
                 #   ek��2 :   B E R T TϑS
 
                 p r i n t ( " \ n ek��2 :   �e,gTϑS" ) 
 
                 e m b e d d i n g s   =   a n a l y z e r . g e t _ b e r t _ e m b e d d i n g s ( a n s w e r _ t e x t s ) 
 
 
 
                 #   ek��3 :   ���{�v<O�^�w5�
 
                 p r i n t ( " \ n ek��3 :   ���{�v<O�^�w5�" ) 
 
                 s i m i l a r i t y _ m a t r i x   =   a n a l y z e r . c a l c u l a t e _ s i m i l a r i t y _ m a t r i x ( ) 
 
 
 
                 #   ek��4 :   �SƉS�v<O�^�w5�
 
                 p r i n t ( " \ n ek��4 :   ub�v<O�^�w5��SƉS" ) 
 
                 a n a l y z e r . v i s u a l i z e _ s i m i l a r i t y _ m a t r i x ( ) 
 
 
 
                 #   ek��5 :   ~b�Q g�v<O�vT{Hh�[
 
                 p r i n t ( " \ n ek��5 :   R�g g�v<O�vT{Hh�[" ) 
 
                 s i m i l a r _ p a i r s   =   a n a l y z e r . f i n d _ m o s t _ s i m i l a r _ p a i r s ( t o p _ k = 1 0 ) 
 
 
 
                 p r i n t ( " \ n  g�v<O�vT{Hh�[: " ) 
 
                 f o r   i ,   p a i r   i n   e n u m e r a t e ( s i m i l a r _ p a i r s [ : 5 ] ,   1 ) : 
 
                         p r i n t ( f " \ n { i } .   �v<O�^:   { p a i r [ ' s i m i l a r i t y ' ] : . 3 f } " ) 
 
                         p r i n t ( f "       T{Hh1   ( I D :   { p a i r [ ' a n s w e r 1 _ i d ' ] } ) :   { p a i r [ ' a n s w e r 1 _ c o n t e n t ' ] } " ) 
 
                         p r i n t ( f "       T{Hh2   ( I D :   { p a i r [ ' a n s w e r 2 _ i d ' ] } ) :   { p a i r [ ' a n s w e r 2 _ c o n t e n t ' ] } " ) 
 
 
 
                 #   ek��6 :   ub͋�N
 
                 p r i n t ( " \ n ek��6 :   ub͋�N" ) 
 
                 a n a l y z e r . g e n e r a t e _ w o r d c l o u d ( ) 
 
 
 
                 #   ek��7 :   O p e n A I ;N��R�g��Y�g�c�O�NA P I �[��	�
 
                 i f   o p e n a i _ a p i _ k e y : 
 
                         p r i n t ( " \ n ek��7 :   O p e n A I ;N��R�g" ) 
 
                         t h e m e _ a n a l y s i s   =   a n a l y z e r . a n a l y z e _ t h e m e s _ w i t h _ o p e n a i ( ) 
 
                         p r i n t ( " ;N��R�g�~�g: " ) 
 
                         p r i n t ( j s o n . d u m p s ( t h e m e _ a n a l y s i s ,   e n s u r e _ a s c i i = F a l s e ,   i n d e n t = 2 ) ) 
 
                 e l s e : 
 
                         p r i n t ( " \ n ek��7 :   �ǏO p e n A I ;N��R�g�*g�c�OA P I �[��	�" ) 
 
 
 
                 #   ek��8 :   ub�~T�bJT
 
                 p r i n t ( " \ n ek��8 :   ub�~TR�g�bJT" ) 
 
                 r e p o r t   =   a n a l y z e r . g e n e r a t e _ c o m p r e h e n s i v e _ r e p o r t ( ) 
 
 
 
                 p r i n t ( " \ n "   +   " = "   *   6 0 ) 
 
                 p r i n t ( " R�g�[b�ub�v�e�N: " ) 
 
                 p r i n t ( " -   s i m i l a r i t y _ m a t r i x . p n g :   �v<O�^�w5��p�R�V" ) 
 
                 p r i n t ( " -   w o r d c l o u d . p n g :   ͋�N�V" ) 
 
                 p r i n t ( " -   s i m i l a r i t y _ a n a l y s i s _ r e p o r t . j s o n :   �~TR�g�bJT" ) 
 
                 p r i n t ( " -   p r o c e s s e d _ a n s w e r s . j s o n :   ��YtT�vT{Hhpenc" ) 
 
                 p r i n t ( " = "   *   6 0 ) 
 
 
 
                 #   >f:y�W,g�~���Oo`
 
                 p r i n t ( f " \ n �W,g�~���Oo`: " ) 
 
                 p r i n t ( f " -   ;`T{Hhpe:   { r e p o r t [ ' b a s i c _ s t a t i s t i c s ' ] [ ' t o t a l _ a n s w e r s ' ] } " ) 
 
                 p r i n t ( f " -   s^GW��^:   { r e p o r t [ ' b a s i c _ s t a t i s t i c s ' ] [ ' a v e r a g e _ l e n g t h ' ] : . 1 f }   W[&{" ) 
 
                 i f   ' s i m i l a r i t y _ s t a t i s t i c s '   i n   r e p o r t   a n d   r e p o r t [ ' s i m i l a r i t y _ s t a t i s t i c s ' ] : 
 
                         p r i n t ( f " -   s^GW�v<O�^:   { r e p o r t [ ' s i m i l a r i t y _ s t a t i s t i c s ' ] . g e t ( ' m e a n _ s i m i l a r i t y ' ,   ' N / A ' ) : . 3 f } " ) 
 
                         p r i n t ( f " -   ؚ�v<O�^�[pe  ( > 0 . 8 ) :   { r e p o r t [ ' s i m i l a r i t y _ s t a t i s t i c s ' ] . g e t ( ' h i g h _ s i m i l a r i t y _ p a i r s ' ,   ' N / A ' ) } " ) 
 
 
 
         e x c e p t   E x c e p t i o n   a s   e : 
 
                 p r i n t ( f " R�gǏz-N�Q�:   { e } " ) 
 
                 i m p o r t   t r a c e b a c k 
 
                 t r a c e b a c k . p r i n t _ e x c ( ) 
 
 
 
 i f   _ _ n a m e _ _   = =   " _ _ m a i n _ _ " : 
 
         m a i n ( ) 