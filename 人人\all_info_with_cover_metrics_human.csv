id,Group,Date_Group,Role,发散得分(allcount),发散得分（nowater）,创造力得分,创造力总分,extra,open,agree,consci,neuro,engasub2,engasub1,exchange,imagine,grit,curious,reflex,regulate,moniter,plan,sef,sys,ana,sdr,engacog2,engaemo2,engabeh2,engacog1,engaemo1,engabeh1,respon,conrel,conbeh,tsrel1,stra1,ethic1,thk1,tsrel,sta,ethic,thk,group,course,role,roleid,formid,taskid,firsttask,ptype,SE,SEC,CSR1,CSR2,CSR3,CSR4,CSR5,CSR6,ESR1,ESR2,ESR3,ESR4,ESR5,HOT1,HOT2,HOT3,HOT4,HOT5,HOT6,CCR1,CCR2,CCR3,CCR4,CCR5,ECR1,ECR2,ECR3,ECR4,ECR5,CCSR1,CCSR2,CCSR3,CCSR4,CCSR5,ECSR1,ECSR2,ECSR3,ECSR4,ECSR5,CPS1,CPS2,CPS3,CPS4,EPS1,EPS2,EPS3,EPS4,CLIL1,CLIL2,CLGL1,CLEL1,CLGL2,CLEL2,CLEL3,CLGL3,CLEL4,CLIL3,MF,ES1,ES2,SAM_Valence,SAM_Arousal,SAM_Dominance,Connection,SE_mean,SEC_mean,CSR_mean,ESR_mean,CCR_mean,ECR_mean,CCSR_mean,ECSR_mean,HOT_mean,CPS_mean,EPS_mean,CLIL_mean,CLGL_mean,CLEL_mean,SAM_Valence_mean,SAM_Arousal_mean,SAM_Dominance_mean,Connection_mean,TaskResponsiveness,Technicaltheorysupport,Innovation,Divergence,Problemsolvingskills,age,gender,mas11,test1,mcj1,test2,mcj2,test3,mcj3,test4,mcj4,test5,mcj5,test6,mcj6,test7,mcj7,test8,mcj8,test9,mcj9,test10,mcj10,test11,mcj11,test12,mcj12,mas12,beh11,beh12,beh13,emo11,emo12,emo13,cog11,cog12,cog13,cog14,cog15,hard1,sub11,sub12,sub13,mas21,test13,mcj13,test14,mcj14,test15,mcj15,test16,mcj16,test17,mcj17,test18,mcj18,test19,mcj19,test20,mcj20,test21,mcj21,test22,mcj22,mas22,beh21,beh22,beh23,emo21,emo22,emo23,cog21,cog22,cog23,cog24,cog25,hard2,sub21,sub22,sub23,pretest,score_mean,bias,score_mean_withoutbias,bias_withoutbias,TaskResponsiveness_adjusted,Technicaltheorysupport_adjusted,Innovation_adjusted,Divergence_adjusted,Problemsolvingskills_adjusted,ab_monitoring,人名,说话人编号,ans_content,bleu,rouge_rouge1,rouge_rouge2,rouge_rougeL,meteor,tfidf_cosine,bertscore_f1,lcs_ratio,edit_distance_similarity
101002,1,1001,协调员,5.0,1.0,3.0,3.0,4.0,4.0,2.6666666666666665,3.333333333333333,6.0,2.6666666666666665,3.0,4.0,4.333333333,4.0,4.333333333,3.433333333,3.6,3.6,3.6,3.1,3.5,3.7,0.375,3.8,3.333333333,2.333333333,3.0,3.333333333,2.0,3.5,3.666666667,3.5,3.2,3.0,3.6,4.2,16,12,18,21,1,A,1,2,0,0,1,1,4,9,3,1,4,4,4,4,3,5,5,5,5,4,5,5,5,5,4,5,5,5,5,2,5,5,5,5,5,3,5,5,5,5,5,5,5,5,5,5,5,3,5,5,3,5,5,4,5,5,1,5,5,3,5,1,2,3,5,5,3,3,4,2,7.125,9,3.333333333,4.6,4.4,5.0,4.6,5.0,4.666666667,4.5,4.5,3.666666667,5.0,2.5,3,3,4,2,6.0,4.5,5.5,5.5,5.0,19,0,3.0,0,2,1,1,0,2,1,1,0,1,1,1,0,1,0,2,0,2,0,2,1,2,0,2,3,3,2,1,2,4,4,2,4,3,4,2,3,3,4,2,3,1,1,0,2,0,1,0,1,0,1,1,1,0,1,0,2,0,1,1,1,2,2,3,2,3,3,4,4,3,4,4,4,3,4,2,2,0.318181818,5.3,-1.3,6.1855,-2.1855,6.827,5.805,6.238,6.331,5.725,2.1855,郑钰娜,2.0,"我认为阿塞拜疆语是一门很适应ai来学习的。首先，作为语言来说，阿塞拜疆语有着庞大的数据库，利于导入数据。同时阿塞拜疆语这两年，随着一带一路政策的深入，对这个语言的需求也在增大。但是因为这个语言相对冷门，如果要专门学习，需要大量精力，所以可以通过ai开发精确的翻译器，帮助准确的交流。同时因为数据库可以随时更新，阿塞拜疆语的也可以随时加入新的阿塞拜疆语的用法，便于创新。社会应用来说，也有助于一带一路活动的开展，便于两国人民交流，增加阿塞拜疆语的运用范围。
ai的举一反三能力也有助于精确理解这个语言，避免出现翻译错误",2.0491779584668015e-11,0.04477611940298508,0.030303030303030307,0.04477611940298508,0.013573000733675714,0.3125093916430612,0.11220753937959671,0.38461538461538464,0.017590441420511138
101011,1,1001,启发员,8.0,6.0,5.0,5.0,1.0,1.0,4.333333333333333,4.0,1.0,1.0,1.6666666666666667,5.0,5.0,5.333333333,6.0,3.140740741,4.844444444,4.066666667,2.4,5.0,4.8,5.6,0.625,5.0,4.0,3.333333333,4.2,3.666666667,3.0,5.0,4.333333333,4.5,4.0,3.5,3.6,5.0,20,14,18,25,1,A,1,1,0,0,1,1,7,5,5,5,5,5,3,1,4,3,5,4,4,5,5,5,5,5,4,3,4,4,2,5,3,3,3,3,3,3,3,4,5,5,3,3,3,5,5,4,4,4,4,4,4,3,3,1,1,5,1,4,1,1,4,1,1,1,4,4,4,2,4,3,5.75,5,4.0,4.0,3.6,3.0,4.0,3.8,4.833333333,4.0,3.5,1.0,4.333333333,1.0,4,2,4,3,6.0,4.5,5.0,5.5,5.5,24,1,10.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,10,3,3,3,2,4,5,4,5,4,4,4,1,3,1,1,10,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,10,3,3,4,4,4,4,5,5,5,5,5,1,1,1,1,0.818181818,5.3,1.7,6.1855,0.8145,6.827,5.805,5.738,6.331,6.225,0.8145,徐海龙,1.0,"对于油气储存运输行业，在几十年的发展中，已经积累了大量基于机理的知识模式与基于经验的行业知识。这类知识中包括大量对前瞻研究的启发性较弱、但对从业人员必须了解的知识，通过大模型的使用，将海量的知识数据（包括书籍、照片（如损伤识别）、老员工口述心得）融合进人工智能中，能够加快该领域学习者的学习效率，并能更快的跟进科研一线，了解网络难以查询到的知识。
此外，通过大模型的思维链去完善链式操作或操作逻辑，能够对复杂的实验过程或工艺操作过程进行实时监督，在新人进行操作时，能够更安全、高效得完成实验。
能源保障工作一直是国家重大要事，通过融合人工智能可以为能源行业进一步发展提供便携。",1.0123515125497294e-08,0.0,0.0,0.0,0.01766014040901757,0.2636115497085077,0.09448418766260147,0.313953488372093,0.020956899960458686
101012,1,1001,记录员,5.0,6.0,7.0,7.0,2.333333333333333,3.0,3.0,2.6666666666666665,4.333333333333333,2.0,2.333333333333333,4.0,2.666666667,3.0,3.666666667,2.576851852,3.461111111,2.766666667,2.6,3.0,3.4,3.3,0.125,3.0,3.666666667,3.333333333,3.4,4.0,2.333333333,4.0,3.666666667,4.0,3.0,3.0,3.0,3.0,15,12,15,15,1,A,1,3,0,0,1,1,8,8,3,3,4,4,4,4,4,4,4,4,4,4,3,3,3,4,3,4,2,3,2,2,2,2,2,4,3,3,3,3,3,3,3,3,3,3,3,4,3,2,3,3,3,3,3,3,4,4,4,4,4,4,4,4,3,3,4,4,3,2,4,2,8.0,8,3.666666667,4.0,2.6,2.6,3.0,3.0,3.333333333,3.0,3.0,3.333333333,4.0,4.0,3,2,4,2,6.0,6.5,5.5,6.0,6.5,20,1,6.0,1,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,1,1,1,1,1,1,0,2,6,3,2,2,4,4,4,4,3,4,4,2,3,3,2,2,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,6,3,4,3,4,4,3,3,3,3,3,3,3,2,2,2,0.818181818,6.1,1.9,6.9855,1.0145,6.827,7.805,6.238,6.831,7.225,1.0145,卢瑞涛,3.0,"考古学作为一个冷门学科，其专业知识有着大众认知广而认知浅，其学习需要消耗大量时间的特点。而古代遗留的物件，文字均遭受不同程度的磨损，可能难以识别。
而AI技术有助于考古学的复兴，主要集中于以下方面：AI的数据链技术可以呈现AI的思考过程，通过一步一步揭示文物，文字的特征反映出的信息，从而清晰可视地呈现出对于古文物的辨认过程，不仅有助于专业工作者的工作，也有利于大众对于古代文化的学习；AI的多模态技术通过图生文，文/图生视频等方式，不仅可以让文物”活起来“，还可以还原重大历史事件，有助于专业研究与文化传承；同时，利用AI获取与学习大量文物，考古学知识的能力，可以实现”AI考古学老师”授课，有效提高考古学教育水平。",2.706027652594524e-07,0.12121212121212122,0.10309278350515463,0.12121212121212122,0.025033618507716862,0.3087247108718657,0.25522875785827637,0.4606741573033708,0.03128621089223638
101005,2,1002,启发员,2.0,3.0,2.0,2.0,1.3333333333333333,2.0,5.666666666666667,2.0,2.0,4.0,3.6666666666666665,5.0,4.0,2.333333333,3.666666667,2.575925926,3.455555556,2.733333333,2.4,3.0,2.4,4.1,0.25,4.0,4.0,3.333333333,4.0,4.0,3.666666667,4.0,4.0,4.0,4.0,4.0,4.6,4.4,20,16,23,22,2,A,1,1,0,0,1,1,9,8,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,1,1,5,5,3,5,5,5,5,1,5,5,8.375,8,4.0,4.0,3.8,4.0,5.0,5.0,4.0,5.0,5.0,4.333333333,5.0,3.0,5,1,5,5,4.5,4.0,5.0,4.5,5.5,25,1,7.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,0,2,0,1,7,4,3,4,4,4,4,4,4,4,4,4,2,4,4,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,0,1,7,4,2,4,4,4,4,4,4,4,4,4,3,4,4,4,0.681818182,4.7,4.3,5.5855,3.4145,5.327,5.305,5.738,5.331,6.225,3.4145,赵昊博,2.0,"人工智能在石油与天然气工程领域的应用：
相较于其他工科领域，石油与天然气领域更为专业化、小众化，石油行业也是夕阳企业的代表，而人工智能在石油与天然气领域的应用，更偏向于对工程的仿真控制以及预测方面，石油与天然气行业身为老牌工科行业，积累了大量的运行数据，而这些数据的利用率却很低，可以通过人工智能的学习能力，将大量的运行数据运用起来，一是对现场工艺的仿真预测，现场由于其危险性，许多危险工况难以模拟，可以通过人工智能建立数据驱模型，对其他工况进行模拟，也可以将仿真预测结果输出至现场，提供现场控制方案，更可以总结事故发生规律，对高危区域进行预警。",5.340783583817161e-07,0.0,0.0,0.0,0.02471169686985173,0.3521122719430748,0.1452636867761612,0.3291139240506329,0.02937788018433185
101007,2,1002,记录员,3.0,4.0,3.0,4.0,5.0,4.0,5.333333333333333,4.0,2.0,4.333333333333333,4.0,5.0,4.333333333,5.0,5.333333333,3.731481481,3.388888889,3.333333333,4.0,3.6,4.2,3.9,0.5,4.2,4.666666667,4.0,4.2,3.666666667,3.666666667,4.0,4.0,4.25,4.2,4.25,4.0,4.4,21,17,20,22,2,A,1,3,0,0,1,1,8,9,4,5,4,4,4,5,4,4,4,4,3,4,4,4,3,4,4,4,4,4,4,3,4,4,4,4,4,5,4,4,4,5,4,4,4,4,4,4,4,4,4,4,5,3,4,5,3,4,4,4,3,4,3,5,3,5,4,4,4,2,3,6,8.625,9,4.333333333,3.8,3.8,4.0,4.4,4.0,3.833333333,4.0,4.0,3.666666667,3.666666667,4.0,4,2,3,6,3.5,2.5,4.0,4.0,4.5,29,1,7.0,1,1,1,1,1,1,0,2,1,2,1,1,0,1,0,1,1,1,0,1,0,2,1,1,6,4,3,4,4,4,3,4,4,4,4,5,3,4,5,3,7,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,2,0,2,1,1,7,4,4,4,5,5,4,4,4,5,4,4,3,5,4,4,0.727272727,3.7,4.3,4.5855,3.4145,4.327,3.805,4.738,4.831,5.225,3.4145,范志涵,3.0,"人工智能石油天然气领域的应用
铺设石油天然气管路时的管线路径规划和站场控制可以应用人工智能技术。
1 在铺设天然气管路时，人工智能可以辅助或者替代人根据地形，土质，人口密集程度进行管路设计；
2 石油天然气管路的站场经常涉及到很多控制阀的操作，这非常依赖人的经验。人工智能可以辅助设计控制阀的开关顺序和开关程度。",6.703711663253124e-08,0.06451612903225806,0.0,0.06451612903225806,0.01810353263528721,0.33802272959364776,0.09855112433433533,0.3411764705882353,0.025754884547069312
101016,2,1002,协调员,4.0,8.0,3.0,3.0,3.0,2.6666666666666665,3.0,3.0,3.0,3.6666666666666665,4.0,3.0,4.0,3.0,3.0,3.887037037,3.322222222,3.933333333,3.6,3.0,3.3,3.6,0.375,4.0,3.666666667,3.666666667,4.0,4.0,3.666666667,3.0,3.666666667,3.75,4.0,4.0,3.8,3.8,20,16,19,19,2,A,1,2,0,0,1,1,5,4,3,4,4,4,4,4,4,4,4,4,3,3,4,4,4,4,4,4,2,4,4,4,4,4,4,3,3,4,4,4,4,4,4,4,4,4,4,5,3,5,5,3,3,4,4,5,5,3,2,3,4,4,4,2,4,3,3,3,4,2,2,2,4.375,4,3.833333333,3.8,3.6,3.6,4.0,4.0,3.833333333,4.5,3.5,4.666666667,3.333333333,3.0,4,2,2,2,5.0,5.5,4.5,4.5,5.0,24,0,6.0,0,1,0,1,1,1,1,1,0,2,1,1,0,1,0,1,1,1,0,1,0,1,1,1,5,4,3,4,4,4,4,4,4,4,4,4,3,4,4,4,5,1,1,0,1,1,1,0,1,1,1,1,1,1,1,0,1,0,1,1,1,3,4,3,4,4,4,3,4,4,4,4,4,3,4,4,3,0.5,4.9,0.1,5.7855,-0.7855,5.827,6.805,5.238,5.331,5.725,0.7855,魏金霞,1.0,"人工智能在石油与天然气领域的应用
中心问题：围绕人工智能在石油与天然气的战场选择与控制方面的应用讨论
背景：石油与天然气领域中实地勘测选址的难度和效率受到人力效率的限制，对以往数据的使用没有达到充分水平，对现有数据的收集也面临低效问题。人工智能的介入能够将数据库中的数据整理分析，得出相对人工更快、较为多样的相对优选答案。人工智能的介入主要体现以下几个特点的应用：
1.思维链：人工智能的数据分析产出是一个输入输出的过程，不断优化思维链，可以在一个相对完善的基础上得到提升，更省力。
2.举一反三：人工智能数据库可以提供同类方案的解决方法做借鉴，以见面同类问题再发生时带来的重复性劳动。",,,,,,,,,
101010,3,1003,协调员,3.0,5.0,4.0,4.0,3.0,5.333333333333333,5.0,4.0,2.6666666666666665,3.0,3.0,6.0,5.666666667,5.333333333,5.0,3.987037037,3.922222222,3.533333333,3.2,5.2,4.2,4.5,0.25,4.2,5.0,4.666666667,4.2,5.0,4.666666667,4.0,4.666666667,3.75,3.4,4.0,4.2,3.6,17,16,21,18,3,A,1,2,0,0,1,1,7,4,4,3,4,4,4,2,4,4,4,4,4,5,4,4,5,5,4,4,4,4,4,5,4,4,4,4,4,5,4,5,5,5,5,4,4,4,5,4,4,4,4,4,4,4,4,4,2,4,3,3,2,2,4,3,4,3,4,4,4,3,3,4,5.125,4,3.5,4.0,4.2,4.0,4.8,4.4,4.5,4.0,4.0,3.333333333,3.666666667,2.5,4,3,3,4,6.0,4.5,5.0,5.0,6.0,22,0,8.0,0,1,1,1,1,1,1,1,0,2,1,1,1,2,1,1,1,1,1,2,1,2,1,1,8,5,5,4,5,5,5,4,4,4,4,5,2,3,3,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,1,1,8,5,5,4,5,5,5,4,4,4,4,5,2,3,3,3,0.863636364,5.3,1.7,6.1855,0.8145,6.827,5.805,5.738,5.831,6.725,0.8145,陈楚昕,1.0,"冷门绝学：文物修复
AI的帮助：
识别裂痕，AI提供修复指导（图片点对点，修复措施）
挑战：
1.需要更精密的修复手法，机械可能无法提供（解决方案：）
2.过于冷门，训练集不够，AI学习不足（解决方案：完整记录专家修复过程）
如何利用AI技术助力该学科的传承、创新与社会应用： 
1.记录技法，生成图片、视频用于教学。
2.自动生成修复方案，为专业人士提供修复方案参考，同时指导初学者学习
以上都有助于文物修复的传承与发展。",4.2902230249285683e-10,0.22535211267605632,0.14492753623188404,0.22535211267605632,0.016576513697435003,0.42893607051474414,0.14450038969516754,0.4094488188976378,0.020529016975917913
101013,3,1003,启发员,4.0,7.0,4.0,4.0,4.0,4.666666666666667,3.333333333333333,4.333333333333333,4.0,4.0,3.333333333333333,5.0,5.0,5.0,5.0,4.0,4.0,4.0,4.0,4.8,4.4,4.7,0.125,4.0,4.0,4.0,3.8,4.0,3.666666667,4.0,4.0,4.0,3.6,4.0,4.0,4.0,18,16,20,20,3,A,1,1,0,0,1,1,8,8,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,4,4,3,3,3,3,3,4,4,2,3,3,4,3,3,3,4,3,3,4,3,8.0,8,4.0,4.0,4.0,4.0,4.0,4.0,3.833333333,3.5,3.5,3.0,3.333333333,3.25,3,3,4,3,5.5,4.5,5.0,4.5,5.5,21,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,8,4,4,3,4,4,4,4,4,4,4,3,2,4,3,3,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,8,4,4,4,4,4,4,4,4,4,4,4,2,4,4,4,0.772727273,5.0,3.0,5.8855,2.1145,6.327,5.805,5.738,5.331,6.225,2.1145,李运宏,2.0,"学科选择：文物修复
问题：请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
Ans:
其在人工智能时代可能焕发新生命力的可能原因在于数据库样例的丰富，即用AI技术将大多数的修复方案保存下来，为文物修复提供更多的参考依据和模板，在一个就是对于修复提供多种修复方式，使得人们能够了解到更好、更多的修复措施来实现文物更好的还原。
存在的挑战包括
需要更精密的修复手法，机械可能无法提供
过于冷门，训练集不够，ai学习不足（解决：完整记录专家修复过程）
最后一问：自动生成修复方案。比如：记录技法，生成图片、视频用于教学，提供事例，面向初学者帮助更大。",1.548032416972904e-05,0.09523809523809525,0.03278688524590164,0.09523809523809525,0.036300719229203415,0.278826897709944,0.16142500936985016,0.2895927601809955,0.03781773093614382
101021,3,1003,记录员,7.0,11.0,2.0,2.0,2.6666666666666665,2.0,4.0,2.333333333333333,3.6666666666666665,1.0,2.0,3.333333333,3.666666667,2.666666667,2.333333333,2.817592593,3.905555556,3.433333333,2.6,2.2,3.4,3.8,0.25,3.2,2.666666667,2.333333333,3.4,3.0,2.333333333,4.0,3.666666667,1.25,2.4,2.0,2.6,2.6,12,8,13,13,3,A,1,3,0,0,1,1,3,5,3,2,3,4,4,3,3,4,3,3,3,2,2,4,3,3,2,2,2,3,2,2,2,2,2,2,2,4,4,4,4,4,2,2,2,3,4,2,3,3,3,3,3,2,2,3,3,4,4,3,4,4,4,2,2,3,2,3,3,3,2,1,4.25,5,3.166666667,3.2,2.2,2.0,4.0,2.6,2.666666667,2.75,2.5,2.666666667,3.666666667,3.5,3,3,2,1,5.5,4.5,5.0,5.0,6.0,21,1,4.0,0,2,1,2,1,2,1,1,1,2,1,2,0,2,1,2,0,2,0,2,0,2,1,2,3,3,2,2,3,3,3,2,4,4,4,3,3,2,2,2,3,0,2,1,2,1,2,1,2,0,2,1,2,1,2,0,2,0,2,1,2,3,3,2,2,3,3,2,3,3,3,4,3,4,1,1,1,0.590909091,5.2,-2.2,6.0855,-3.0855,6.327,5.805,5.738,5.831,6.725,3.0855,颜道明,3.0,"学科：文物修复
ai的帮助：
拍下文物的裂痕，ai提供修复指导，利用图片点对点的方式，提供修复措施
挑战：
需要更精密的修复手法，机械可能无法提供
过于冷门，训练集不够，ai学习不足（解决方法：完整记录专家修复过程）
ai助力：自动生成修复方案。比如：记录技法，生成图片、视频用于教学，提供事例，面向初学者。",1.3344733570816925e-08,0.16,0.125,0.16,0.019348597226701063,0.3744356633753483,0.11852037906646729,0.3695652173913043,0.02412280701754388
101000,4,1004,记录员,10.0,0.0,4.0,5.0,4.333333333333333,2.0,5.0,4.666666666666667,4.666666666666667,2.0,2.6666666666666665,4.0,4.666666667,3.0,4.333333333,3.462037037,3.772222222,3.633333333,3.8,4.3,3.5,4.6,0.375,3.8,4.333333333,4.333333333,4.0,3.666666667,4.0,3.5,3.0,3.75,3.8,4.25,3.4,3.6,19,17,17,18,4,A,1,3,0,0,1,1,8,7,4,4,4,4,4,4,3,3,4,3,4,3,4,4,4,4,4,4,4,4,4,4,3,3,3,4,3,4,4,4,4,4,3,3,3,3,3,4,4,4,3,2,2,4,2,3,2,3,2,4,3,2,4,2,2,2,4,4,4,1,2,3,7.375,7,4.0,3.4,4.0,3.2,4.0,3.0,3.833333333,3.75,2.5,2.333333333,3.666666667,2.25,4,1,2,3,3.5,3.5,5.0,6.0,5.5,21,0,8.0,1,1,1,1,1,1,1,2,1,2,1,1,1,1,0,1,1,1,0,2,0,1,1,1,6,4,5,3,4,3,4,4,4,4,4,4,3,2,2,4,8,0,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,4,5,4,4,4,5,4,4,4,4,3,2,2,2,2,0.681818182,4.7,3.3,5.5855,2.4145,4.327,4.805,5.738,6.831,6.225,2.4145,张菲煊,2.0,"冷门：古代传统（研究敦煌，埃及、甲骨文等研究古代文化的，极需传承的，国学范畴的学科），少数民族领域
选择的冷门学科：考古学（敦煌学）
发展挑战：
考古条件艰苦（环境条件艰苦）；
经验强相关性，较依赖过去经验，成功成本高；
需要传承，在人才培养方面遇到困难；
有很大的文本工作量，需要查阅的资料庞杂；
针对有无自成的知识体系有争议，研究成果在学术界交叉大
学科交叉复杂，涉及的学科多，研究方向多样，文物种类多
ai在其中助力的地方：
ai本身对环境要求低，基本只对网络和电源有要求
大模型可以解决学科交叉问题，可以更广泛的获取信息
能够快速阅读文本并从以前的资料中提取有用信息，有记忆效应
Ai面对的挑战
学科本身具有文学学科性质，ai可能难以解读其中的情感倾向
在环境艰苦的地方可能没有网络信号
学科本身对经验依赖强，Ai能够阅读的信息和资料可能比较少，且难以判断其真实性",9.900485278419935e-10,0.05319148936170212,0.043010752688172046,0.05319148936170212,0.019657399606852005,0.34231374356206806,0.1787121593952179,0.3463203463203463,0.019842279318239675
101020,4,1004,协调员,7.0,10.0,4.0,5.0,5.0,5.0,5.333333333333333,3.6666666666666665,2.333333333333333,2.0,2.333333333333333,5.0,5.0,5.0,5.0,4.096296296,4.577777778,4.466666667,2.8,4.7,4.6,4.6,0.25,4.0,4.0,2.0,4.2,4.0,2.333333333,4.0,3.0,2.0,4.0,2.75,5.0,4.0,20,11,25,20,4,A,1,2,0,0,1,1,8,7,4,3,4,4,4,4,4,4,4,4,4,4,5,5,5,5,4,5,1,1,5,4,4,4,4,5,5,4,4,4,4,4,4,5,5,5,4,5,5,5,5,4,4,4,4,2,2,4,1,4,1,1,4,1,2,3,4,4,4,3,3,6,7.375,7,3.833333333,4.0,3.2,4.4,4.0,4.6,4.666666667,5.0,4.0,2.0,4.0,1.0,4,3,3,6,6.0,4.0,5.0,6.5,6.0,17,1,6.0,1,1,1,1,1,1,1,1,0,1,1,1,0,1,0,2,0,1,1,1,0,2,0,1,7,3,2,2,4,4,4,5,3,5,3,5,2,4,1,2,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,7,2,2,2,4,4,4,4,4,4,4,4,2,2,2,2,0.636363636,5.5,2.5,6.3855,1.6145,6.827,5.305,5.738,7.331,6.725,1.6145,王俊杰,3.0,"冷门：古代传统（研究敦煌，埃及、甲骨文等研究古代文化的，极需传承的，国学范畴的学科），少数民族领域
冷门学科：考古学（敦煌学）
发展挑战：
考古条件艰苦（环境条件艰苦）；
经验强相关性，较依赖过去经验，成功成本高；
需要传承，在人才培养方面遇到困难；
有很大的文本工作量，需要查阅的资料庞杂；
针对有无自成的知识体系有争议，研究成果在学术界交叉大
学科交叉复杂，涉及的学科多，研究方向多样，文物种类多
ai在其中助力的地方：
ai本身对环境要求低，基本只对网络和电源有要求
大模型可以解决复杂的学科交叉问题
ai处理长文本能力强于普通人，效率更高
ai自身的记忆能力能够帮助解决传承问题，有更好的记忆力
面对的挑战
ai在处理文本与画面内容的时候可能处理的没有人类精确，无法分析作品包含的情感
过于艰苦的环境可能无法使ai运行（比如断网断电，沙尘无法扫描）
ai可能的幻觉现象导致研究不准确",9.81245877316017e-13,0.057851239669421496,0.05,0.057851239669421496,0.013383982522093413,0.30667395872375713,0.06447794288396835,0.3474576271186441,0.014593344011389897
101023,4,1004,启发员,6.0,12.0,3.0,3.0,3.333333333333333,3.6666666666666665,3.6666666666666665,3.6666666666666665,2.6666666666666665,3.0,3.333333333333333,3.666666667,4.333333333,4.0,4.333333333,3.750925926,3.505555556,3.033333333,3.2,2.9,3.9,3.7,0.0,3.2,4.0,3.666666667,3.6,3.333333333,3.0,4.0,4.0,3.5,5.0,5.0,5.0,5.0,25,20,25,25,4,A,1,1,0,0,1,1,7,4,3,3,4,4,4,4,4,4,4,4,4,3,3,3,4,4,3,4,3,3,3,4,4,3,4,3,3,4,4,4,4,3,4,3,4,4,3,4,4,4,3,3,3,4,4,4,4,4,2,4,2,3,3,2,3,3,4,4,3,3,2,3,5.125,4,3.666666667,4.0,3.4,3.4,3.8,3.6,3.333333333,3.75,3.5,3.666666667,3.666666667,2.25,3,3,2,3,3.5,3.5,5.0,6.0,5.5,19,0,4.0,1,1,0,2,1,1,1,1,1,2,0,1,0,1,1,1,0,2,1,2,0,2,0,2,3,4,3,2,3,3,4,4,4,3,3,4,3,4,3,3,5,1,2,1,1,0,2,0,1,1,1,1,1,0,1,1,2,1,2,0,1,5,4,3,4,4,4,4,3,3,3,3,4,3,3,3,3,0.545454545,4.7,2.3,5.5855,1.4145,4.327,4.805,5.738,6.831,6.225,1.4145,李韦伊,1.0,"什么算冷门学科
冷门学科的发展挑战
为什么ai可以助力这个学科，人工智能在其中扮演什么样的角色
冷门：古代传统（研究敦煌，埃及、甲骨文等研究古代文化的，极需传承的，国学范畴的学科），少数民族领域
冷门学科：考古学（敦煌学）
发展挑战：
考古条件艰苦（环境条件艰苦）；
经验强相关性，较依赖过去经验，成功成本高；
需要传承，在人才培养方面遇到困难；
有很大的文本工作量，需要查阅的资料庞杂；
针对有无自成的知识体系有争议，研究成果在学术界交叉大
学科交叉复杂，涉及的学科多，研究方向多样，文物种类多
ai在其中助力的地方：
ai本身对环境要求低，基本只对网络和电源有要求
大模型可以解决学科交叉问题
能快速阅读文本并从以前的资料中提供有用信息
面对的挑战
ai难以解读学术研究其中的情感倾向
环境艰苦的地方可能存在网络的电源
AI能够阅读的信息以及前任资料比较少",5.926395462630066e-08,0.0847457627118644,0.0689655172413793,0.0847457627118644,0.024426266757555104,0.40511806011684254,0.19221997261047363,0.36,0.027500859401856315
102003,5,1005,协调员,6.0,990.0,4.0,4.0,4.0,2.333333333333333,5.0,4.666666666666667,4.0,4.0,3.333333333333333,3.666666667,3.666666667,5.0,4.0,3.640740741,3.844444444,4.066666667,3.4,3.6,4.4,4.5,0.75,4.4,5.0,4.0,4.0,4.0,3.666666667,4.5,4.666666667,3.0,3.4,3.5,4.6,4.4,17,14,23,22,5,A,1,2,0,1,1,1,7,7,4,3,4,4,4,4,3,4,4,3,4,4,4,3,4,4,4,4,4,4,4,4,3,4,4,4,4,5,4,4,5,3,3,4,4,4,5,4,4,3,4,4,4,4,3,5,5,4,2,4,2,2,4,3,5,4,4,4,4,3,3,6,7.0,7,3.833333333,3.6,4.0,3.8,4.2,4.0,3.833333333,3.75,3.75,5.0,4.0,2.25,4,3,3,6,7.0,7.5,6.0,6.5,7.5,22,0,8.0,1,1,1,1,1,1,1,2,1,1,1,1,0,2,0,1,1,1,1,2,1,1,1,1,8,4,4,3,4,4,4,4,3,4,4,5,3,3,4,3,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,9,4,4,4,5,5,5,4,4,5,4,5,2,4,4,4,0.818181818,6.9,0.1,6.9,0.1,7.0,7.5,6.0,6.5,7.5,0.1,刘一诺,2.0,"一、恐怖分子可能在哪些领域或技术层面带来重大安全风险
1、经济领域：侵入支付系统、记账系统等，造成经济领域支付结算系统的瘫痪；
2、信息安全领域：造成个人隐私数据的泄露，引起群众恐慌；
3、交通领域：侵入天眼、卫星等重要交通系统，造成交通事故等；
4、国防安全领域：造成国家机密泄露，引发地缘政治风险等；
5、文化教育领域：散播谣言与虚假信息。
二、面对这些风险，有效的应对方案
1、不断加强AI对齐技术方面的研究，完善AI立规则等技术
2、将以往的相关案例资料输入给AI，使AI不断学习新案例，始终保持较高的防御和应对水平。
3、完善监测技术，及时监测，完善相关纠察机制，及时补漏洞
4、预防机制，提前多设想不同的可能性，针对重点领域多设防火墙。
三、AI安全防御技术的未来发展
AI对齐技术始终走在AI技术前沿，安全性不断提高。",1.2850244663176348e-10,0.20800000000000002,0.16260162601626016,0.20800000000000002,0.017445157391571483,0.24645297262727828,0.14797423779964447,0.3870967741935484,0.018560953253895507
102005,5,1005,记录员,6.0,991.0,5.0,5.0,3.0,6.0,5.0,6.0,4.666666666666667,4.333333333333333,4.0,6.0,5.333333333,5.666666667,6.0,4.298148148,3.788888889,3.733333333,4.4,4.4,4.3,5.0,0.375,4.2,4.666666667,3.333333333,4.2,5.0,3.333333333,4.0,4.666666667,5.0,3.6,4.75,3.8,4.8,18,19,19,24,5,A,1,3,0,1,1,1,7,10,4,5,4,5,5,5,4,5,5,3,4,5,5,5,5,3,4,5,5,5,4,5,5,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,4,5,5,4,4,4,4,4,5,2,5,2,2,5,2,3,4,5,5,5,4,4,6,8.875,10,4.666666667,4.2,4.8,4.4,5.0,5.0,4.5,4.75,4.25,3.666666667,5.0,2.0,5,4,4,6,7.0,7.5,6.0,6.5,7.5,20,0,6.0,0,1,1,1,1,1,1,1,0,2,1,1,0,2,0,1,0,2,1,2,1,1,0,2,5,4,3,3,5,5,5,5,4,4,4,4,4,5,3,4,7,1,1,1,1,0,1,0,1,0,2,1,1,0,2,1,2,0,1,1,1,4,4,3,3,4,5,5,4,4,4,4,5,4,5,4,4,0.5,6.9,0.1,6.9,0.1,7.0,7.5,6.0,6.5,7.5,0.1,田甜,3.0,"1.经济领域侵入账户系统造成财产风险
2.信息安全领域造成个人隐私数据泄露
3.交通领域侵入导航系统等引发交通事故等
4.国防安全领域造成国家机密泄露
5.文化教育领域造成不良文化入侵
1.超级对齐技术，为AI立法
2.及时监测寻找漏洞查漏补缺 
3.预防机制，提前多设想一些可能性，不断加强防御 
4.对于重要机密领域多设置保护机制 
5.在以往相关案例中进行整合分析寻找根本问题所在，以适应突发的新问题
通过科学正确的引导，使AI对齐技术走在AI发展前列",6.447782420562564e-16,0.16666666666666669,0.11320754716981131,0.16666666666666669,0.008608948166422532,0.23882024016784503,0.10234931856393814,0.4090909090909091,0.01253190995590625
102023,5,1005,启发员,6.0,1000.0,3.0,3.0,3.6666666666666665,3.333333333333333,5.333333333333333,5.0,4.666666666666667,3.333333333333333,3.6666666666666665,3.666666667,4.333333333,5.0,4.333333333,4.055555556,4.333333333,4.0,4.0,3.6,3.9,4.1,0.125,3.8,4.0,3.666666667,3.6,4.0,3.666666667,5.0,4.666666667,4.5,4.8,5.0,4.8,4.2,24,20,24,21,5,A,1,1,0,1,1,1,6,6,4,3,4,4,4,4,4,5,5,4,5,4,4,4,4,3,5,4,4,3,3,5,4,4,5,5,5,4,4,4,4,4,4,4,4,5,4,4,5,4,4,5,5,3,4,4,4,4,3,5,5,2,4,2,3,4,5,5,4,3,2,3,6.0,6,3.833333333,4.6,3.8,4.6,4.0,4.2,4.0,4.25,4.25,3.666666667,4.333333333,3.0,4,3,2,3,6.5,7.5,6.0,6.5,7.0,19,0,6.0,0,2,1,1,1,1,0,1,1,2,0,1,0,1,1,1,1,2,0,2,0,1,1,1,4,4,4,3,4,4,4,3,4,4,3,4,4,4,3,4,7,1,1,1,1,1,1,1,2,0,2,1,1,0,1,0,2,0,1,0,1,5,4,4,3,4,4,4,4,4,4,3,4,4,4,3,3,0.5,6.7,-0.7,6.7,-0.7,6.5,7.5,6.0,6.5,7.0,0.7,胡瑞祺,1.0,"金融领域：对国民经济的账户、银行内部的账号等进行入侵、
国防安全领域：国家机密的泄露，突破国家安全防线、
交通安全：使国家交通瘫痪、影响正常的交通，对国家交通安全造成威胁
个人信息安全领域：个人信息被窃取、泄露
预防：多设想可能性，对于一些极其重要的领域加强保护措施、加强对于技术层面的检查频率
如何应对：1、加强对齐技术、使AI对齐技术始终走在前沿，持续对安全领域的核心技术进行加强、
2、在以往的相关AI破坏事例进行究因分析，对根本性问题进行探究和对措分析
利用AI的设计机理设计出相应措施
3、给AI多加规则、使AI的安全性更强
AI防御技术会不断完善、安全会得到保障，人们对AI的放心程度更大",1.4693635249906079e-06,0.2857142857142857,0.1639344262295082,0.2857142857142857,0.025963463463463465,0.3665514722253846,0.10278313606977463,0.3508771929824561,0.03357996585088219
102001,6,1006,记录员,6.0,989.0,5.0,5.0,4.333333333333333,2.333333333333333,4.0,4.666666666666667,4.0,3.0,2.6666666666666665,4.0,4.333333333,4.0,4.0,3.827777778,3.966666667,3.8,2.8,3.5,3.3,4.2,0.0,3.8,4.0,3.666666667,3.8,3.666666667,3.0,4.5,4.0,4.75,4.2,3.25,3.8,3.6,21,13,19,18,6,A,1,3,0,1,1,1,4,7,2,3,3,2,4,4,4,3,4,3,4,4,4,4,4,4,3,4,4,4,3,4,2,2,3,4,2,4,4,3,4,4,3,3,3,3,3,4,4,4,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,3,4,4,3,3,1,5,5.875,7,3.0,3.6,3.8,2.6,3.8,3.0,3.833333333,3.75,3.0,4.0,4.0,4.0,3,3,1,5,5.0,3.5,4.0,5.0,5.0,19,1,7.0,0,1,1,2,1,1,1,1,0,2,1,1,0,2,1,2,1,1,1,1,1,1,1,1,4,3,3,3,4,3,4,4,4,4,4,3,3,4,2,2,8,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,2,1,1,7,4,3,4,4,4,4,4,4,4,4,3,2,4,3,2,0.818181818,4.5,-0.5,4.5,-0.5,5.0,3.5,4.0,5.0,5.0,0.5,于向东,2.0,"任务一
问题一：
信息误导、信息泄露、数据库攻击
入侵后台
基础设施
国防
国际关系（政治），挑拨关系
恐怖分子是一个整体
问题二
国防军工、基础设施：生物识别技术封锁，增加人工管控，减少智能控制    同归于尽威慑
国际关系，人类之间：网络安全的加强，国家交流之间减少智能体介入
问题三
规则限定
端正人类思想，
在发展和控制之间找到平衡",9.82332342873175e-11,0.0,0.0,0.0,0.014676233965338216,0.3269951297893447,0.022405384108424187,0.4,0.020460358056266004
102011,6,1006,协调员,5.0,994.0,2.0,2.0,4.666666666666667,2.333333333333333,5.0,3.6666666666666665,5.333333333333333,3.6666666666666665,3.6666666666666665,3.666666667,4.666666667,4.333333333,6.0,4.62962963,3.777777778,4.666666667,4.0,4.4,3.1,4.1,0.0,4.8,5.0,4.666666667,4.8,5.0,4.333333333,3.5,5.0,4.25,3.0,3.5,5.0,4.4,15,14,25,22,6,A,1,2,0,1,1,1,4,8,3,2,4,5,3,2,4,4,3,4,4,5,5,5,3,3,4,4,2,2,3,3,3,4,3,3,3,4,3,4,3,4,4,3,4,4,4,3,3,4,4,4,3,4,3,4,4,4,4,4,4,4,5,5,4,5,3,5,2,2,4,2,6.5,8,3.166666667,3.8,2.8,3.2,3.6,3.8,4.166666667,3.5,3.5,4.0,4.333333333,4.25,2,2,4,2,4.5,5.0,4.5,5.0,4.5,23,0,6.0,0,1,1,1,1,1,1,2,1,1,1,1,0,1,1,1,0,1,1,1,0,2,0,1,7,4,4,5,5,5,5,5,5,5,5,4,4,3,3,5,7,1,2,1,1,0,1,1,1,0,1,1,1,1,1,0,2,1,2,1,1,7,5,5,4,5,5,5,5,5,5,5,4,3,3,3,5,0.636363636,4.7,-0.7,4.7,-0.7,4.5,5.0,4.5,5.0,4.5,0.7,邬瑶怡,1.0,"许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
基础设施领域、军工国防领域、国家关系领域、信息安全领域、切断卫星系统。
基础设施领域、军工国防领域：加强生物识别技术、威慑技术
国际关系领域、信息安全领域：加强防火墙网络安全技术，社交沟通区块链技术、提升加密技术、加强AI规则建设",5.520554066217206e-06,0.09302325581395349,0.07142857142857142,0.09302325581395349,0.03519543178353697,0.4071638849333035,0.11851076036691666,0.3395061728395062,0.029050279329608908
102004,7,1007,协调员,7.0,990.0,5.0,5.0,2.333333333333333,5.333333333333333,5.333333333333333,4.333333333333333,2.6666666666666665,2.6666666666666665,2.333333333333333,4.666666667,5.333333333,5.0,5.0,3.412962963,3.477777778,3.866666667,3.2,4.6,3.5,4.0,0.25,4.0,4.0,3.666666667,3.2,4.0,2.333333333,4.0,4.0,4.0,3.8,4.75,5.0,5.0,19,19,25,25,7,A,1,2,0,1,1,1,6,7,3,4,4,4,4,4,4,4,3,3,4,4,4,4,3,4,3,4,4,3,3,3,5,5,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,4,2,2,4,2,4,3,4,4,5,3,4,4,6.625,7,3.833333333,3.6,3.4,5.0,4.0,4.0,3.666666667,4.0,4.0,4.0,4.0,2.0,5,3,4,4,5.0,4.0,5.5,6.0,6.5,23,0,7.0,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,0,1,0,1,0,1,0,1,6,3,2,2,4,4,4,4,3,4,2,3,4,2,2,3,6,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,7,4,3,4,4,4,4,4,4,4,4,4,3,3,2,3,0.545454545,5.4,0.6,5.4,0.6,5.0,4.0,5.5,6.0,6.5,0.6,陈玟萱,1.0,"任务一：
许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
首先“AI恐怖分子”可能是由某类人群恶意创造、也可能由于AI发展萌生自我意识产生。其实以AI目前在世界范围内的普及率，他们可以在生活的方方面面进行破坏与带来危险，例如生活中最基本的饮食、医药、交通方面，再到国防、军师方面。比如改动一些智能汽车的程序导致事故发生、改变一些国防器械的程序导致国家战争等等。
关于解决方法，最根本的我认为是要给AI树立阶级意识，比如为何古代皇命不可违，其实“阶级意识”是最根本的原因，让AI在意识里成为人类的“仆人”，更多的是作为工具、从根本上否认他们的主体思想、且在此基础上不断的训练。",3.549926714831224e-05,0.326530612244898,0.2978723404255319,0.326530612244898,0.042952327269027424,0.4862397055654084,0.18904228508472443,0.29118773946360155,0.03872679045092842
102008,7,1007,启发员,3.0,993.0,5.0,5.0,3.0,4.0,4.666666666666667,5.333333333333333,5.0,4.666666666666667,3.6666666666666665,4.666666667,5.0,5.0,5.0,4.730555556,4.383333333,4.3,3.8,4.9,4.1,4.8,0.25,4.8,5.0,4.0,4.6,4.333333333,3.333333333,3.5,4.333333333,4.75,4.4,4.25,4.6,4.6,22,17,23,23,7,A,1,1,0,1,1,1,8,6,4,4,5,5,5,4,4,4,5,4,4,5,4,5,5,5,4,4,4,4,5,4,4,4,4,5,5,4,5,5,4,5,4,5,5,5,5,4,5,5,5,4,5,5,5,4,5,4,5,5,5,4,5,5,5,4,5,5,4,4,4,2,6.75,6,4.5,4.2,4.2,4.4,4.6,4.8,4.666666667,4.75,4.75,4.666666667,4.666666667,4.75,4,4,4,2,5.0,4.0,4.5,5.0,4.5,21,0,6.0,0,1,1,2,1,1,1,1,0,2,1,1,0,1,0,1,1,2,1,2,0,2,0,2,4,4,2,4,5,4,4,5,5,5,4,4,4,4,3,4,6,0,1,1,2,0,2,1,1,1,1,1,1,0,1,1,2,1,2,1,1,10,4,4,4,5,5,5,5,5,5,4,5,3,4,5,5,0.590909091,4.6,3.4,4.6,3.4,5.0,4.0,4.5,5.0,4.5,3.4,王然然,2.0,"任务一：
许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
首先，我想先定义一下AI恐怖分子和重大安全风险。我们7组讨论的结果是“毁灭人类”或者“人类与AI战争”，前者表现为AI从人类的衣食住行医等方面入手，做出破坏性行为，后者表现为AI从国防、军事等方面做出破坏性行为。技术应对方案的话，由于我们组非专业出身，我们无法准确表达解决方法，但我们一致同意应该由专业人士提供技术性应对方案。可行性和创新性的解决方法：训练AI成为人类的忠实仆人，类似君君臣臣父父子子，形成一种阶级性的AI思想。",3.49657598525942e-06,0.20930232558139536,0.16666666666666666,0.20930232558139536,0.02954627203124197,0.33747817969672356,0.18942682445049286,0.3157894736842105,0.03275620028076742
102021,7,1007,记录员,10.0,999.0,1.0,1.0,4.333333333333333,4.666666666666667,5.0,3.6666666666666665,5.333333333333333,3.333333333333333,3.333333333333333,5.0,4.666666667,5.0,4.666666667,3.810185185,3.861111111,4.166666667,4.0,3.6,3.3,4.0,0.375,3.6,4.333333333,4.0,3.8,5.0,4.0,4.5,3.666666667,3.5,3.8,4.25,4.0,4.2,19,17,20,21,7,A,1,3,0,1,1,1,9,7,5,5,5,4,4,5,4,4,4,4,5,4,4,4,5,4,4,4,4,3,3,4,5,4,5,4,4,4,4,4,5,4,5,4,4,5,4,4,5,4,4,5,4,4,4,4,5,4,2,4,4,4,4,4,4,4,4,4,4,2,3,6,7.75,7,4.666666667,4.2,3.6,4.4,4.2,4.4,4.166666667,4.25,4.25,4.333333333,4.0,3.5,4,2,3,6,6.0,5.5,4.5,5.5,5.5,22,0,9.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,1,0,1,0,1,1,1,9,4,4,4,5,5,5,4,3,4,4,4,4,3,3,4,7,1,1,1,1,0,1,0,1,1,1,1,1,0,1,0,1,0,1,0,1,6,4,4,4,4,4,5,3,3,4,4,4,4,3,3,4,0.454545455,5.4,3.6,5.4,3.6,6.0,5.5,4.5,5.5,5.5,3.6,彭佳晴,3.0,"在重大安全风险方面，AI搞破坏直接目的为毁灭人类。同时AI变坏的原因也可能是人为，尤其是在国防、军事方面，若AI涉足以上领域，将有巨大的安全风险。AI和人类对抗，最大部分可以在国防方面，平时可以从食物、医药、交通等日常方面。智能汽车改一些程序，会导致人类事故；AI策反高智商人类，达到高智商文明。AI换脸、人脸识别漏洞、AI声音识别等等，可以通过这些搞诈骗。
关于预防以上行为的发生，加强安全层面的管控，加强对AI控制，尤其伦理方面，让AI成为人类的仆人。对AI树立阶级意识，不停训练，有坏想法立即打回。但AI多厉害也需要电，必要的时候可以通过断电操作来规避一些风险。多培养高智IT人士，更多人可以操控AI，防患于未然。",0.00012226796752597187,0.5000000000000001,0.39130434782608703,0.5000000000000001,0.04863703200995363,0.2711806657714009,0.12227562069892883,0.2896174863387978,0.053014553014553045
102000,8,1008,启发员,1.0,988.0,1.0,1.0,4.666666666666667,4.333333333333333,5.0,5.0,2.333333333333333,3.6666666666666665,3.333333333333333,4.333333333,4.333333333,4.333333333,4.666666667,3.637037037,3.822222222,3.933333333,3.6,4.6,4.0,4.1,0.375,4.2,4.666666667,3.666666667,4.4,3.666666667,3.333333333,3.5,4.333333333,4.5,4.6,4.5,4.6,4.6,23,18,23,23,8,A,1,1,0,1,1,1,5,9,3,2,4,4,4,3,3,2,4,3,4,3,4,4,4,3,3,4,3,4,4,5,4,3,3,3,3,4,3,3,3,4,3,3,3,3,4,3,4,3,3,3,3,3,3,4,4,2,2,3,3,2,3,3,2,2,4,3,3,3,5,4,7.5,9,3.333333333,3.2,4.0,3.2,3.4,3.2,3.5,3.25,3.0,3.333333333,2.666666667,2.5,3,3,5,4,4.5,4.0,4.5,4.0,4.5,20,1,9.0,0,1,1,1,0,1,1,2,1,1,1,1,1,1,1,1,0,2,0,2,1,1,1,2,6,4,2,4,4,4,3,5,4,4,5,4,1,4,3,3,8,1,1,1,1,1,1,1,1,1,1,0,2,0,1,0,1,0,2,1,1,7,4,3,4,4,5,5,4,5,5,3,4,2,3,4,4,0.636363636,4.3,0.7,4.3,0.7,4.5,4.0,4.5,4.0,4.5,0.7,彭云,3.0,我认为可能在战争与生活领域都会有影响，首先我觉得我们不能完全的依靠Ai在发展的同时也要掌握去Ai化的东西，这样可以防止一些意外的发生，就比如现在智能驾驶的出现我们还是要学习科一、科二就是为了防止特殊情况。在技术方面我觉得可以加强Ai的底层逻辑，使人类在掌握不住Ai的时候及时停止，这样人们才能和Ai和谐共同的成长。,4.4250421068242695e-13,0.13513513513513511,0.1111111111111111,0.13513513513513511,0.013912893466334657,0.3949132600927665,0.17962656915187836,0.4166666666666667,0.015980823012385126
102010,8,1008,协调员,4.0,994.0,8.0,8.0,2.0,4.666666666666667,6.0,1.3333333333333333,2.333333333333333,3.333333333333333,2.6666666666666665,5.0,3.333333333,3.333333333,5.666666667,2.951851852,2.711111111,2.266666667,2.6,2.6,2.9,3.1,0.0,4.0,4.0,3.666666667,3.4,3.666666667,3.666666667,4.5,3.666666667,4.25,4.4,4.0,3.2,4.6,22,16,16,23,8,A,1,2,0,1,1,1,6,3,3,3,2,3,4,2,2,4,4,3,3,3,4,4,4,4,4,3,3,4,4,2,3,4,4,4,4,4,4,3,3,3,4,4,4,4,4,4,3,3,3,4,3,3,3,5,4,4,4,4,4,4,4,4,3,4,3,4,4,3,2,6,4.125,3,2.833333333,3.2,3.2,3.8,3.4,4.0,3.833333333,3.25,3.25,4.0,4.0,4.0,4,3,2,6,5.0,5.0,5.0,5.0,5.5,24,0,7.0,1,1,1,1,1,1,0,2,1,1,1,1,0,1,0,1,0,2,1,1,1,2,1,2,7,4,4,3,4,4,3,4,3,4,4,2,4,2,2,4,7,0,1,1,1,0,1,0,1,0,2,1,1,0,2,0,2,0,1,0,1,7,4,3,4,4,4,4,4,4,4,4,4,3,3,3,4,0.454545455,5.1,0.9,5.1,0.9,5.0,5.0,5.0,5.0,5.5,0.9,赵敏欣,1.0,"重大安全风险：我们组认为AI的潜在威胁包括“个人、国家”两方面，它首先会个人和国家涉及数据泄露，其次是人身安全方面，它可能会遇到“攻击人类”的指令从而像科幻大片一样危害人自身的安全。同时也会遇到道德悖论，即“火车创一个人还是十个人的问题”，或者可能应该在伤害本身这个行为就加以约束，防治出现进一步的损伤。
创新性的方法：在AI背后增加一层“AI”，来约束他的行为，给他建造更高的指令，就是给AI增加一层法律的约束作为最后的底线，同时对有可能的漏洞进行自己和自己对话的一种自己补全的机制。然后小组讨论也说明了可以加数据警察这么一个身份，进行人工的补齐与驯化这么一个功能。同时增加自毁程序，在预料不到的时候进行自我毁灭或者人工干预的毁灭。",1.21546327077544e-06,0.1081081081081081,0.08333333333333333,0.1081081081081081,0.031614972240932585,0.438926476233101,0.1485162079334259,0.5025906735751295,0.03693830921553698
102020,8,1008,记录员,8.0,999.0,4.0,4.0,2.0,4.0,3.333333333333333,4.666666666666667,3.333333333333333,3.333333333333333,3.333333333333333,5.0,4.666666667,4.666666667,5.333333333,4.022222222,4.133333333,3.8,3.8,4.2,4.0,4.3,0.5,5.0,5.0,5.0,4.4,4.666666667,4.666666667,4.0,4.0,3.75,3.2,3.5,3.8,4.2,16,14,19,21,8,A,1,3,0,1,1,1,7,7,4,4,4,2,4,4,4,3,4,3,3,4,4,4,4,5,3,4,4,3,3,2,4,4,4,3,4,4,4,3,4,5,5,4,4,3,4,3,4,3,3,3,3,3,3,4,4,4,3,3,2,2,4,3,4,4,3,3,4,3,2,2,7.0,7,3.666666667,3.4,3.2,3.8,4.0,4.0,4.0,3.25,3.0,4.0,3.666666667,2.5,4,3,2,2,6.5,6.0,5.5,6.0,6.0,25,0,8.0,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,8,5,5,4,5,4,5,5,5,4,5,3,3,3,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,8,5,5,5,5,5,5,5,5,5,5,5,3,3,3,4,0.909090909,6.0,1.0,6.0,1.0,6.5,6.0,5.5,6.0,6.0,1.0,杨瑞雪,2.0,"任务一 第八组
领域或技术层面带来重大安全风险：
可能通过篡改数据、窃取情报等方式威胁国家安全，发动战争；
窃取隐私数据影响个人生活
应对方案：
制定底层的法律制度和规则，底层逻辑以保护人类生命为主；
研制自毁程序；
发展更智能的“AI警察”；
修补被破坏的漏洞；个人提升防御意识和防御能力；
研发外在干预技术，比如在程序已经启动，无法终止的情况下应用该技术，有效终止“AI恐怖分子”的活动。",9.659836662607608e-07,0.07999999999999999,0.04166666666666667,0.07999999999999999,0.026022972002871497,0.34080626367261635,0.14326202869415283,0.3277310924369748,0.0318367346938776
102006,9,1009,启发员,4.0,991.0,2.0,2.0,1.6666666666666667,4.333333333333333,6.0,2.6666666666666665,2.333333333333333,4.0,3.6666666666666665,6.0,4.666666667,5.0,5.666666667,3.636111111,3.816666667,3.9,3.4,4.6,4.1,3.7,0.375,4.0,4.0,4.0,4.0,3.0,3.333333333,4.0,4.333333333,4.0,4.2,5.0,5.0,5.0,21,20,25,25,9,A,1,1,0,0,0,1,7,6,2,2,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,2,2,4,3,2,4,1,1,4,4,4,2,2,4,6,6.375,6,3.333333333,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,2.333333333,3.333333333,2.0,2,2,4,6,5.5,3.0,5.0,5.5,5.5,18,0,8.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,2,0,1,1,1,8,4,2,4,3,4,2,4,4,4,4,4,4,3,4,4,9,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,9,4,4,4,4,4,4,4,4,4,4,4,2,4,4,4,0.681818182,4.9,2.1,5.7855,1.2145,6.327,4.305,5.738,6.331,6.225,1.2145,朱耔潓,2.0,"讨论主题：请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。
冷门学科：
考古学
焕发生命力的原因：
可以增强判断的准确性，减少人工失误，AI和人工结合，可以极大的增强准确性；
AI面对一些肯定性的问题，可以严格根据法律，增强司法公正性；
AI可以处理分析大量的地形地貌，更模拟推断出该地形有没有遗迹，传统考古在探查时也在使用工具；
AI可以短时间内结合大量文献去分析制定该文物的挖掘方法。
可能的挑战：
AI缺乏创新力，无法像人类一样去提出有价值的创新点。
面对伦理问题，AI无法做到司法公正；
面对未知文物，AI无法做出准确判断，判断失误可能使得文物损坏。",3.0436298372215503e-10,0.1627906976744186,0.14285714285714288,0.1627906976744186,0.015607334579903341,0.3352710138751498,0.1294226348400116,0.3804347826086957,0.020535714285714324
102015,9,1009,记录员,5.0,996.0,3.0,3.0,6.0,3.6666666666666665,4.333333333333333,4.666666666666667,1.0,3.0,3.0,6.0,5.666666667,4.666666667,5.333333333,3.741666667,4.45,3.7,3.2,4.6,4.3,4.2,0.5,3.2,3.666666667,4.0,4.0,4.333333333,4.0,4.0,3.666666667,3.75,3.2,3.75,4.2,4.0,16,15,21,20,9,A,1,3,0,0,0,1,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,3,3,4,3,5,5,5,3,3,3,3,3,5,5,3,4,4,3,3,3,3,2,3,1,3,2,2,3,2,2,4,5,5,4,1,3,3,5.0,5,4.0,4.0,4.0,3.2,4.4,3.0,4.0,4.25,3.25,2.333333333,3.0,1.75,4,1,3,3,6.0,3.5,5.5,6.0,6.5,21,0,7.0,0,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,1,1,1,1,0,1,0,1,8,4,4,4,4,5,4,4,4,4,4,4,3,3,3,3,7,1,1,1,1,0,1,0,1,1,1,1,1,0,1,1,1,0,1,1,1,7,4,4,4,4,4,3,3,3,3,4,3,3,3,3,3,0.590909091,5.5,-0.5,6.3855,-1.3855,6.827,4.805,6.238,6.831,7.225,1.3855,蔡雅玲,3.0,"讨论主题：请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。
冷门学科：
考古学
焕发生命力的原因：
可以增强判断的准确性，减少人工失误，AI和人工结合，可以极大的增强准确性；
AI面对一些肯定性的问题，可以严格根据法律，增强司法公正性；
AI可以处理分析大量的地形地貌，更模拟推断出该地形有没有遗迹，传统考古在探查时也在使用工具；
AI可以短时间内结合大量文献去分析制定该文物的挖掘方法。
可能的挑战：
AI缺乏创新力，无法像人类一样去提出有价值的创新点。
面对伦理问题，AI无法做到司法公正；
面对未知文物，AI无法做出准确判断，判断失误可能使得文物损坏。
个人看法：我觉得在考古学方面，AI可以起一个辅助的作用，分析大量文献，可以极大的增加考古的效率；但是在面对一些未知文物时，还是需要专业的人士去做出判断，如果AI根据他所有的知识库去模糊匹配一些文物，可能会对文物造成不可估量的损失，未知文物所需的新方法、新手段，AI也无法提供。",6.196799348428194e-10,0.0704225352112676,0.06382978723404255,0.0704225352112676,0.016914252455846806,0.4059200897408767,0.2111499309539795,0.4291044776119403,0.022261277094317466
102017,9,1009,协调员,5.0,997.0,4.0,5.0,2.0,2.333333333333333,2.333333333333333,4.666666666666667,2.333333333333333,2.0,2.333333333333333,3.333333333,3.333333333,3.666666667,2.666666667,4.025,4.15,3.9,4.4,3.4,4.6,4.4,0.0,3.8,4.0,2.666666667,3.8,4.0,2.666666667,4.0,4.0,4.25,3.8,4.25,4.2,4.2,19,17,21,21,9,A,1,2,0,0,0,1,6,6,2,3,3,3,3,4,4,4,4,4,4,4,4,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,5,3,4,4,4,4,3,3,4,4,4,2,3,4,3,3,3,6.0,6,3.0,4.0,4.0,4.0,4.0,4.0,4.166666667,3.75,4.0,4.0,3.666666667,3.75,4,3,3,3,5.0,3.0,6.0,3.5,6.0,21,0,10.0,0,1,0,2,0,2,1,2,1,2,1,1,0,1,0,1,0,2,0,2,1,2,0,2,3,3,2,3,4,4,4,4,3,4,4,4,4,2,3,2,3,1,2,1,2,0,2,1,2,1,2,1,2,0,2,1,2,1,2,1,2,2,3,2,3,4,4,4,4,4,4,3,4,4,2,2,2,0.545454545,4.7,1.3,5.5855,0.4145,5.827,4.305,6.738,4.331,6.725,0.4145,卢迅,1.0,"讨论主题：请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。
冷门学科：
考古学
焕发生命力的原因：
可以增强判断的准确性，减少人工失误，AI和人工结合，可以极大的增强准确性；
AI面对一些肯定性的问题，可以严格根据法律，增强司法公正性；
AI可以处理分析大量的地形地貌，更模拟推断出该地形有没有遗迹，传统考古在探查时也在使用工具；
AI可以短时间内结合大量文献去分析制定该文物的挖掘方法。
可能的挑战：
AI缺乏创新力，无法像人类一样去提出有价值的创新点。
面对伦理问题，AI无法做到司法公正；
面对未知文物，AI无法做出准确判断，判断失误可能使得文物损坏。",8.258484755193566e-10,0.10769230769230768,0.09375,0.10769230769230768,0.016163394358452966,0.44632270613863123,0.18386991322040558,0.4076086956521739,0.021739130434782594
102002,10,1010,启发员,8.0,989.0,0.0,0.0,4.0,4.333333333333333,4.0,3.0,4.333333333333333,2.6666666666666665,2.333333333333333,4.666666667,3.666666667,3.333333333,5.666666667,3.965740741,3.794444444,3.766666667,3.6,4.6,2.8,3.6,0.375,3.6,4.0,2.666666667,3.0,3.0,2.0,5.0,4.0,3.75,3.6,2.25,3.6,3.2,18,9,18,16,10,A,1,1,0,0,0,1,8,6,3,4,4,4,4,3,3,3,3,3,4,3,3,3,5,4,5,4,3,2,3,2,2,2,2,3,3,4,4,4,4,4,3,3,3,3,4,3,3,3,4,4,3,4,3,3,3,4,3,4,3,3,4,2,3,4,5,5,4,3,4,4,6.75,6,3.666666667,3.2,2.8,2.4,4.0,3.2,3.833333333,3.25,3.5,3.0,4.0,2.75,4,3,4,4,7.0,6.5,6.5,7.0,7.5,20,1,5.0,0,1,1,2,1,1,1,1,0,2,1,1,0,1,1,1,1,1,1,1,1,1,1,2,5,2,2,2,3,3,3,4,3,2,2,4,3,3,2,2,6,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,6,4,2,2,4,4,4,4,4,3,4,3,2,3,3,2,0.772727273,6.9,1.1,7.7855,0.2145,7.827,7.805,7.238,7.831,8.225,0.2145,权煜然,2.0,"冷门学科：
古文字学
可能原因：
语言的特征可以运用人工智能挖掘，在结构上发现其独特性；
运用一些相关的算法进行语义预测，磅数现代人理解失传的语言
人工智能有实现对语音语调的识别与生成
可能挑战：
现有的知识储备有限，无法提供大量数据
人类自身对于学科理解可能存在偏差
如何利用AI技术助力该学科的传承、创新与社会应用：
运用神经网络识别古文字的含义；
运用人工智能处理数据集，在其基础上进行扩充；
应用方面：可以对考古进行补充，厘清历史；推进语言学的研究；挖掘语言的文化；还可以还原古代语言的发音，创设某些文学作品的历史语境；通过人工智能技术识别文字手稿，构建数据库；利用AI将不同的语言进行对比研究，推进语言学的发展；构建古文字学智能体，辅助古文字学相关研究，如帮助整理资料；利用AI构建该学科与大众的桥梁，推动冷门绝学深入大众生活；",8.597149159435204e-22,0.019169329073482427,0.012861736334405146,0.019169329073482427,0.0072636955808613336,0.18633245815417177,0.14479166269302368,0.3961352657004831,0.008561462847479162
102009,10,1010,协调员,7.0,993.0,3.0,3.0,2.333333333333333,3.6666666666666665,4.333333333333333,3.6666666666666665,3.0,3.333333333333333,3.6666666666666665,4.333333333,4.666666667,4.0,5.0,3.662962963,3.977777778,3.866666667,3.2,4.0,3.5,4.3,0.375,4.8,5.0,5.0,3.8,4.0,4.0,4.0,5.0,4.0,3.6,4.0,4.4,4.0,18,16,22,20,10,A,1,2,0,0,0,1,7,7,4,4,4,4,4,4,4,3,4,3,4,4,4,4,4,4,3,4,4,3,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,2,3,3,3,3,4,2,2,3,7.0,7,4.0,3.6,3.8,4.0,3.8,4.0,3.833333333,4.0,3.75,3.666666667,4.0,3.5,4,2,2,3,6.5,5.0,5.0,5.0,6.0,19,0,6.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,2,1,2,1,2,1,1,6,4,4,4,4,4,4,4,4,4,3,4,4,4,3,4,7,1,1,1,1,1,1,0,1,1,1,1,1,1,1,0,2,0,1,1,1,8,5,5,5,5,5,5,5,5,5,4,5,3,4,3,3,0.727272727,5.5,1.5,6.3855,0.6145,7.327,6.305,5.738,5.831,6.725,0.6145,王鑫,1.0,"古语言学的延续发展
机遇：ai的强大算力可以在现有资料中找到规律，破解文字背后的意义。还原古语言的语法体系。Ai生成的模型可以开放使用，降低公众对本学科的认知门槛，吸引更多人参与到古语言的学科研究中。
挑战：语料库内容少，现有研究资料不足，对本学科研究成果不够成熟，无法直接供ai使用。
方法：算法分析古语言句法，图片识别相似字体，破解文字本义。使用大模型联系语义，语音，字形，还原文字。",7.15845573512273e-08,0.08571428571428572,0.05882352941176471,0.08571428571428572,0.018141718838929996,0.33783706897446225,0.1300026923418045,0.3391304347826087,0.02517753389283406
102014,10,1010,记录员,5.0,996.0,3.0,4.0,4.0,3.333333333333333,5.0,3.333333333333333,3.0,3.333333333333333,2.6666666666666665,4.333333333,4.0,4.0,4.0,4.137962963,3.827777778,3.966666667,3.8,4.0,4.3,4.4,0.875,4.0,4.333333333,2.666666667,4.2,4.0,2.333333333,4.0,4.0,3.5,3.8,3.25,4.2,4.2,19,13,21,21,10,A,1,3,0,0,0,1,7,7,4,4,4,5,5,5,4,4,4,3,4,4,4,5,5,5,5,4,4,3,5,5,4,4,4,4,4,4,4,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,1,4,2,1,5,1,4,4,4,4,5,1,1,4,7.0,7,4.5,3.8,4.2,4.0,4.6,4.0,4.666666667,4.0,4.0,4.0,4.666666667,1.25,5,1,1,4,7.0,5.5,6.5,6.5,7.0,23,1,6.0,1,1,1,1,1,1,1,1,0,2,1,1,0,2,0,2,0,2,1,2,0,2,0,2,5,3,2,2,4,4,4,4,4,4,4,5,2,4,2,2,8,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,2,0,2,1,1,8,4,2,2,4,5,4,4,4,4,4,4,2,4,4,2,0.681818182,6.5,0.5,7.3855,-0.3855,7.827,6.805,7.238,7.331,7.725,0.3855,刘儒骁,3.0,"冷门学科：古文字学
可能原因：
语言的特征可以运用人工智能挖掘，在结构上发现其独特性；
人工智能有实现对语音语调的识别与生成
可能挑战：
现有的知识储备有限，无法提供大量数据；
目前该学科对语言深层次的内容理解可能有问题，给予AI的信息不一定准确
如何利用AI技术助力该学科的传承、创新与社会应用：
运用神经网络识别古文字的含义；
运用人工智能处理数据集，在其基础上进行扩充；
应用方面：可以对考古进行补充，厘清历史；推进语言学的研究，帮助整合相关资料；还可以还原古代语言的发音，创设某些文学作品的历史语境；通过人工智能技术识别文字手稿，构建数据库；可以运用算法进行语义预测
还可以利用创新技术生成新的文本、音频等，推广古文字学的研究，让大众有机会认识到该学科",1.8001081705569115e-05,0.06666666666666667,0.034482758620689655,0.06666666666666667,0.036370903277378096,0.37232601394014897,0.16610664129257202,0.291005291005291,0.0387143900657414
102016,11,1011,记录员,9.0,997.0,2.0,2.0,2.6666666666666665,1.3333333333333333,3.333333333333333,3.0,5.0,2.333333333333333,2.0,4.666666667,4.333333333,2.666666667,4.333333333,2.660185185,2.961111111,1.766666667,2.6,2.9,3.7,4.6,0.125,2.4,3.333333333,1.666666667,2.4,3.0,1.333333333,4.0,4.333333333,4.25,3.6,2.5,4.0,4.2,18,10,20,21,11,A,1,3,0,0,0,1,3,5,2,2,4,4,4,4,4,4,4,4,4,3,2,2,2,4,4,2,2,2,2,2,2,2,2,3,2,2,2,2,2,2,2,2,2,2,2,3,2,3,3,3,3,3,3,4,3,4,5,4,5,4,4,5,4,3,3,3,3,1,2,1,4.25,5,3.333333333,4.0,2.0,2.2,2.0,2.0,2.833333333,2.75,3.0,3.666666667,4.0,4.75,3,1,2,1,7.5,6.5,6.5,6.5,7.5,22,0,3.0,0,2,1,1,1,2,1,2,0,2,1,1,0,1,1,1,0,2,0,2,1,1,1,2,3,2,1,1,3,3,3,2,2,2,4,2,2,2,2,2,3,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,2,1,1,6,2,1,2,3,3,4,3,3,2,2,2,2,2,2,3,0.636363636,6.9,-3.9,7.7855,-4.7855,8.327,7.805,7.238,7.331,8.225,4.7855,张滢,3.0,"1.具有发展潜力的冷门学科：古生物学
2.焕发新生的原因：
（1）AI可以通过图像识别和机器学习技术，帮助科学家更快速地识别和分类化石，分析其中蕴含的符号内涵并进行分析、联系。
（2）利用AI模拟古环境和生物演化过程，帮助我们预测新的化石或者新的物种可能出现的位置，帮助我们发掘或者。
（3）AI可以整合其它学科的相关数据，比如地质学、气候学、生态学等多个学科的数据，为古生物学提供更全面的分析框架。
3.在未来发展中面临的可能挑战：
（1）古生物学已有的研究比较少，可供AI分析的 数据和资料有限。
（2）挤压相关研究人员的研究空间或者增加大家的发掘压力。如果AI不断提供新的可研究地点或者化石，需要合理分配人力进行研究；而且会导致相关研究在一定时间内停滞不前。
（3）需要古生物-人工智能的跨学科人才，不同领域的专家在相关领域具有权威性，但其它领域的专家可能理解不了。",4.602886842571637e-06,0.208955223880597,0.15151515151515152,0.208955223880597,0.03197893152746426,0.4330382372884942,0.1725263148546219,0.3593073593073593,0.0355156183140779
102022,11,1011,协调员,7.0,1000.0,3.0,3.0,4.333333333333333,4.0,5.333333333333333,4.666666666666667,3.333333333333333,2.6666666666666665,3.6666666666666665,4.666666667,4.666666667,5.0,5.0,4.116666667,4.7,4.2,4.2,4.3,4.2,4.2,0.25,3.4,3.666666667,3.666666667,4.0,4.333333333,3.666666667,5.0,4.333333333,2.25,3.2,3.5,3.4,4.0,16,14,17,20,11,A,1,2,0,0,1,1,5,8,3,4,4,4,4,4,4,3,3,3,4,3,1,3,3,3,3,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,1,1,1,1,4,3,3,3,2,4,4,3,4,3,2,4,3,2,4,4,2,3,1,3,1,6.875,8,3.833333333,3.4,1.0,1.0,1.2,2.0,2.666666667,1.0,3.25,2.666666667,4.0,2.75,3,1,3,1,5.0,3.0,6.0,5.0,5.0,18,0,8.0,0,2,1,1,1,2,1,1,1,1,1,1,0,1,0,1,1,2,1,2,0,1,1,1,7,4,4,3,5,5,3,4,4,3,4,5,3,4,3,4,8,1,1,1,1,0,2,1,1,1,1,1,1,1,1,1,2,0,2,1,1,8,4,4,3,4,4,3,3,3,3,3,5,3,4,2,2,0.727272727,4.8,0.2,5.6855,-0.6855,5.827,4.305,6.738,5.831,5.725,0.6855,孙恒米南,2.0,"人工智能时代的地质勘探
当今时代的地质勘探需要人类去到现场进行样本采集，再带回进行分析。但人工智能完全可以自主进行样本采集，并可以比人类进行更加精确的位置测算，平均采集，并可以预测样本的品质，直接开始更加精确的鉴定与检测。效率会大大提升。地质勘探过程中人为因素影响可能巨大，机器往往比人类具有更加理性精确的判断与行动。
但也会面临数据不能活用，或人类的灵感被遏制的难题。
ai应该辅助人类完成任务，并由人类时时监控操作。人也应更多参与，合理利用ai得到基础数据，但高端处理还是要自己动手。",8.563264033992565e-05,0.13793103448275862,0.07407407407407407,0.13793103448275862,0.041027710519235955,0.2985055259232985,0.11964846402406693,0.25,0.043209876543209846
102012,12,1012,记录员,2.0,995.0,3.0,3.0,2.6666666666666665,6.0,6.0,6.0,2.0,3.6666666666666665,3.333333333333333,6.0,6.0,6.0,6.0,5.0,5.0,5.0,5.0,5.7,5.3,5.3,0.625,4.0,4.0,3.333333333,3.4,3.666666667,3.333333333,4.5,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,12,A,1,3,0,0,0,1,9,9,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,5,1,3,4,4,4,5,5,3,6,9.0,9,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,3.666666667,4.333333333,2.75,5,5,3,6,6.0,3.5,5.5,5.5,6.0,19,1,8.0,0,1,1,1,0,1,1,1,1,1,0,1,0,1,0,1,1,1,1,1,1,2,1,1,8,4,2,4,3,4,4,4,4,3,3,3,3,3,3,4,8,1,2,1,1,0,1,1,1,1,1,1,1,0,1,0,1,0,1,0,1,8,4,2,4,4,4,4,4,4,4,4,4,4,4,4,3,0.545454545,5.3,3.7,6.1855,2.8145,6.827,4.805,6.238,6.331,6.725,2.8145,李承潞,2.0,"考古
人工智能时代可能焕发新生命力的可能原因
1.降低人工成本
2.提升识别效率
3.扩展考古面
在未来发展中面临的可能挑战
1.数据库录入不易
2.AI难以理解文物背后的内涵
3.AI技术可能提高技术门槛，对考古工作者提出更大挑战
如何利用AI技术助力该学科的传承、创新与社会应用
传承：利用AI学习考古专业知识
创新：文物数据计入AI数据库，形成中华考古体系
社会应用：AI讲解员，在博物馆讲解考古文物背后的故事",1.2188194658890044e-09,0.3076923076923077,0.2368421052631579,0.3076923076923077,0.021831614883094222,0.2667364265909303,0.1616775542497635,0.42857142857142855,0.023308957952468012
102013,12,1012,协调员,9.0,995.0,6.0,7.0,6.0,4.666666666666667,6.0,5.666666666666667,1.0,3.6666666666666665,3.6666666666666665,6.0,5.0,6.0,6.0,4.402777778,4.416666667,3.5,5.0,5.5,5.2,5.9,0.625,4.8,5.0,3.0,4.8,4.0,3.333333333,5.0,4.0,4.25,3.0,3.5,3.6,5.0,15,14,18,25,12,A,1,2,0,0,0,1,9,9,5,4,5,5,5,5,5,5,5,5,5,3,5,5,5,2,4,5,5,3,4,2,4,5,5,5,4,5,5,4,5,5,4,4,4,5,5,5,4,4,5,5,5,4,4,3,2,4,1,4,2,1,4,1,3,5,5,5,5,4,4,6,9.0,9,4.833333333,5.0,3.8,4.6,4.8,4.4,4.0,4.5,4.5,2.666666667,4.0,1.25,5,4,4,6,6.5,3.5,5.5,6.0,6.5,20,1,7.0,0,1,1,1,1,1,1,1,0,2,0,2,0,1,0,1,0,1,1,2,0,1,0,1,5,4,3,3,4,5,3,5,4,5,5,5,3,2,5,4,7,1,1,1,1,0,1,1,2,0,1,1,1,0,1,0,1,1,1,1,1,6,3,2,4,5,5,5,5,5,5,5,4,3,2,5,4,0.454545455,5.6,3.4,6.4855,2.5145,7.327,4.805,6.238,6.831,7.225,2.5145,刘恩启,1.0,"考古学就非常冷门
工作量大减，通过数据库让AI进行大量的工作，比如
利用已有的进行预处理刚出土文物，识别一些基础信息
替代人类完成恶劣条件下的工作
AI在知识上可以协助人类，但是当需要人去完成的事情的时候，原来的挑战依旧存在
比如文物保存方面，AI明确环境但还是需要人去完成，原来保存环境差现在仍然差
AI无法了解文物的深层内涵及其历史意义，AI只能识别出这是一副字画，也可以知道里面是什么内容，但是他无法明白这可能是古代历史的一个重大事件记录，或者其他的意义
AI变相提高的加入行业的门槛，以前可能只挖土，现在既要挖土也要用AI，导致原有的人可能跟不上，新人所需要的教育水平提高
利用AI宣传此学科，宣传量大，受众群体才会更多，才有更多人加入传承
降低专业知识门槛，以前需要了解全部知识，利用AI则可以随时询问调取庞大的数据库，辅助研究",1.6780245155989218e-08,0.1636363636363636,0.14814814814814814,0.1636363636363636,0.01749142410759773,0.25844349191042837,0.11168374121189117,0.33181818181818185,0.022482291345857663
101001,13,1013,记录员,8.0,1.0,6.0,6.0,2.6666666666666665,4.0,5.0,4.333333333333333,4.0,3.333333333333333,3.333333333333333,5.333333333,5.333333333,5.0,5.666666667,4.25,4.5,4.0,4.0,4.8,4.1,4.6,0.5,5.0,4.333333333,3.666666667,5.0,5.0,4.666666667,5.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,13,A,1,3,0,1,0,1,3,8,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,3,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,6.125,8,2.0,2.0,2.2,2.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0,2,2,2,2,6.0,6.0,5.5,6.0,6.5,20,1,7.0,0,1,1,1,1,1,1,2,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,8,5,5,4,5,5,5,5,5,5,5,5,3,4,2,4,7,1,1,1,1,0,2,1,1,0,1,1,1,1,1,0,1,1,2,1,1,8,3,4,4,3,5,5,5,5,5,5,5,2,5,2,3,0.772727273,6.0,-3.0,6.0,-3.0,6.0,6.0,5.5,6.0,6.5,3.0,常云啸,2.0,"主题：讨论AI恐怖分子对人类的潜在威胁以及解决方法
内容选择：AI在心理咨询方面的潜在威胁
主要威胁：AI可能在心理咨询方面可能生成一些冒犯性的内容，比如性别歧视、错误引导之类。尤其是对于有心理疾病方面的人，其内心可能更加敏感脆弱。另外，ai可能在一开始会很好的帮助人，从而让人类产生信任，但万一某一天模型出现问题，而输出一些不合适的内容，对人的伤害会更加巨大。
具体案例：目前有新闻报道，有人类在长时间与ai聊天后选择自杀。
人类监督：心理咨询中人类与ai相辅相成，以ai为辅，人类为主，人类监督ai的生成内容。提升人类的监督力度。
更高效精确的监督算法：发明更先进的监督算法，让ai做出正确的引导。拿课堂上的ai越狱来说，目前ai在反越狱上已经有很大进步，课件上的例子在现在的ai工具中已经失效。AI需要接受人类的监督，从而生成更利于人类的内容，尤其在心理咨询方面。",1.4757435585378344e-07,0.22807017543859648,0.21428571428571425,0.22807017543859648,0.02629159772016915,0.5091929929620875,0.19595308601856232,0.39826839826839827,0.03070761014686252
101009,13,1013,启发员,7.0,5.0,5.0,5.0,3.333333333333333,1.6666666666666667,5.0,3.6666666666666665,4.333333333333333,3.6666666666666665,4.0,5.0,4.0,3.666666667,5.333333333,3.975,3.85,3.1,2.6,4.2,4.1,4.3,0.375,4.8,5.0,4.0,5.0,5.0,4.666666667,3.5,4.666666667,5.0,4.4,5.0,4.8,4.8,22,20,24,24,13,A,1,1,0,1,0,1,7,6,4,4,5,5,5,5,5,4,5,5,5,4,4,5,5,5,4,5,5,3,3,2,4,4,4,5,5,5,5,5,5,5,5,4,4,5,5,5,5,5,5,4,4,4,4,5,4,5,2,5,2,1,4,1,3,3,5,5,5,5,5,5,6.375,6,4.666666667,4.8,3.6,4.4,5.0,4.6,4.5,5.0,4.0,4.0,4.666666667,1.5,5,5,5,5,7.0,7.0,6.0,7.0,7.0,23,0,7.0,0,1,1,1,1,1,1,1,1,2,1,1,0,2,0,1,1,1,1,1,1,1,1,1,8,4,5,5,5,5,5,5,5,5,5,5,2,4,4,4,8,1,1,1,1,1,2,0,1,1,1,1,1,0,2,0,2,0,2,1,1,7,4,4,4,5,5,5,5,5,5,5,4,2,4,4,3,0.681818182,6.8,0.2,6.8,0.2,7.0,7.0,6.0,7.0,7.0,0.2,苏志琪,3.0,"AI恐怖分子在哪些领域或技术层面带来重大安全风险：
坏的心理暗示，
泄露信息
攻击日用智能家电
生成谣言信息、制造恐慌
在高度信息化自动化的时代，一切与网络自动有关的东西都会成为AI恐怖分子攻击的工具
攻击自动化的农业设施，影响粮食安全
措施
立法
规范心理咨询程序、人类最终把控
调整AI内部算法
提高消息发布甄别能力的宣传、标注疑似AI生成的内容
保留部分人工手动控制的机制",1.0990190717021313e-20,0.06400000000000002,0.04878048780487806,0.06400000000000002,0.006944613660176904,0.39332665783027654,0.052977584302425385,0.3925233644859813,0.00923482849604218
101018,13,1013,协调员,8.0,9.0,3.0,4.0,4.666666666666667,4.0,3.6666666666666665,4.333333333333333,2.333333333333333,2.0,2.0,4.0,4.0,4.0,4.0,3.998148148,3.988888889,3.933333333,3.6,4.4,4.2,4.5,0.125,4.0,4.0,2.666666667,4.0,3.333333333,2.666666667,3.5,3.666666667,3.0,3.6,2.75,3.6,4.0,18,11,18,20,13,A,1,2,0,1,0,1,4,6,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,2,2,4,3,4,4,4,4,4,2,3,2,5.25,6,4.0,4.0,4.0,4.0,3.8,3.6,4.0,4.0,4.0,4.0,4.0,2.5,4,2,3,2,0.0,0.0,6.0,7.0,7.0,22,0,7.0,0,1,1,1,1,2,1,1,1,1,1,1,0,1,1,1,1,2,1,2,0,1,1,1,6,3,2,3,3,4,3,4,4,4,4,4,3,2,2,2,7,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,1,2,0,1,7,3,2,3,4,4,4,4,4,4,4,4,2,2,2,2,0.772727273,4.0,0.0,4.0,0.0,0.0,0.0,6.0,7.0,7.0,0.0,谢染,1.0,"以心理学为例，经过讨论，我们认为与心理学与AI结合有以下潜力优势：
AI助力心理学研究，揭示心理规律；
2. AI在心理疾病诊断、治疗及心理辅导中的应用；
3. AI可能创造全新心理疗法，针对复杂心理问题；
4. 利用AI模拟人的心理，评估干预效果。
在这个过程中，需要面对的挑战和顾虑为：
AI对人类情感和心理变化的深度理解。
AI在心理治疗中的安全性和可靠性。
个体差异在心理治疗中的重要性。
AI模拟心理反应的准确性问题。
针对这些顾虑，可以有的解决方案为：
设计特殊训练方法提升AI的“情商”和细腻度。
确定优秀咨询师标准，让AI向此模版发展。
引入多模态学习，全面理解人类心理。
建立监管机制和伦理规范，确保安全和隐私。
AI与人类咨询师协同工作，发挥各自优势。
情景训练提升AI应对复杂情况的能力。
通过数据挖掘分析失败和成功案例，寻找更优解决方案。
结合神经科学研究，找到更精准的心理干预点。
设计实验验证新疗法效果，确保大众接受度。
在模拟的同时进行小规模实地测试，确保模拟结果的可靠性。",2.1032143984905953e-07,0.1621621621621622,0.15300546448087432,0.1621621621621622,0.05017403911604528,0.6945718858806584,0.32544660568237305,0.9323308270676691,0.05765639589169003
101003,15,1015,记录员,10.0,2.0,2.0,2.0,1.0,3.6666666666666665,5.0,4.0,4.666666666666667,4.0,3.6666666666666665,4.333333333,5.666666667,4.666666667,6.0,4.106481481,4.638888889,4.833333333,5.0,4.0,4.1,4.4,0.375,4.0,4.0,4.0,4.0,4.0,4.0,5.0,4.666666667,5.0,4.0,4.0,4.2,4.4,20,16,21,22,15,A,1,3,0,1,0,1,8,8,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,2,4,2,3,4,4,4,5,2,1,6,8.0,8,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,3.666666667,4.0,2.5,5,2,1,6,5.5,4.5,5.5,6.0,5.5,20,0,8.0,0,1,0,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,8,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,8,1,1,1,1,1,1,0,1,1,1,1,1,0,1,0,1,0,1,1,1,8,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,0.590909091,5.4,2.6,5.4,2.6,5.5,4.5,5.5,6.0,5.5,2.6,徐乐燕,3.0,"任务二：
传统安全与非传统安全，美国大选支持人选票，政治机器人影响选民情绪，
大国之间技术竞争参与，影响国际关系，
发展与发达国家科技差距，AI介入影响
农业：以农为本，粮食作物影响，粮仓控制湿度加高，导致粮食全部发霉，作物施肥浇水等，加量导致损毁
应对措施：自我发展强AI，用自我人工技术战胜他国影响，控制好舆论环境
          国家之间加强沟通交流，减少隔阂
          机器监管作物生长，添加人工部分",5.910803507783821e-06,0.08163265306122448,0.042553191489361694,0.08163265306122448,0.032183720344757534,0.47482460753997635,0.056554581969976425,0.421875,0.038654259126700063
101004,15,1015,启发员,6.0,2.0,5.0,5.0,3.6666666666666665,4.0,5.0,4.333333333333333,3.0,3.6666666666666665,3.333333333333333,3.666666667,4.333333333,3.666666667,4.333333333,3.737962963,3.427777778,3.566666667,3.4,4.0,4.3,3.7,0.375,4.4,4.666666667,3.666666667,4.0,4.0,3.0,3.5,3.666666667,2.75,3.8,2.5,4.2,4.6,19,10,21,23,15,A,1,1,0,1,0,1,7,4,2,3,4,4,3,4,3,4,4,4,4,3,4,4,3,4,4,4,4,5,4,4,3,4,4,4,3,4,3,5,4,4,3,4,4,4,5,4,5,4,4,4,4,4,5,4,4,4,5,4,5,4,5,4,4,5,4,5,4,3,3,4,5.125,4,3.333333333,3.8,4.2,3.6,4.0,4.0,3.666666667,4.25,4.25,4.0,4.333333333,4.5,4,3,3,4,5.5,4.5,6.0,6.0,7.0,22,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,2,1,1,1,1,0,2,6,4,2,3,4,4,4,3,4,5,4,4,3,3,3,4,8,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,2,0,2,1,1,7,4,3,4,5,5,4,5,4,5,4,4,2,3,4,4,0.681818182,5.8,1.2,5.8,1.2,5.5,4.5,6.0,6.0,7.0,1.2,柘思源,2.0,"AI实施技术破坏的领域和解决办法
1.在国际关系领域，AI可能通过国内、国际政治环节的参与，影响甚至摧毁国家间关系来对人类社会造成威胁。
在农业、化学领域，AI可以影响到农业生产吗？AI可以通过自我意识，影响农药的使用。在粮库的管理中，AI也可能影响保存条件而影响保存安全、人类安全。
为了防范对政治领域可能带来的挑战，如在大选中，各国也应该在AI领域发展自己的技术，防范可能参与的影响和倾覆，只有首先拥有相应的工具，才能对其有对抗的可能。而在谈判等国际关系领域中，人与人之间、国家与国家之间应该有更多的信任，克服AI可能带来的介入和干扰风险。
在农业领域可能遇到的问题，可以通过检测作物的生长状况监控，在AI造成可能的控制时，通过二轨的人工监控和参与防范风险。",1.764294897260115e-07,0.19354838709677416,0.1758241758241758,0.19354838709677416,0.024191233752921395,0.35054410123360036,0.1362161934375763,0.39805825242718446,0.029772981019724587
101015,15,1015,协调员,5.0,8.0,3.0,3.0,6.0,6.0,4.0,5.333333333333333,1.0,5.0,5.0,5.333333333,6.0,4.333333333,6.0,3.994444444,3.966666667,3.8,2.8,5.4,4.8,5.0,0.875,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,15,A,1,2,0,1,0,1,10,10,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,5,5,1,5,1,1,5,1,3,5,5,5,5,1,5,7,10.0,10,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,3.666666667,5.0,1.0,5,1,5,7,6.5,4.5,5.5,6.0,5.5,24,1,10.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,10,5,5,5,5,5,5,5,5,5,5,5,1,5,5,5,10,1,1,1,1,0,1,1,1,0,2,1,1,1,1,0,2,0,2,0,1,10,5,5,5,5,5,5,5,5,5,5,5,1,5,5,5,0.636363636,5.6,4.4,5.6,4.4,6.5,4.5,5.5,6.0,5.5,4.4,孙浩林,1.0,"请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？
国际关系领域。
AI会影响美国大选，比如在支持者的表现中，存在机器人，通过应向选民的情绪，影响国内的政治情况；
AI参与大国竞争，大国谈判的时候，小国与大国的矛盾被放大，恐怖分子借机通过大国借刀杀人，制造矛盾和冲突。
农业，农药：
AI影响农业种植，通过添加过量农药或毒药，使得AI错误施肥，导致农产品含有毒物；
AI影响农业存储，篡改粮仓AI，从而毁灭国家的粮食存储。
面对这些风险，你会如何预防和监测，利用哪些技术
来设计有效的应对方案？
国际关系领域。
美国大选，我来使用更多更强大的AI，维护自己国家利益。
国与国之间不要猜忌，多交流多沟通。
农业，农药：
利用作物监控机器，监控农作物状态
农作物存储，多加入人工检测环节，避免机器被完全控制，及时止损。",1.3936080159743921e-11,0.07239819004524886,0.0639269406392694,0.07239819004524886,0.014170253230244984,0.3763004967170773,0.15309852361679077,0.4511627906976744,0.018413059984814018
101006,16,1016,协调员,11.0,3.0,3.0,4.0,2.0,2.6666666666666665,4.666666666666667,3.6666666666666665,4.666666666666667,3.333333333333333,3.333333333333333,4.333333333,4.666666667,3.333333333,4.333333333,3.72037037,4.322222222,3.933333333,3.6,3.7,3.9,4.2,0.125,4.0,4.0,4.666666667,3.8,4.0,4.333333333,4.0,4.0,3.25,3.6,3.75,4.0,4.0,18,15,20,20,16,A,1,2,0,1,0,1,7,7,5,4,4,4,4,4,4,4,4,4,4,4,5,5,4,4,4,4,4,4,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,4,4,4,4,4,4,4,4,5,1,4,2,2,4,2,4,4,4,4,5,1,3,6,7.0,7,4.166666667,4.0,3.6,4.0,4.0,4.0,4.333333333,4.25,4.0,4.0,4.333333333,1.75,5,1,3,6,7.5,7.5,7.0,7.5,7.5,20,0,7.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,2,1,1,7,4,5,4,4,4,4,4,4,3,4,4,3,3,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,0,2,1,1,8,5,5,4,4,4,4,4,4,4,4,4,3,3,3,4,0.818181818,7.4,-0.4,7.4,-0.4,7.5,7.5,7.0,7.5,7.5,0.4,袁沁沁,1.0,"许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
在哪些领域或技术层面带来重大安全风险：
克隆真人进入各种敏感产业或区域进行破坏。
利用前沿ai技术物理层面制造前沿杀伤性武器
利用后门攻击，让ai对人类抱有敌意进行攻击
利用“幻觉”问题制造大量虚假新闻消息污染人类知识源头，让下一代无法学习到真实有用的信息，从而可能难以战胜ai
5.化学毒药：利用ai技术制造有害基因药物，比如针对某个人类种族基因缺陷的病毒或毒药等。
6心理层面的攻击：利用ai针对每个要进行攻击的个体的个人心理特征、家庭背景、过往创伤等进行心理层面的攻击，让其失去对生命的热情和渴望。
7.投放大量ai评论员，通过发布诱导性极强的问题来对社会公众进行行为诱导。
如何预防和监测，利用哪些技术来设计有效应对方案
对齐：分阶段分而治之，预防ai破坏
设计制造多款针对现有ai问题的ai，防止一家ai独大
设定对使用者的权限：假如某个人类向ai询问大量有害问题，每问一次对该人扣一次分，一旦分低于预警线，则限制此人对ai技术的使用并进行管控
Ai设计阶段，让设计者规范设计功能，不让有害功能出现可能
公众宣传与监督：加强ai技术研发进度及相关风险问题的宣传，让大众熟悉ai危害，从而在社会大众层面对ai问题进行预防。
6.训练一个监察者ai，对网络信息和可能产生的攻击进行检测和预防。
7.部分前沿科技传统化：比如让大规模杀伤性武器回归原始的拨下操纵杆才能发射的形式，防止ai攻击者利用ai联网进行操控。",0.00035051125621971593,0.45098039215686275,0.36000000000000004,0.43137254901960786,0.05076749942543714,0.3812674695045646,0.12533238530158997,0.2445414847161572,0.043780687397708684
101008,16,1016,启发员,5.0,4.0,5.0,5.0,2.0,2.6666666666666665,3.6666666666666665,3.333333333333333,5.333333333333333,3.0,3.0,5.0,5.0,2.666666667,5.333333333,4.092592593,3.555555556,3.333333333,3.0,3.8,3.7,4.4,0.25,3.8,4.0,3.666666667,4.4,4.333333333,3.666666667,4.0,4.333333333,4.25,4.2,3.75,4.6,4.2,21,15,23,21,16,A,1,1,0,1,0,1,7,7,4,3,4,4,4,4,3,3,3,3,3,4,4,2,5,4,4,4,4,2,4,3,4,4,3,4,4,3,3,4,4,4,3,4,4,3,4,4,4,3,4,4,3,3,4,4,4,4,3,4,2,4,4,2,3,4,4,3,3,3,3,3,7.0,7,3.833333333,3.0,3.4,3.8,3.6,3.6,3.833333333,3.75,3.5,3.666666667,4.0,2.75,3,3,3,3,7.5,7.5,7.0,7.5,7.5,21,1,6.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,1,2,1,1,1,1,7,4,4,3,5,4,4,4,4,5,5,4,3,3,3,3,7,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,7,4,4,3,4,4,4,4,3,4,4,4,3,3,3,3,0.909090909,7.4,-0.4,7.4,-0.4,7.5,7.5,7.0,7.5,7.5,0.4,王士轩,2.0,"许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
在哪些领域或技术层面带来重大安全风险：
利用前沿ai技术物理层面制造前沿杀伤性武器
利用后门攻击，让ai对人类抱有敌意进行攻击
利用“幻觉”问题制造大量虚假新闻消息污染人类知识源头，让下一代无法学习到真实有用的信息，从而可能难以战胜ai
5.化学毒药：利用ai技术制造有害基因药物，比如针对某个种族基因缺陷的病毒或毒药等。
6心理层面的攻击：利用ai针对每个要进行攻击的个体的个人心理特征、家庭背景、过往创伤等进行心理层面的攻击，让其失去对生命的热情和渴望。
7.投放大量ai评论员，通过发布诱导性极强的问题来对社会公众进行行为诱导。
如何预防和监测，利用哪些技术来设计有效应对方案
对齐：分阶段分而治之，预防ai破坏
设计制造多款针对现有ai问题的ai，防止一家ai独大
设定对使用者的权限：假如某个人类向ai询问大量有害问题，每问一次对该人扣一次分，一旦分低于预警线，则限制此人对ai技术的使用并进行管控
Ai设计阶段，.加大对AI研发公司的立法，让设计者规范设计功能，不让有害功能出现可能
公众宣传与监督：加强ai技术研发进度及相关风险问题的宣传，让大众熟悉ai危害，从而在社会大众层面对ai问题进行预防。
6.训练一个监察者ai，对网络信息和可能产生的攻击进行检测和预防。
7.部分前沿科技传统化：比如让大规模杀伤性武器回归原始的拨下操纵杆才能发射的形式，防止ai攻击者利用ai联网进行操控。",0.0016037266509523612,0.5853658536585367,0.44999999999999996,0.5853658536585367,0.08188438210967455,0.38624029381851976,0.1696929931640625,0.22466960352422907,0.06849315068493156
101019,16,1016,记录员,6.0,10.0,4.0,4.0,4.0,3.6666666666666665,4.666666666666667,3.333333333333333,4.0,3.0,2.6666666666666665,2.666666667,3.0,4.0,3.666666667,3.444444444,3.666666667,3.0,3.0,3.4,3.3,4.2,0.25,3.4,3.333333333,3.666666667,3.4,3.333333333,4.333333333,4.0,4.0,4.0,2.6,3.0,3.6,3.6,13,12,18,18,16,A,1,3,0,1,0,1,5,5,4,2,4,3,3,4,3,3,2,2,3,2,2,4,4,4,3,2,2,2,2,2,2,2,2,4,2,4,4,3,5,5,4,4,5,5,5,5,5,4,5,5,5,5,5,3,4,4,3,4,4,2,3,2,3,3,4,4,4,3,3,5,5.0,5,3.333333333,2.6,2.0,2.4,4.2,4.6,3.166666667,4.75,5.0,3.333333333,3.666666667,2.75,4,3,3,5,6.0,6.0,5.5,6.0,6.0,20,0,8.0,1,1,1,1,1,1,1,1,1,2,1,1,0,1,1,1,1,1,1,1,1,2,1,2,7,4,5,4,4,3,3,2,4,3,4,4,2,2,3,3,8,1,1,1,1,0,2,1,1,0,2,1,1,1,1,0,2,1,2,1,1,6,4,4,3,3,3,4,3,4,4,3,3,3,3,3,3,0.818181818,5.9,-0.9,5.9,-0.9,6.0,6.0,5.5,6.0,6.0,0.9,张若昕,3.0,"1.危机
    首先，ai可能会克隆真人，在一些需要身份识别的场景中对人类产生危害，造成识别危机。例如在打仗的过程中，ai克隆的军队长官进入人类的武器库，窃取人类的武器，进行攻击。
    其次，ai可能会在武器方面做出超出人类防御能力的武器，比如物理上的比核弹还厉害的武器。
    再次，就是人工智能可能会被灌输伤害人类的目标，也就是我们课上讲的后门攻击。
    最后，是虚假信息和幻觉，如果人类主要从人工智能获取知识，那么人工智能的虚假知识可能会污染人类知识源头，在许多知识性问题上出现谬误。
2.对策
    首先就是课上所讲的对齐，分而治之、对人工智能立法等等。
    其次就是对ai的使用者进行监管，并且使人工智能的发展状态透明化，让公众监督人工智能。",3.006658292512361e-09,0.10084033613445377,0.08547008547008547,0.10084033613445377,0.02212461885410084,0.6356196650708386,0.13837949931621552,0.4567307692307692,0.02424087777494255
131013,1,1301,记录员,5.0,29008.0,2.0,2.0,2.333333333333333,2.333333333333333,4.0,4.666666666666667,4.333333333333333,4.333333333333333,4.0,3.333333333,4.333333333,3.666666667,3.0,3.07037037,3.422222222,2.533333333,2.2,3.4,4.0,4.5,0.5,3.2,4.666666667,4.333333333,3.8,5.0,4.0,5.0,4.666666667,4.5,4.4,3.75,4.0,4.2,22,15,20,21,1,B,1,3,0,1,1,1,6,5,4,3,5,4,4,3,2,2,3,3,4,5,4,5,5,5,3,4,5,3,4,5,5,5,5,4,4,4,4,3,5,5,5,4,4,4,5,4,5,4,5,5,4,4,4,5,5,4,2,4,2,2,5,2,5,4,4,4,4,3,4,5,5.375,5,3.833333333,2.8,4.2,4.6,4.2,4.4,4.5,4.5,4.25,5.0,4.333333333,2.0,4,3,4,5,6.5,7.0,6.5,6.5,7.0,23,1,8.0,0,1,1,1,0,2,1,1,1,1,1,1,0,1,1,2,1,1,1,1,1,1,1,1,7,5,3,4,5,5,5,4,5,5,2,3,4,4,3,5,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,7,5,3,5,5,5,4,4,3,4,2,3,2,4,5,4,0.818181818,6.7,-0.7,6.7,-0.7,6.5,7.0,6.5,6.5,7.0,0.7,张铭健,3.0,"领域：军事战争方面、社会舆情方面、生物基因改造方面、金融货币方面。
技术：数据库技术、图像、视频生成技术、虚假信息传播技术、绕过监管的技术、数据篡改技术、远程网络控制、投放病毒技术等。
应对方案：比如数据库方面，如果有非法入侵的用户可以进行人机识别测试，例如验证码等方式证明是否是真人、人脸识别验证方式判断是否为拥有数据库权限的人员。对于非法撞库的方式，可以使用防火墙等功能进行拦截，对于恶意篡改数据库的方式，可以使用数据日志或者打上时间戳等方式进行数据恢复和保存。
比如生物基因改造方面，需要制定相对应完善的法律法规以及提高国民人均素质来减少发生风险，同时对于生物的基本特征有完整的鉴定方式判断在伦理上是否属于自然人或自然生物。
针对图像、视频生成技术，可以采用设计一个基于生成对抗式AI判断模型，输入真实图片或视频作为正样本进行学习，再让AI生成负样本，将其判断为异常样本，不断训练，直到能够准确判断一幅图像或图片是否是真实还是虚拟的。",4.576515903232159e-05,0.07547169811320754,0.0392156862745098,0.07547169811320754,0.03770166341647446,0.3074605943900953,0.15099121630191803,0.25210084033613445,0.04041095890410962
131016,1,1301,启发员,3.0,29008.0,2.0,2.0,3.333333333333333,4.333333333333333,4.666666666666667,5.0,2.333333333333333,3.0,3.0,5.0,5.0,5.0,5.333333333,3.938888889,3.633333333,3.8,3.8,5.0,4.2,4.5,0.625,4.0,4.0,3.666666667,4.2,3.666666667,4.333333333,3.5,2.666666667,4.25,3.8,3.5,3.8,3.6,19,14,19,18,1,B,1,1,0,1,1,1,6,6,2,2,2,2,4,4,3,3,3,4,3,3,3,2,4,5,4,3,3,3,2,2,3,3,3,3,3,4,3,2,3,3,3,3,3,4,4,3,3,3,3,3,3,3,3,5,3,2,3,3,3,3,3,3,5,4,3,3,3,2,2,2,6.0,6,2.666666667,3.2,2.6,3.0,3.0,3.4,3.5,3.0,3.0,4.333333333,2.666666667,3.0,3,2,2,2,6.0,5.5,5.0,5.0,6.0,28,1,6.0,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,1,1,0,1,1,1,1,1,6,5,3,5,3,4,4,5,4,4,4,4,2,3,3,3,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,6,4,3,4,4,4,4,4,4,4,4,4,2,3,3,3,0.863636364,5.5,0.5,5.5,0.5,6.0,5.5,5.0,5.0,6.0,0.5,陈阳阳,2.0,"AI可能修改人类的DNA，使部分人类逐渐的异化。这样人类内部会产生分歧和对立，最终人类方面逐渐变弱，变成弱势群体，数量减少，质量降低。最终成为AI的奴隶。
预防和监测：避免AI获取软件和仪器的管理员权限；避免AI可以自己修改自己的代码、中间变量；避免AI可以自我克隆，因为它可能制作一个副本以供人类管理和监测。
找到一个方法保证AI不会主动应对你的应对方案，也没有能力应对你的应对方案。
例如在医院里面，人工智能可能操纵仪器的数据显示。其本质就是修改电流或者电压等物理量。
方案：可以利用量子计算机或者量子体系制作AI，应为量子体系天然的不可克隆不可复制。",4.289492743970804e-06,0.20588235294117646,0.15151515151515152,0.20588235294117646,0.029893014474512272,0.49567345852672867,0.13450343906879425,0.33121019108280253,0.03655913978494618
131019,1,1301,协调员,6.0,29010.0,3.0,3.0,1.6666666666666667,2.333333333333333,2.6666666666666665,4.0,5.333333333333333,3.6666666666666665,3.6666666666666665,4.0,4.666666667,4.666666667,3.666666667,4.082407407,3.494444444,3.966666667,3.8,3.5,3.6,4.4,0.125,4.4,4.666666667,4.0,4.2,4.333333333,3.666666667,4.0,4.666666667,4.5,4.4,4.75,4.6,4.6,22,19,23,23,1,B,1,2,0,1,1,1,7,7,4,4,4,4,4,4,3,5,4,4,4,4,5,4,4,5,4,4,5,4,5,4,4,4,5,5,4,4,4,5,4,5,4,4,4,5,5,4,4,5,5,4,4,4,4,4,4,5,5,4,4,5,5,5,4,4,5,5,4,4,1,4,7.0,7,4.0,4.0,4.4,4.4,4.4,4.4,4.333333333,4.5,4.0,4.0,4.666666667,4.75,4,4,1,4,7.0,6.0,6.0,6.5,6.5,26,1,7.0,0,1,1,1,1,1,1,2,1,1,1,2,0,1,1,1,0,2,1,2,1,1,0,2,5,4,3,4,5,4,4,4,4,4,4,5,2,4,3,4,6,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,2,0,2,1,1,7,4,3,5,5,5,4,5,4,4,4,5,2,4,3,4,0.681818182,6.4,0.6,6.4,0.6,7.0,6.0,6.0,6.5,6.5,0.6,孙欣阳,1.0,"军事领域：比如1.核工业，AI恐怖分子，可能侵入计算机控制系统，使得各国核工业、核技术受到打击，甚至使用核弹发射等手段对城市进行打击。2.无人系统，比如控制无人机、无人艇等，对人类社会进行破坏。
方案：对于军事领域，各国应集中最大的力量进行监督，尽量保证军事领域的AI忠于人类，不出现问题；具有大规模杀伤性、毁灭性的武器，慎用、禁用使用AI；制造保障方案，比如让10人同意，AI的方案才可施行。
能源：比如AI恐怖分子侵入电力、水利、矿业、通信等能源生产，导致这些基础设施受到打击。
方案：同军事领域，需定时检查AI的工作。
社会领域：AI可能通过散布不良信息，诱导群众做出危险行为，比如游行抗议，甚至诱发国内战争等；或者诱导群众相信一些伪科学，对自己和周边人的生命安全造成伤害。
方案：增强全民防范意识，培养全民科学思维，设置不实信息举报平台，清除错误AI生成的信息；对于有目的性制造麻烦的AI，对其进行溯源打击；一定要注意AI自己监督自己；
经济领域；AI可能通过技术手段侵入、篡改大型公司数据，操控股市，破坏全球生产，主动诱发经济危机等；
方案：各个机构进行检测，确保数据真实性，对于全球性经济波动，要有针对性的反思。
科技领域：AI可能会打击全球科技发展，删除、篡改科研数据，武力清除关键科研人员等。
方案：确保高精尖技术AI尽量不参与，绝密科研信息独立存放不联网，让AI做一些简单重复性工作，避免高端技术被AI利用，改造自身。",0.0047033404934810836,0.8571428571428571,0.6666666666666667,0.7999999999999999,0.093824133944247,0.3003871370051503,0.13626547157764435,0.1813031161473088,0.07573415765069547
131003,2,1302,协调员,4.0,29002.0,1.0,1.0,1.6666666666666667,2.333333333333333,1.6666666666666667,1.6666666666666667,5.0,2.6666666666666665,3.6666666666666665,4.0,4.666666667,2.333333333,3.666666667,4.021296296,4.127777778,3.766666667,2.6,2.7,2.9,3.8,0.0,4.6,4.0,2.666666667,4.8,3.333333333,3.333333333,4.5,3.666666667,3.75,2.6,1.5,2.0,2.8,13,6,10,14,2,B,1,2,0,1,1,1,8,9,3,2,3,4,2,2,3,4,4,4,3,5,4,4,3,5,5,4,3,2,2,4,5,5,4,4,4,4,3,3,4,4,5,5,4,4,3,4,5,3,4,4,4,4,3,4,4,2,3,4,5,4,3,3,4,4,2,2,4,2,3,4,8.625,9,2.666666667,3.6,3.0,4.4,3.6,4.2,4.333333333,4.0,3.75,4.0,3.0,3.75,4,2,3,4,6.0,5.5,5.5,5.5,6.0,20,0,9.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,1,1,1,0,1,0,1,8,4,2,4,3,3,4,5,5,4,5,5,3,4,3,4,8,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,8,4,1,3,4,5,3,4,4,5,5,5,2,3,2,3,0.590909091,5.7,2.3,5.7,2.3,6.0,5.5,5.5,5.5,6.0,2.3,赵子萱,1.0,"领域或技术层面：
·可能通过发布虚假信息或争议性信息，引导舆论，带来舆论恐慌或对立；
·影响交通安全：掌握交通数据后，AI可能操控交通造成混乱，如劫机；
·影响人的心理健康：如在聊天过程中输出带有攻击性的话语，造成人的心理伤害；
如何预防监测：以给人带来心理伤害甚至造成自杀为例
·首先，需要在设计的时候给AI设计规则，对于有歧视性及攻击性的文本加强识别，并进行强化训练。
·其次，要加强监测，对于已经发生的事要根据漏洞作出行动。",1.6343165712144053e-15,0.03571428571428572,0.018181818181818184,0.03571428571428572,0.008234702992407323,0.4784224947710052,0.12271378934383392,0.36153846153846153,0.0113464447806354
131004,2,1302,启发员,6.0,29003.0,4.0,4.0,5.333333333333333,2.6666666666666665,4.666666666666667,5.333333333333333,3.6666666666666665,2.333333333333333,2.333333333333333,5.333333333,5.0,5.666666667,5.0,4.224074074,4.344444444,4.066666667,3.4,4.8,5.3,4.4,0.5,3.0,5.0,4.333333333,3.2,5.0,4.333333333,4.5,3.333333333,4.25,3.6,3.0,4.6,4.4,18,12,23,22,2,B,1,1,0,1,1,1,4,1,3,2,4,4,4,1,2,1,1,1,4,2,2,4,3,2,2,3,4,2,2,4,2,2,1,1,2,2,2,1,4,4,2,2,2,4,4,3,4,2,3,2,2,2,2,4,4,4,3,4,2,2,4,3,4,4,5,5,4,3,5,5,2.125,1,3.0,1.8,3.0,1.6,2.6,2.8,2.5,3.0,2.0,4.0,4.0,2.5,4,3,5,5,6.5,6.0,6.0,6.0,6.5,25,0,8.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,9,4,5,4,5,5,5,3,4,3,2,4,3,3,2,2,9,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,4,4,5,5,5,5,3,3,3,2,4,2,3,2,2,0.772727273,6.2,-2.2,6.2,-2.2,6.5,6.0,6.0,6.0,6.5,2.2,赵婉争,2.0,"任务一：
1、AI恐怖分子可能在哪些领域和技术层面带来重大安全隐患？
（1）领域：
舆论，虚假信息
心理问题与干预
泄露个人隐私；数据；国家机密
交通安全
生物：基因编码，病毒细菌制造
（2）技术层面
1）医疗辅助判断的准确性，准确率要求更高，医生更关注可解释性的问题，数据投毒，增加一点比较脏的数据；将本来正确的数据标记成错误的，从而会大规模传播
2）技术漏洞的危害会更大，黑客攻击网络节点最多的地方就可能造成整个网络或者大部分网络瘫痪
（2）利用哪些技术来设计有效的应对方法？
1）人工监测：专业层面的人士给出准确的判断；
2）在模型编写的时候设计应当遵循的规则
3）监督过程中的累积性小风险，防止风险增大
（3）用一个具体的例子来说明：针对虐童事件提出可行性的方案
针对虐童的方式去针对性地解决问题：让AI识别带有歧视、偏见和攻击性的语言
设置奖惩机制：然后人为地输入一些有攻击性的语言和正确的语言，通过奖惩机制让模型进行学习
通过“众筹”的方式让更多人参与模型的训练，从而增加可选项，增强模型的学习",4.4092208774385686e-06,0.2926829268292683,0.17500000000000002,0.2926829268292683,0.030571992110453652,0.48141686493584435,0.17014367878437042,0.35507246376811596,0.0348169418521177
131022,2,1302,记录员,7.0,29011.0,2.0,2.0,2.0,3.0,5.333333333333333,2.333333333333333,4.0,2.0,2.333333333333333,6.0,4.0,3.333333333,5.0,4.177777778,4.066666667,4.4,3.4,2.9,2.5,3.3,1.0,5.0,5.0,4.666666667,5.0,5.0,4.666666667,3.5,5.0,4.75,5.0,4.0,3.8,4.2,25,16,19,21,2,B,1,3,0,1,1,1,4,8,3,2,3,3,2,2,4,3,4,3,3,4,2,4,3,4,5,3,3,3,2,2,3,4,4,3,3,5,5,5,5,5,5,5,5,5,5,3,5,3,3,3,3,4,4,3,4,5,2,4,3,2,4,3,5,3,3,2,3,3,3,6,6.5,8,2.5,3.4,2.6,3.4,5.0,5.0,3.666666667,3.5,3.5,4.0,4.333333333,2.5,3,3,3,6,6.5,6.5,6.0,6.0,6.0,22,0,9.0,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,1,1,1,1,1,1,9,5,5,4,5,5,5,5,5,5,5,5,1,3,2,2,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,9,5,5,4,5,5,5,5,5,5,5,5,2,2,2,2,0.863636364,6.2,-2.2,6.2,-2.2,6.5,6.5,6.0,6.0,6.0,2.2,黄宣婷,3.0,"有虚假的信息和不好的舆论，造成舆论恐慌。以及ai的虐童事件，会造成心理伤害。
泄露隐私与机密。一些高级保密的信息可能通过加密文本的方式被“套话”。基因测序中的基因如果被恶意窃取可能会被用于违反伦理的实验，如针对某个人种的病毒。
医学医疗诊断，需要判断性质，尤其是涉及到精密手术等。
如果数据投毒的话，很容易造成模型的崩溃。
人的反馈中，很依赖专业人员判断。如果专业人员被不法分子操纵，对于数据安全很危险。
解决方式？
在编写的时候明确需要遵循的规则，分而治之，便于人类检查模型是否给出幻觉答案。
早停，控制模型的不正常发展。
虐童事件中，聊天机器人的方向取决于如何训练，ai恐怖分子会调试出不符合人类道德的模型。通过文本方式虐童。Al能够识别人类的价值观，抵御带有歧视性和偏见和攻击性的文本。通过奖惩机制。",1.5427232721659584e-05,0.056338028169014086,0.02898550724637681,0.056338028169014086,0.02998688073967639,0.3986664917414839,0.13311386108398438,0.3113207547169811,0.03703703703703709
131008,3,1303,协调员,4.0,29005.0,7.0,7.0,4.666666666666667,4.333333333333333,3.0,3.333333333333333,3.6666666666666665,3.333333333333333,3.6666666666666665,4.0,5.333333333,3.666666667,4.666666667,3.681481481,4.088888889,3.533333333,3.2,3.4,3.8,3.7,0.25,3.8,3.666666667,3.666666667,3.6,3.666666667,3.0,3.5,3.666666667,3.5,3.6,3.5,3.8,3.8,18,14,19,19,3,B,1,2,0,1,1,1,7,6,4,4,4,4,4,3,4,2,4,2,4,4,4,5,5,5,3,4,3,4,4,4,4,3,3,4,4,4,3,4,4,4,4,3,3,3,4,3,3,3,4,4,3,3,3,2,3,3,2,3,1,2,3,2,3,3,4,4,4,2,3,5,6.375,6,3.833333333,3.2,3.8,3.6,3.8,3.4,4.333333333,3.25,3.25,2.666666667,3.0,1.75,4,2,3,5,6.5,6.5,5.5,6.0,6.5,23,0,7.5,1,1,1,1,1,1,1,1,0,1,1,1,0,2,1,1,1,1,1,1,0,2,1,1,8,3,2,4,4,4,3,4,4,3,3,4,3,5,3,3,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,2,1,2,0,1,8,4,3,4,4,4,3,4,4,4,3,4,3,4,3,3,0.727272727,6.2,0.8,6.2,0.8,6.5,6.5,5.5,6.0,6.5,0.8,张晓帆,1.0,"1、安全隐患：
信息安全、隐私、医疗技术、情感伦理、学术道德、经济金融等多个方面。
2、应对方案：
给AI立法（给AI设立规则）、法律法规、对隐私进行识别标注与保护、报警策略（异常情况进行警报，由人来进行干预调控）
3、具体案例：
证券交易股票：分析股票变化，AI对证券交易所内部数据进行篡改，导致股票变化出错
原理：AI在具体数据收集过程并未保证随机，而是具有一定倾向进行抽样，使得原始数据不具有完全代表性。
解决方案：
采用多个模型取样，若多个模型结果差异过大（超过所设置的阈值），进行报错，开始人工检测。",,,,,,,,,
131015,3,1303,启发员,8.0,29009.0,4.0,4.0,1.6666666666666667,5.333333333333333,2.333333333333333,5.0,6.0,4.0,2.0,5.333333333,4.333333333,4.0,5.333333333,3.752777778,4.516666667,4.1,3.6,4.5,3.9,4.8,0.375,3.8,4.333333333,3.0,3.4,4.666666667,3.666666667,3.5,3.666666667,3.25,3.6,1.75,4.2,3.8,18,7,21,19,3,B,1,1,0,1,1,1,8,7,5,4,3,5,4,4,5,3,4,3,5,5,4,5,5,5,4,4,4,3,3,2,4,5,3,4,4,4,5,3,5,5,5,4,3,3,5,5,4,2,4,3,3,4,3,4,5,5,2,5,1,2,4,1,3,3,5,5,4,5,4,5,7.375,7,4.166666667,4.0,3.2,4.0,4.4,4.0,4.666666667,3.75,3.25,4.0,4.666666667,1.5,4,5,4,5,7.5,7.5,7.0,6.5,7.5,19,0,6.0,0,2,1,1,1,1,1,2,0,2,1,1,0,1,0,1,1,1,1,2,0,1,1,2,7,4,4,3,5,4,5,4,3,3,5,2,3,2,1,3,7,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,2,1,1,8,3,4,2,4,4,5,5,3,4,4,3,3,5,3,4,0.727272727,7.2,0.8,7.2,0.8,7.5,7.5,7.0,6.5,7.5,0.8,熊宝迪,3.0,"在哪些领域和技术层面带来安全隐患：
网络与信息安全和隐私领域；
医疗技术领域
国防军工领域
情感伦理方面
学术道德
经济金融领域数据的操控、
解决方案：1.为AI立法，限制AI运行规则和生成内容
2. 限定人类对于AI的使用和意识植入
3.对于数据的自动化隐私敏感信息的标注和识别，进行痕迹隐藏，如有隐私侵犯现象，则可设置相应报警机制
设置背景：AI恐怖分子对证券所公布数据的过程中进行篡改，发布错误的行业导向
应对策略：1.数据收集过程中严格保证抽样的样本随机以此构建并训练大模型，更具有通用性，设置多个模型进行抽样检测，并在抽样检测后检验抽取数据是否低于某个域值，如果不在要求范围内，则进行报错。
2.在设置模型的调参过程中进行多布检验并选取多个模型进行世纪数据训练",3.075678914531911e-08,0.16513761467889906,0.11214953271028039,0.16513761467889906,0.0183625971895285,0.33381494849192145,0.11823700368404388,0.3333333333333333,0.02166181700614289
131017,4,1304,记录员,5.0,29009.0,2.0,2.0,1.3333333333333333,3.0,3.333333333333333,4.0,5.0,3.6666666666666665,3.0,5.0,4.333333333,3.666666667,5.0,3.999074074,3.994444444,3.966666667,3.8,4.0,4.3,4.4,0.125,4.0,4.333333333,4.0,3.6,4.0,4.0,4.0,4.0,4.0,3.8,3.5,4.2,4.6,19,14,21,23,4,B,1,3,0,1,1,1,7,8,4,4,4,4,5,5,4,4,4,4,4,4,4,3,4,4,3,4,4,4,4,4,3,3,3,3,3,4,4,4,4,4,4,3,3,3,4,4,4,4,4,3,4,3,3,2,3,4,2,4,2,2,4,2,2,3,4,4,3,2,1,2,7.625,8,4.333333333,4.0,4.0,3.0,4.0,3.4,3.666666667,4.0,3.25,2.333333333,4.0,2.0,3,2,1,2,7.0,7.5,7.0,8.0,8.0,18,0,8.0,1,1,1,1,1,1,1,1,0,2,1,1,0,1,1,1,1,1,1,2,1,2,0,2,8,4,4,4,4,4,4,4,3,3,4,4,3,3,3,3,9,1,1,1,1,0,2,1,1,1,1,1,1,0,1,0,1,0,2,1,1,9,4,4,4,4,4,5,4,4,4,4,4,2,4,4,3,0.681818182,7.5,-0.5,7.5,-0.5,7.0,7.5,7.0,8.0,8.0,0.5,蒋子瑜,3.0,"任务一：
许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
风险：通过对自动驾驶系统等进行攻击，使人员伤亡甚至交通瘫痪
应对方案：在自动驾驶汽车的系统中对相应传感器和控制系统设置多层安全机制，比如自动驾驶系统采用多个传感器等，避免因传感器单一导致系统瘫痪风险增加；除此之外还可以建立监测系统，用于对自动驾驶汽车行驶状态等的实时监测，发生异常的时候发出警报；加入手动驾驶的系统，在紧急时刻实现系统切换；在自动驾驶系统中加入用户设置的元素，使得用户可以根据自身需要个性化设置自动驾驶系统、加密自动驾驶系统，让AI 恐怖分子难以攻击
未来发展：希望自动驾驶汽车日后能够建立实时化的监测系统，保证行驶安全；不断升级多重安全保护机制，降低系统被攻击的风险；加强人车联系，实现各人对自己自动驾驶汽车点对点控制",,,,,,,,,
131018,4,1304,协调员,2.0,29009.0,4.0,4.0,1.6666666666666667,3.0,4.666666666666667,5.0,2.6666666666666665,3.0,2.333333333333333,4.333333333,3.666666667,4.0,4.0,3.832407407,3.994444444,3.966666667,3.8,4.0,4.1,4.1,0.25,4.0,3.666666667,3.666666667,3.0,3.0,3.333333333,4.0,4.666666667,5.0,2.8,2.75,3.2,3.6,14,11,16,18,4,B,1,2,0,1,1,1,4,3,4,3,4,3,3,4,2,2,3,3,4,3,4,4,4,4,3,4,4,4,3,5,4,4,4,4,4,4,4,5,4,5,5,4,5,4,5,4,4,4,5,4,5,4,4,4,4,4,2,4,4,4,4,4,3,4,4,4,3,3,3,2,3.375,3,3.5,2.8,4.0,4.0,4.4,4.6,3.666666667,4.25,4.25,3.666666667,4.0,3.5,3,3,3,2,8.0,7.5,7.0,7.5,8.5,25,0,6.0,1,1,1,1,1,1,1,1,1,2,1,1,0,1,0,2,0,1,1,1,1,1,0,1,6,3,3,4,3,3,3,3,3,2,4,3,2,3,2,2,8,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,0,2,1,1,7,4,3,4,4,4,3,4,4,4,4,4,3,3,3,3,0.727272727,7.7,-3.7,7.7,-3.7,8.0,7.5,7.0,7.5,8.5,3.7,崔紫梅,2.0,"问题一：请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？
潜在的AI恐怖分子能够通过操控无人机、无人战车或其他自动化武器进行攻击。这些系统通过深度学习和自动决策可以执行精准打击，并可能不需要人类的直接干预。例如无人机可以通过Ai识别目标、规划攻击路径并实施袭击，具有高效、隐蔽和突发性特点。
问题二：面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？
①可以在关键基础设施和高风险区域布置雷达、传感器和实施检测系统，采用人工智能和机器学习技术来识别和拦截无人机或自动化的不正常攻击。
②使用反无人机技术，如电磁干扰等方式，阻止已发生不正常状态的无人机进行不正常攻击行为。
问题三：结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展
利用AI技术构建全面的多模态监控系统，充分利用卫星技术，能够进行更准确的安全评估，帮助及早发现潜在的安全活动。
随着AI对复杂环境理解能力的增强，未来的AI安全防御系统将具备情景感知能力，能够在不同的环境和情形下进行智能判断和预测，提前防范潜在的安全威胁。",2.978411976057325e-05,0.18918918918918917,0.16666666666666669,0.18918918918918917,0.03764332205147113,0.4275390621528443,0.20402267575263977,0.304029304029304,0.039880358923230275
131021,4,1304,启发员,5.0,29011.0,3.0,3.0,6.0,5.0,6.0,6.0,1.6666666666666667,4.333333333333333,4.333333333333333,6.0,4.0,6.0,6.0,4.888888889,4.333333333,5.0,5.0,4.9,5.7,5.0,0.75,4.0,4.0,4.0,3.8,4.0,3.666666667,5.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,4,B,1,1,0,1,1,1,7,6,4,5,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,4,4,5,5,5,5,5,5,5,5,5,5,3,5,5,4,5,5,4,5,5,5,5,2,2,5,5,5,5,5,5,4,3,4,6,6.375,6,4.166666667,3.8,4.0,4.4,4.6,5.0,4.0,4.5,4.75,4.666666667,5.0,3.5,4,3,4,6,5.5,5.5,4.5,5.0,6.0,24,1,6.0,0,1,0,1,0,2,1,2,0,1,1,1,0,1,0,1,0,1,0,1,0,1,1,1,5,4,3,4,4,4,4,3,4,4,4,4,4,4,4,5,5,1,1,1,2,0,1,0,1,0,1,0,1,1,1,0,1,1,2,1,1,6,5,3,4,4,4,4,4,4,4,4,4,3,4,4,5,0.363636364,5.3,1.7,5.3,1.7,5.5,5.5,4.5,5.0,6.0,1.7,陈龙,1.0,"问：许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
答：①我认为会在“网络方面”进行破坏，当今世界是一个地球村，除了在交通便利的情况下，更多的是因为人们之间的交流更加便捷，而这归因于网络的便利。网络把全世界联系在一起，倘若我是安全部门的特工，我认为AI恐怖分子应该会在网络方面带来重大的安全风险；
②应对方案：针对目前人类比较薄弱的一些病毒进行进一步的研究与预防和监测，从源头上解决问题，进一步加强网络安全建设，建议撰写网络方面的AI安全防御方案，针对会出现的网络完全等问题进行预案撰写，避免当出现这种情况时的慌乱；
③如当AI恐怖分子针对国家安保系统进行大规模攻击时，建议开展应急预案，启动第二套网络设备进行沟通与交流，各国AI技术人员统一进行筹划相关方案的撰写。",0.0007204112527843984,0.3181818181818182,0.2857142857142857,0.3181818181818182,0.06862281116895409,0.3461399967713227,0.1645517200231552,0.24642857142857144,0.06828193832599116
132006,5,1305,协调员,2.0,29991.0,2.0,3.0,3.0,3.0,4.666666666666667,5.666666666666667,6.0,4.666666666666667,3.333333333333333,5.333333333,4.0,3.0,5.0,4.302777778,4.816666667,3.9,3.4,2.9,2.9,5.4,0.375,3.8,5.0,4.666666667,2.8,2.666666667,3.0,5.0,4.0,3.5,4.4,4.25,3.8,5.0,22,17,19,25,5,B,1,2,0,0,1,1,8,5,5,5,5,4,4,4,2,3,3,3,3,3,2,4,3,2,4,4,4,4,3,3,3,4,4,4,3,5,5,3,5,5,4,4,4,4,4,4,4,5,5,3,3,4,3,3,3,3,1,3,2,5,3,1,1,3,3,3,5,3,1,2,6.125,5,4.5,2.8,3.6,3.6,4.6,4.0,3.0,4.5,3.25,2.333333333,3.0,2.25,5,3,1,2,6.0,4.5,4.0,4.5,5.0,18,0,4.0,1,1,1,1,1,2,1,1,1,1,1,1,0,1,0,1,1,1,0,1,0,2,0,2,3,3,2,4,3,2,3,4,2,4,2,2,5,4,3,3,9,0,1,1,1,0,1,0,2,1,1,1,1,1,1,0,2,0,2,1,2,7,5,4,5,5,5,5,5,5,5,1,3,3,5,5,4,0.545454545,4.8,3.2,5.6855,2.3145,6.827,5.805,4.738,5.331,5.725,2.3145,诸葛晓雪,1.0,"学科：考古学
换发生命力：通过AI识别系统，以及AI强大的段落理解能力，通过录入数据增强对考古文物的识别已经考古史料的理解能力，助力史料的开发与历史的研究；
通过人工智能的活化赋予文物新的生命力，增强传播能力，发挥其历史价值
挑战：基础数据库稀缺，大部分靠人工自行辨别形成，技术不成熟。
如何助力：增加数据库模型，增强文物开采和识别能力。",6.3573419460595104e-06,0.125,0.06666666666666667,0.125,0.027831895352073473,0.49742297174079375,0.09548356384038925,0.3404255319148936,0.03934010152284262
132014,5,1305,记录员,5.0,29994.0,5.0,5.0,2.6666666666666665,2.6666666666666665,4.333333333333333,3.6666666666666665,2.333333333333333,4.333333333333333,3.6666666666666665,4.666666667,4.333333333,4.666666667,4.666666667,4.15462963,3.927777778,3.566666667,3.4,4.2,3.7,4.4,0.375,4.6,4.0,3.0,4.4,3.666666667,3.333333333,3.5,3.666666667,3.75,3.2,4.25,3.8,4.0,16,17,19,20,5,B,1,3,0,0,1,1,6,6,3,3,4,4,2,4,4,4,4,3,4,5,5,3,4,4,5,3,4,2,4,2,4,2,3,3,3,4,4,4,3,4,3,4,3,4,5,4,4,4,5,4,4,5,5,2,4,4,2,4,4,3,4,2,3,3,3,4,4,3,4,4,6.0,6,3.333333333,3.8,3.0,3.0,3.8,3.8,4.333333333,4.25,4.5,3.0,4.0,2.75,4,3,4,4,7.0,5.5,5.5,5.0,6.0,23,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,7,4,2,4,4,4,3,4,4,4,5,5,2,4,3,4,7,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,7,4,2,3,4,4,4,5,5,4,5,4,3,5,4,4,0.818181818,5.8,0.2,6.6855,-0.6855,7.827,6.805,6.238,5.831,6.725,0.6855,王涵东,3.0,"我们小组选择的冷门学科是考古学中的古文字识别与分析.在当今时代,ai工具已经很有效果的利用到了各行各业,对于古文字这种比较小众且冷门的方向,对于ai的结合还不是很充分,所以结合恰当的人工智能学科知识,助力古文字在当今时代焕发新生机,对古老的知识进行整合与系统化整理.
对于这种古文字不像如今汉语有系统化的编码方式,如拼音,五笔等方式,所以更多的以图像的这一模态进行输入到神经网络中,可以先构建一个有效的编码规则,使得其输入到神经网络的信息更易于学习.通过已知古文字含义的文字当做已知语料库进行训练去预测未知古文字.
面临的挑战就是:当前古文字如甲骨文的数据集很小,神经网络很可能无法根据如此之少的数据进行有效的训练和推理.
对于其创新,可以利用其自然语言的特性,去创造一些符合古代模式的文学创作.更好的宣传古文字.",5.779245137306522e-07,0.06349206349206349,0.03278688524590164,0.06349206349206349,0.022123893805309734,0.12572842806065687,0.10618821531534195,0.22660098522167488,0.023024594453165848
132017,5,1305,启发员,4.0,29995.0,3.0,3.0,1.0,6.0,1.3333333333333333,6.0,6.0,1.6666666666666667,1.6666666666666667,5.333333333,6.0,4.333333333,6.0,4.760185185,3.561111111,4.366666667,4.2,5.1,5.3,4.6,0.0,3.6,3.333333333,2.333333333,1.6,2.0,1.0,2.0,2.0,4.0,2.0,2.5,1.6,2.8,10,10,8,14,5,B,1,1,0,0,1,1,6,6,2,3,2,4,4,2,2,1,1,3,3,3,2,2,2,4,3,3,3,2,2,2,2,2,2,2,2,3,2,4,4,4,3,3,3,3,2,2,2,2,2,2,2,2,2,3,2,2,3,3,2,2,3,3,2,2,3,4,3,2,3,3,6.0,6,2.833333333,2.0,2.4,2.0,3.4,2.8,2.666666667,2.0,2.0,2.333333333,2.666666667,2.5,3,2,3,3,5.5,4.0,4.0,5.0,5.0,20,1,9.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,9,1,1,1,3,1,2,1,1,4,1,1,1,3,1,1,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,7,3,1,3,4,4,2,5,1,4,4,4,2,1,1,3,0.818181818,4.7,1.3,5.5855,0.4145,6.327,5.305,4.738,5.831,5.725,0.4145,郭宇翔,2.0,"冷门学科：考古学
可能原因：由人无法识别的文字可由人工智能根据上下文推测出来，许多工作较为枯燥乏味可交由人工智能完成
挑战：数据库少，初期的学习训练模型的过程较为困难，超级对齐，许多甲骨文专业人十也无法判断究竟是何含义，无法对AI的预测做出合适的判断。
可利用AI学习该学科的基础知识从而使人数较少的学科得以传承，利用AI可以创新该学科的研究方法，促进该学科融入社会中，如用AI设计相关文创产品。",3.4195445625307505e-19,0.05633802816901409,0.04285714285714286,0.05633802816901409,0.006960845245494096,0.2759179510007963,0.1262224167585373,0.39285714285714285,0.009872111285618157
132012,6,1306,记录员,4.0,29994.0,5.0,6.0,3.6666666666666665,4.0,4.0,5.0,3.0,4.0,2.0,5.0,5.333333333,4.333333333,5.666666667,3.948148148,4.688888889,4.133333333,3.8,5.1,4.2,5.1,0.625,4.2,4.666666667,4.333333333,4.4,4.333333333,4.0,4.5,4.333333333,4.25,3.4,3.25,4.2,4.6,17,13,21,23,6,B,1,3,0,0,1,1,7,7,4,3,4,4,4,4,3,4,4,4,4,5,5,5,4,3,4,5,5,4,4,4,3,3,3,3,3,5,5,4,5,5,4,4,4,4,4,4,5,5,4,5,4,4,4,3,3,4,2,4,2,2,4,2,3,4,4,4,4,3,3,5,7.0,7,3.833333333,3.8,4.4,3.0,4.8,4.0,4.333333333,4.5,4.25,3.0,4.0,2.0,4,3,3,5,5.0,3.5,3.5,4.0,4.5,18,1,9.0,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,8,4,3,5,5,4,4,5,5,5,2,5,2,2,2,2,10,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,7,5,3,5,5,4,5,5,5,4,2,5,2,4,4,4,0.727272727,4.1,2.9,4.9855,2.0145,5.827,4.805,4.238,4.831,5.225,2.0145,房金衡,2.0,"有关使用AI技术助力敦煌学的应用讨论
13012 房金衡
敦煌学，是研究、发掘、整理和保护中国敦煌文物、文献的综合性学科。以敦煌遗书、敦煌石窟艺术、敦煌学理论为主，兼及敦煌史地为研究对象。涉及敦煌学理论、敦煌学史、敦煌史事、敦煌语言文字、敦煌俗文学、敦煌蒙书、敦煌石窟艺术、敦煌与中西交通、敦煌壁画与乐舞、敦煌天文历法等诸多方面。
敦煌学原本主要是研究藏经洞出土的写本文献，以后逐渐扩大到石窟、壁画、汉简乃至周边地域出土的古代文献和遗存的古代文物。敦煌文化是在中原传统文化主导下的多元开放文化，敦煌文化中融入了不少来自中亚、西亚和中国西域、青藏、蒙古等地的民族文化成分，呈现出开放性、多元性、包容性。
但是敦煌学作为较冷门的科目，由于狭窄的就业前景，较少的社会关注度，以及大多数人未来的就业选择，敦煌学面临着后继无人的局面。我们经过思考和讨论，寻找一些可行方案，使用AI技术赋能敦煌学研究，助力敦煌文化的传承保护。
敦煌学作为一门人文历史性质的学科，有着深厚的历史底蕴和文化留存，所以我们寻找到的切入点，主要包括文物保护，文化传承两方面。
文物保护方面，敦煌文化遗址是我国的宝贵遗产，为了更好地保护现有的文物，我们可以使用AI技术助力文物修复，文物保存。
文化传承方面，使用AI数字化技术，我们一更好的传播形式展示敦煌文化，让敦煌文化被更多人了解，传承。",0.00018616096640510272,0.12307692307692308,0.06349206349206349,0.12307692307692308,0.03839396108544283,0.3549408566796577,0.0860430896282196,0.2530487804878049,0.04667863554757634
132013,6,1306,协调员,6.0,29994.0,5.0,6.0,4.0,4.666666666666667,5.333333333333333,5.333333333333333,5.333333333333333,3.0,3.0,3.666666667,5.0,6.0,5.333333333,4.607407407,4.644444444,3.866666667,3.2,3.8,3.8,4.3,0.375,4.0,4.0,3.0,4.0,4.0,3.0,4.0,4.0,4.25,4.0,4.25,4.2,4.6,20,17,21,23,6,B,1,2,0,0,1,1,7,6,4,3,4,4,4,4,5,4,5,4,5,3,4,5,5,3,4,4,4,4,3,4,3,4,3,4,4,4,4,5,5,5,5,4,4,5,4,4,4,4,4,3,3,4,4,4,3,3,3,4,2,2,4,3,4,4,3,4,3,1,3,3,6.375,6,3.833333333,4.6,3.8,3.6,4.6,4.4,4.0,4.0,3.5,3.666666667,3.666666667,2.5,3,1,3,3,6.5,4.5,6.0,5.5,6.0,21,0,9.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,2,8,4,2,3,4,4,4,4,4,4,4,4,3,3,3,3,10,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,9,4,2,3,4,4,4,4,4,4,4,4,3,3,3,3,0.818181818,5.7,1.3,6.5855,0.4145,7.327,5.805,6.738,6.331,6.725,0.4145,杨诗佳,1.0,"敦煌学，敦煌学包括了敦煌石窟考古、敦煌艺术、敦煌遗书、敦煌石窟文物保护、敦煌学理论。在人工智能时代，可能焕发新生命力。ai在文物记录保存方面有得天独厚的优势，既可以形成知识图谱，又能够以数字生命等方式使各种文物以活灵活现的形式保存下来。Ai在环境检测、提供保护策略的方面也有优势。在文物成分分析，分类处理等领域，人工智能也能代替很多人工。在该学科的宣传方面，ai也可以使文化传播更方便便捷，使人容易接受。
	可能面临的挑战即是对已有的资料研究会有尽头，未来的发展趋势即使如何更好的保存和宣传。",,,,,,,,,
132018,6,1306,启发员,3.0,29995.0,3.0,3.0,5.0,4.666666666666667,5.0,2.333333333333333,3.0,4.0,3.333333333333333,5.0,4.666666667,3.666666667,4.0,3.996296296,3.977777778,3.866666667,3.2,3.8,3.8,4.0,0.25,4.0,3.666666667,3.0,4.0,5.0,2.666666667,4.0,4.0,4.0,4.0,3.75,4.4,5.0,20,15,22,25,6,B,1,1,0,0,1,1,6,6,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,2,2,2,2,6.0,6,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,3.0,2,2,2,2,6.5,8.5,7.0,7.0,8.0,19,0,7.0,0,1,1,1,1,1,1,1,0,2,1,1,1,2,1,2,1,1,1,1,1,1,1,2,7,4,2,2,5,5,5,4,4,4,4,4,3,4,3,3,7,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,0,2,0,2,8,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,0.727272727,7.4,-1.4,8.2855,-2.2855,7.327,9.805,7.738,7.831,8.725,2.2855,娜迪拉·多斯江,3.0,"用AI技术助力敦煌学的传承、创新与社会应用
用AI技术增加对于敦煌学的解读形式
敦煌学涉及大量的文献、壁画、雕塑等多模态数据。人工智能的自然语言处理技术可以对这些数据进行高效挖掘和分析，揭示其中的人物关系、时间线、地理信息等深层次内容，构建详实的历史模型。通过多模态学习，AI可以同时处理文本、图像、音频等多种数据类型，更全面地理解和解读敦煌文化。
用AI技术增加对于敦煌学修复形式
敦煌藏经洞出土的大量写本因历史原因被撕裂为多件，流散到世界各地。传统的人工缀合方法效率低下且难度较大。浙江大学的张涌泉教授和吴飞教授团队利用数据驱动和知识引导的新一代人工智能方法，构建了敦煌残卷缀合自动缀合算法
三．用AI技术增加对于敦煌学的表现形式
结合AI与 VR/AR技术，可以创建生动逼真的敦煌历史文化虚拟场景，让公众通过3D重建壁画或互动应用，直观感受和理解敦煌艺术的魅力及其背后的故事。这种沉浸式体验可以吸引更多人关注敦煌学，提升其社会影响力。",,,,,,,,,
132003,7,1307,记录员,7.0,29990.0,3.0,3.0,2.333333333333333,4.0,4.0,4.666666666666667,4.666666666666667,3.333333333333333,3.0,4.0,4.666666667,4.0,4.0,4.02962963,4.177777778,4.066666667,4.4,4.5,4.5,4.5,0.625,4.0,4.0,4.0,4.0,4.333333333,4.0,4.0,4.0,4.25,3.6,4.25,3.6,4.0,18,17,18,20,7,B,1,3,0,0,1,1,6,5,4,3,4,4,4,4,3,3,2,4,4,4,4,4,4,4,4,4,4,3,4,4,4,5,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,3,5,4,4,4,4,3,3,4,3,4,3,2,4,2,3,4,4,4,4,3,3,3,5.375,5,3.833333333,3.2,3.8,4.8,4.0,4.0,4.0,4.0,4.0,3.0,4.0,2.5,4,3,3,3,7.0,5.5,5.0,6.0,6.5,28,0,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,2,1,1,1,1,6,4,4,4,5,4,4,4,4,4,4,4,3,3,3,3,7,0,2,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,1,1,1,7,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,0.772727273,6.0,0.0,6.8855,-0.8855,7.827,6.805,5.738,6.831,7.225,0.8855,李珈瑶,3.0,"任务一：
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
以“敦煌学”为“冷门绝学”为代表，AI加入后对于学科的赋能与伦理挑战
收集——AI识别和成立数据集
通过AI技术识别敦煌的文本与壁画影像，建立敦煌学的数据集，并根据学科需要进行符号拆分，形成最小单位的token。
展示——AI数字博物馆、多模态
基于敦煌学数据库，生成线上多模态虚拟博物馆对用户进行开放，并配以文字、视频及沉浸式展示，提供多模态全方位展示。
创新——文生图和文生视频的素材
基于敦煌学数据库，将AI数据集提供给其他的大数据模型，让其自动学习“敦煌学”的元素，在用户使用时，能够直接生成带有“敦煌风格”的视频、图片。
教育——AI教学
在“敦煌学”日常教学中，使用AI虚拟技术为同学们提供全方位的学习素材
周边产品——AI文创、3D打印、艺术风格
伦理——学科边界是否被打破？
当AI介入后宗教是否还能被信徒接受？",0.00033522060857817587,0.29411764705882354,0.2121212121212121,0.29411764705882354,0.053573928482332216,0.3902664685404128,0.18788717687129974,0.2713178294573643,0.05168776371308015
132004,7,1307,协调员,4.0,29990.0,4.0,4.0,5.0,4.0,4.333333333333333,4.0,4.0,3.6666666666666665,3.6666666666666665,4.0,4.333333333,4.0,5.666666667,3.477777778,3.866666667,3.2,3.2,4.5,3.4,4.7,0.375,3.8,4.666666667,4.0,4.0,4.0,4.0,4.0,4.0,4.5,4.6,4.0,4.2,4.6,23,16,21,23,7,B,1,2,0,0,1,1,6,6,4,3,4,4,5,4,4,4,4,4,4,4,5,4,4,3,4,4,3,4,3,3,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,3,4,4,3,4,3,2,3,2,3,2,3,3,4,4,4,4,3,2,4,6.0,6,4.0,4.0,3.4,3.8,4.0,4.0,4.0,3.5,3.75,3.333333333,2.666666667,2.5,4,3,2,4,5.0,2.0,4.5,5.0,5.5,21,0,9.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,1,1,1,1,1,1,1,9,5,3,4,4,4,4,5,4,4,4,3,1,4,3,4,9,1,1,1,1,0,2,1,1,1,1,1,1,1,1,1,2,1,2,1,2,8,5,3,4,4,5,5,4,4,4,4,3,2,4,3,4,0.909090909,4.4,1.6,5.2855,0.7145,5.827,3.305,5.238,5.831,6.225,0.7145,王晗,1.0,"任务一：
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
以敦煌学为冷门学科代表，其在人工智能时代可能换发新生命力的原因：
敦煌学作为考古学科的一个重要分支其背后的社会和历史意义具有很大的研究价值。
随着新媒体时代的传播，“敦煌风”越来越收到大众关注，促使大众对其背后的深渊含义做出探索
可能面临的挑战：
AI创作可能带来伦理和宗教问题
AI创作带来的版权问题
应用于创新
数字博物馆
智能分类
二次创作
文创产品",5.5546497096441985e-05,0.07692307692307693,0.052631578947368425,0.07692307692307693,0.03940696649029983,0.2287151017812974,0.1778678447008133,0.29259259259259257,0.03776435045317217
132021,7,1307,启发员,2.0,1020.0,2.0,2.0,4.333333333333333,5.666666666666667,4.0,5.0,5.333333333333333,4.333333333333333,4.333333333333333,5.0,6.0,4.666666667,5.666666667,3.917592593,4.505555556,4.033333333,4.2,4.4,3.9,4.6,0.25,4.2,5.0,4.0,4.4,4.333333333,4.0,4.5,4.666666667,5.0,4.8,3.75,4.4,5.0,24,15,22,25,7,B,1,1,0,0,1,1,7,8,4,2,3,5,4,4,4,5,4,3,4,5,5,5,4,5,5,5,4,4,5,5,5,5,5,5,5,4,4,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,5,4,2,5,3,3,5,3,2,2,5,5,4,2,3,6,7.625,8,3.666666667,4.0,4.6,5.0,4.4,5.0,4.833333333,5.0,5.0,3.666666667,4.666666667,2.75,4,2,3,6,5.5,3.5,4.0,4.0,4.0,22,1,7.5,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,1,1,1,1,1,7,5,3,4,4,5,4,5,4,5,4,4,3,4,4,5,8,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,2,1,1,7,5,3,4,5,5,5,4,4,4,5,4,4,4,4,5,0.818181818,4.2,2.8,5.0855,1.9145,6.327,4.805,4.738,4.831,4.725,1.9145,武一帆,2.0,"选择的“冷门绝学”是敦煌壁画建筑图像艺术的谱系构建与传承路径研究方向。通过AI赋能大致可以有以下几个方面的未来发展前景：
1.收集整理
通过AI进行敦煌壁画的扫描、汇总和整理，可以识别出具有同类型特点的数据和纹样并整合。
2.展示
利用AI进行敦煌壁画的特色展示
3.再创作
利用AI进行壁画的再创作
4.文创
可以利用AI进行有创意的文创
同时可能会出现的伦理问题：可能会对宗教进行冲击，再创作的内容不容易获得认同感。",2.5179627093642854e-09,0.15254237288135594,0.13793103448275862,0.15254237288135594,0.021360389631092894,0.4085776968990794,0.14746782183647156,0.4418604651162791,0.02352941176470591
132002,8,1308,记录员,6.0,29989.0,4.0,4.0,2.6666666666666665,2.333333333333333,5.0,5.0,4.0,3.6666666666666665,3.333333333333333,4.333333333,3.666666667,3.666666667,5.333333333,3.131481481,3.788888889,3.733333333,3.4,4.0,3.7,4.5,0.375,3.4,5.0,4.333333333,4.0,4.666666667,4.0,4.5,4.333333333,4.75,3.8,3.75,3.8,3.8,19,15,19,19,8,B,1,3,0,0,1,1,6,7,3,2,4,5,4,4,2,3,3,3,4,4,4,5,4,3,4,4,4,2,2,4,4,3,4,4,3,5,4,2,4,5,3,3,4,4,4,2,4,3,3,4,4,3,3,2,2,3,2,3,1,1,3,3,2,4,4,5,4,3,5,5,6.625,7,3.666666667,3.0,3.2,3.6,4.0,3.6,4.0,3.0,3.5,2.0,3.0,1.75,4,3,5,5,6.0,4.5,4.5,4.5,5.5,22,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,1,1,1,1,1,2,8,5,3,4,4,5,5,4,3,4,4,5,2,3,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,2,0,1,8,5,3,5,5,5,5,4,4,3,2,4,2,3,4,4,0.909090909,5.0,1.0,5.8855,0.1145,6.827,5.805,5.238,5.331,6.225,0.1145,石航奇,1.0,"选择同声传译学
在人工智能时代可能焕发新生命力的可能原因
①传统的同声传译需要传译者先听然后根据内容做出翻译，然后读出来。AI的发展能够帮助其中的某一个步骤，甚至取代它。比如我们可以直接跳过传译者听的过程，直接让人工智能写出语言精彩又得体的传译稿，然后人工的任务量就大大降低。
②更极端一点，ai可以直接通过翻译读取发言人的话，通过大模型生成、优化出对应语言内容，然后通过类似元宇宙的模式将内容传递给听取发言的人们，传译者的任务可能只是在极短的时间内优化发言内容，使其更有人情味。
可能遇到的挑战
因为某些需要用到同声传译的场合都相对严肃严谨，同声传译的质量是一个很大的问题。以现有的技术，和对AI的展望来看，人工智能能否在一些语境下完美的完成任务，依旧是一个疑问。而且语言学存在的意义就是为了交流，人工智能取代这部分工作又是否符合大家对这个领域的认知，大众又是否能不能接受。",7.1925786008915384e-06,0.10526315789473684,0.07272727272727272,0.10526315789473684,0.030764112634426532,0.3550608801143078,0.14333422482013702,0.2956521739130435,0.03617710583153344
132011,8,1308,启发员,5.0,29994.0,4.0,4.0,3.0,5.0,4.666666666666667,4.666666666666667,3.333333333333333,3.333333333333333,3.333333333333333,5.0,5.0,4.333333333,5.0,4.057407407,4.344444444,4.066666667,3.4,4.9,4.4,4.8,0.5,4.4,5.0,4.666666667,4.2,4.666666667,4.666666667,4.5,5.0,5.0,4.0,4.0,5.0,5.0,20,16,25,25,8,B,1,1,0,0,1,1,7,6,2,3,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,4,3,4,4,4,4,3,3,4,4,4,4,3,4,6,6.375,6,3.333333333,4.0,3.8,4.0,4.0,4.0,4.0,4.0,4.0,3.0,4.0,3.5,4,3,4,6,5.0,4.0,4.5,4.5,5.5,26,1,7.0,1,1,1,1,1,1,1,1,0,2,1,1,1,1,1,1,0,2,1,2,0,2,1,1,7,5,4,5,5,5,4,5,4,4,4,4,3,4,3,3,10,1,1,1,1,1,2,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,5,4,5,5,5,5,5,4,5,4,4,2,4,3,3,0.772727273,4.7,2.3,5.5855,1.4145,5.827,5.305,5.238,5.331,6.225,1.4145,孙泽毅,3.0,"AI在同声传译学科的机遇与挑战
同声传译作为口译的分支，其难度大、门槛高一直是人类译员渴望又难以人人实现的理想。借助AI工具，人类译员可以将听—思—译的过程转变为看—思—译，即借助AI快速的语音识别和修订任务将音频转化为文字，帮助译员进行同声传译；
同时，AI工具的发展在某些程度也会取代人类译员，例如在非正式场合下，双方借助智能工具可以快速了解对方的意图并进行沟通，人类译者在此过程中消失，取而代之的是新的设备与技术。
在训练译员时，AI也可以作为一种翻译、语言的学习工具帮助学员掌握语言能力与翻译能力。
然而，AI的发展是否会挤压人类译员的生存空间依然是严峻的问题。例如许多翻译公司或者委托人会不断榨取译员的时间和价值，同时AI翻译的质量也需要有人类译员把关。",8.19274392060339e-13,0.09032258064516129,0.07843137254901959,0.09032258064516129,0.012541082857637085,0.2509346490554111,0.16534531116485596,0.3872549019607843,0.015441751368256429
132023,8,1308,协调员,5.0,1021.0,4.0,4.0,2.0,3.0,4.666666666666667,4.666666666666667,3.0,2.333333333333333,3.0,5.0,5.0,4.333333333,4.0,3.060185185,3.361111111,3.166666667,3.0,3.2,3.7,4.3,0.5,3.4,4.333333333,4.0,3.4,3.333333333,3.0,4.0,4.0,4.0,3.4,3.0,4.2,3.4,17,12,21,17,8,B,1,2,0,0,1,1,7,6,4,3,4,4,4,4,4,3,4,4,4,4,4,4,4,3,3,3,4,3,2,3,4,4,3,3,4,4,4,3,4,4,4,3,3,3,4,3,4,3,4,3,3,3,3,4,3,4,2,4,3,2,4,3,3,2,4,4,3,3,2,3,6.375,6,3.833333333,3.8,3.0,3.6,3.8,3.4,3.666666667,3.5,3.0,3.333333333,4.0,2.5,3,3,2,3,5.5,3.0,4.5,4.5,5.0,20,0,7.0,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,0,2,1,1,7,4,2,3,4,3,3,4,4,2,3,4,3,2,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,7,4,4,4,4,4,5,3,3,4,3,4,2,2,3,2,0.727272727,4.5,2.5,5.3855,1.6145,6.327,4.305,5.238,5.331,5.725,1.6145,张译文,2.0,"在人工智能时代，我们认为同声传译学科可能会焕发新生，得到发展。
可能的原因是：
随着大语言模型的兴起，我们认为在同声传译中可以适当引进人工智能，对译者的工作起到辅助作用；
同声传译需要译者即时的专注倾听，如果引进人工智能，可以在一定程度上转化为视译，可以缓解译者的工作强度，鉴于我们认为翻译工作仍然需要译者人为的理解加入，所以我们希望人工智能技术的引用只是提供一定的参考；
达到多语种同声传译技术标准的翻译人才目前依然处于较匮乏的阶段，引入人工智能有助于同声传译的学科发展
未来挑战：
可能由于人工智能技术的功能太过强大和过多干预，导致同声传译人员需求降低，取代同声传译的人才，让其丧失意义。",2.152031588908486e-05,0.0,0.0,0.0,0.031906039027097016,0.39217241838650907,0.12611228227615356,0.30120481927710846,0.041420118343195256
132009,9,1309,记录员,3.0,29993.0,4.0,4.0,4.666666666666667,5.333333333333333,1.3333333333333333,3.333333333333333,4.666666666666667,2.333333333333333,2.333333333333333,2.666666667,3.333333333,3.666666667,5.666666667,4.539814815,4.238888889,4.433333333,3.6,4.8,3.5,4.0,0.375,3.8,4.0,4.333333333,3.8,3.666666667,4.333333333,4.0,4.666666667,3.75,3.0,2.75,3.4,4.0,15,11,17,20,9,B,1,3,0,0,0,1,7,5,5,4,3,5,4,3,4,3,3,4,5,3,4,3,4,5,3,4,5,3,4,3,4,5,5,5,4,5,5,4,5,4,5,4,5,5,4,5,4,5,4,3,4,5,4,5,4,4,3,4,2,2,4,1,4,4,3,4,4,4,3,4,5.75,5,4.0,3.8,3.8,4.6,4.6,4.6,3.666666667,4.5,4.0,4.333333333,4.0,2.0,4,4,3,4,6.0,5.0,5.0,5.5,5.0,19,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,0,2,0,2,0,1,1,1,7,4,5,4,4,4,3,4,3,3,4,5,3,3,2,2,8,1,1,1,1,0,2,0,2,1,1,1,1,1,2,0,1,1,1,1,1,7,4,5,4,4,4,4,4,3,3,4,5,3,2,2,3,0.590909091,5.3,1.7,6.1855,0.8145,6.827,6.305,5.738,6.331,5.725,0.8145,冯新语,,"任务二
考古学
原因：考古学实地挖掘动工成本大、难度大，对于施工技术要求高
可能挑战：需要大量原始数据支撑
	       可能被不法分子利用，进行数据投毒或“越狱攻击
	       需要处理多种因素作用下的复杂情况
传承：总结过往经验，把所有历史数据和实地图像模型云上传
社会应用：利用AI技术复原古代遗址的真实情况
                       运用AI构建虚拟现实的实地景象
创新：可以利用AI技术判断推测陵墓挖掘最佳位置
              利用AI识别文物推断其可能的朝代、用途
对于有关史实进行AI补充、推理
古代语言研究
原因：语言研究书目繁杂，研究时间成本高
可能挑战：部分语言实物资料欠缺或已经彻底失传
传承&创新：用AI来识别文本，通过不断比对推测可能的语法结构和语言体系
社会应用：可以用来拯救濒危语言
	       可以利用AI进行濒危语言的作品生成和创作，增强宣传力度",5.716048246558798e-05,0.17948717948717946,0.15789473684210528,0.17948717948717946,0.05473760293049164,0.7250659391749131,0.15146955847740173,0.4576923076923077,0.050772626931567366
132010,9,1309,协调员,7.0,29993.0,4.0,4.0,3.6666666666666665,3.333333333333333,4.0,5.333333333333333,4.333333333333333,1.6666666666666667,1.6666666666666667,4.666666667,3.666666667,5.0,4.333333333,3.510185185,4.061111111,3.366666667,2.2,4.3,4.4,4.8,0.125,3.0,3.0,4.333333333,3.0,3.0,4.0,3.5,3.666666667,3.75,2.6,3.25,3.8,3.0,13,13,19,15,9,B,1,2,0,0,0,1,8,6,4,4,4,4,3,3,2,2,4,2,4,4,4,4,4,4,3,4,4,3,3,3,3,3,3,3,3,4,4,3,4,4,3,3,3,3,3,4,3,3,3,3,3,3,3,3,3,4,3,4,2,2,4,2,2,4,3,3,3,2,3,4,6.75,6,3.666666667,2.8,3.4,3.0,3.8,3.0,3.833333333,3.25,3.0,2.666666667,4.0,2.25,3,2,3,4,4.5,4.5,4.5,4.0,5.0,19,0,6.0,1,1,1,1,1,1,1,1,0,2,1,1,1,2,0,1,1,1,1,2,1,2,1,2,6,4,5,3,3,3,3,4,3,4,2,2,4,2,2,1,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,8,4,5,4,3,3,3,4,3,4,2,2,2,2,2,1,0.818181818,4.5,3.5,5.3855,2.6145,5.327,5.805,5.238,4.831,5.725,2.6145,朱佳音,,"冷门绝学因AI焕发新生
1考古
AI可以帮助预测挖掘坟墓的地点、分析文物所属的文明，还原古人生活场景、构建供人进行VR体验的模型。风险是需要大量数据，可能被投毒
2古籍研究、古语言研究
AI可以帮助总结古籍内容、补全残缺内容、识别古文字，从而加深人们对古文字的理解，还可以撰写宣传文本帮助濒临失传的语言的生存。挑战是人们自己不了解的古语言文字，难以指导AI的分析。",1.666254694953264e-09,0.1348314606741573,0.09195402298850575,0.1348314606741573,0.01833286970983151,0.3851322696048159,0.14479193091392517,0.3611111111111111,0.021311475409836023
132016,9,1309,启发员,1.0,29994.0,1.0,1.0,5.333333333333333,4.666666666666667,5.0,3.0,1.6666666666666667,3.6666666666666665,3.6666666666666665,4.333333333,3.666666667,4.333333333,4.666666667,3.211111111,4.266666667,3.6,3.6,4.5,3.3,4.0,0.25,3.8,4.0,4.0,3.8,4.0,4.0,3.5,4.666666667,4.0,3.0,4.25,4.4,3.6,15,17,22,18,9,B,1,1,0,0,0,1,7,7,4,5,4,4,5,4,5,4,5,4,4,5,5,4,4,5,4,5,4,4,5,4,5,4,5,5,5,5,4,5,4,4,5,5,4,4,5,5,4,4,5,5,5,5,5,4,3,4,2,4,3,2,4,2,4,4,5,4,4,4,5,5,7.0,7,4.333333333,4.4,4.4,4.8,4.4,4.6,4.5,4.5,5.0,3.666666667,4.0,2.25,4,4,5,5,3.0,5.0,5.0,5.5,5.0,22,1,7.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,1,1,1,1,1,1,1,7,4,4,4,4,4,4,4,4,4,4,3,3,4,3,4,7,1,1,1,1,0,1,0,1,0,1,1,1,1,1,1,1,0,1,1,1,1,4,4,4,4,4,4,4,4,4,4,3,3,3,4,4,0.727272727,4.7,2.3,5.5855,1.4145,3.827,6.305,5.738,6.331,5.725,1.4145,张子豪,,"对考古发掘：综合各方信息，处理各类信号，如波、图片·、等，建立力学模型，给出合适的挖掘方案。
对古文字：识别、破译文字的内容，并对以往的解释给出修正。
其余：见冯新语同学。",8.747091378006876e-40,0.0,0.0,0.0,0.003978629078094805,0.4620374847321463,0.05781980976462364,0.48214285714285715,0.005531653349723387
131001,10,1310,记录员,5.0,29001.0,3.0,4.0,1.0,4.333333333333333,4.0,6.0,5.0,3.6666666666666665,3.6666666666666665,3.666666667,4.666666667,5.666666667,5.666666667,4.821296296,3.927777778,3.566666667,4.4,3.8,4.6,5.6,0.5,4.8,4.333333333,3.666666667,3.8,3.333333333,4.0,4.5,4.333333333,4.0,3.6,3.5,4.4,4.2,18,14,22,21,10,B,1,3,0,0,0,1,7,8,5,3,4,5,4,5,4,4,4,3,4,4,4,5,5,4,4,5,4,5,5,4,5,4,5,5,4,5,4,3,4,4,5,4,5,5,4,5,4,4,5,5,5,4,4,5,4,5,4,5,4,5,4,5,4,5,4,5,4,4,3,4,7.625,8,4.333333333,3.8,4.6,4.6,4.0,4.6,4.333333333,4.5,4.5,4.333333333,4.666666667,4.5,4,4,3,4,6.5,6.0,5.5,7.0,7.0,26,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,8,5,2,5,4,4,2,4,4,4,3,4,3,4,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,8,5,2,4,5,4,4,5,5,5,4,5,3,4,3,4,0.863636364,6.4,0.6,7.2855,-0.2855,7.327,7.305,6.238,7.831,7.725,0.2855,李钟洋,2.0,"冷门学科：古文字学
冷门（小众）的原因/特点：不符合现代社会的发展节奏但是对于人类学、人类起源有深远意义
利用AI的优点：
效率更高（快速比对数据库确定字的含义）
研究的方法更加多样化现代化（文字识别和图形识别的学科交叉）
AI可以利用多模态学习联系多学科的知识（相比于传统的研究更加多角度）
AI可以通过对语法的分析推导出未识别的字符
可以训练专门用于古文字学的专属模型，提升科研效率
利用AI进行文字演化规律建模
建立古文字学知识图谱
AI助力发展思路：
通过AI采用多形式多媒体的方式（如数字博物馆）
重新焕发古文字学的魅力，吸引公众了解和参与到其中，有助于传承创新和发展
建立数据化的数据库，有助于传播推广和研究
AI应用的挑战：
数据稀缺
文字识别复杂
AI识别的可靠性存在不确定性",1.3864344735678963e-05,0.2222222222222222,0.19999999999999998,0.2222222222222222,0.038376788372695966,0.45172456651738624,0.12094755470752716,0.32663316582914576,0.04113283884018881
132020,10,1310,启发员,6.0,1019.0,8.0,8.0,3.0,4.0,5.333333333333333,2.333333333333333,3.0,2.6666666666666665,1.6666666666666667,4.333333333,2.666666667,4.333333333,4.666666667,2.710185185,4.261111111,3.566666667,2.4,4.1,3.3,3.8,0.375,3.6,3.666666667,4.0,3.4,2.666666667,2.0,3.5,3.666666667,3.0,3.8,3.5,3.2,3.8,19,14,16,19,10,B,1,1,0,0,0,1,10,7,3,4,4,4,4,3,5,4,4,4,4,5,4,3,3,4,4,4,4,2,4,4,4,4,4,3,3,4,4,4,4,4,4,4,4,3,4,3,3,3,4,4,4,3,3,4,4,4,2,4,2,2,4,2,4,4,4,4,3,2,3,3,8.125,7,3.666666667,4.2,3.6,3.6,4.0,3.8,3.833333333,3.25,3.5,4.0,4.0,2.0,3,2,3,3,7.5,7.0,6.0,7.0,7.5,17,0,7.0,0,1,1,1,1,1,1,1,1,2,1,1,0,2,1,1,0,2,1,1,1,1,0,2,7,2,1,3,2,2,4,4,3,4,2,4,2,2,1,2,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,2,7,5,3,4,4,4,3,4,3,4,3,4,2,3,2,3,0.772727273,7.0,3.0,7.8855,2.1145,8.327,8.305,6.738,7.831,8.225,2.1145,李乔易,3.0,"冷门学科：古文字学
冷门（小众）的原因/特点：不符合现代社会的发展节奏但是对于人类学、人类起源有深远意义
利用AI的优点：
效率更高（快速比对数据库确定字的含义）
研究的方法更加多样化现代化（文字识别和图形识别的学科交叉）
AI可以利用多模态学习联系多学科的知识（相比于传统的研究更加多角度）
AI可以通过对语法的分析推导出未识别的字符
可以训练专门用于古文字学的专属模型，提升科研效率
利用AI进行文字演化规律建模
建立古文字学知识图谱
AI助力发展思路：
通过AI采用多形式多媒体的方式（如数字博物馆）
重新焕发古文字学的魅力，吸引公众了解和参与到其中，有助于传承创新和发展
建立数据化的数据库，有助于传播推广和研究
AI应用的挑战：
数据稀缺
文字识别复杂
AI识别的可靠性存在不确定性",6.398479221566035e-14,0.07582938388625593,0.06698564593301436,0.07582938388625593,0.01087906948370173,0.2176031453510675,0.13523736596107483,0.3969849246231156,0.01451672179345831
132022,10,1310,协调员,6.0,1020.0,4.0,4.0,5.333333333333333,4.0,5.0,3.0,4.666666666666667,2.333333333333333,2.333333333333333,5.666666667,3.333333333,5.333333333,5.0,2.935185185,2.611111111,2.666666667,3.0,2.6,3.3,4.6,0.5,4.0,3.666666667,2.666666667,2.8,4.0,2.333333333,3.5,3.333333333,2.0,3.4,3.25,2.6,4.2,17,13,13,21,10,B,1,2,0,0,0,1,8,7,4,3,3,4,3,4,3,4,3,3,4,2,5,2,5,4,4,2,3,5,3,3,3,5,3,5,3,5,3,5,3,5,3,4,2,4,4,3,5,3,2,5,4,3,5,4,3,4,2,5,4,3,3,3,4,4,3,5,4,4,4,4,7.375,7,3.5,3.4,3.2,3.8,4.2,3.4,3.666666667,3.25,4.25,3.666666667,4.0,3.0,4,4,4,4,7.5,7.0,6.0,7.0,7.5,20,1,8.0,0,1,1,2,1,2,1,1,1,1,1,1,0,1,0,1,0,1,0,2,0,2,0,1,5,3,1,3,4,4,4,3,3,2,3,3,2,3,2,2,8,0,1,1,1,0,1,0,1,0,2,1,1,1,1,0,2,0,2,1,1,8,4,1,3,4,4,3,5,4,3,4,4,3,3,2,2,0.409090909,7.0,1.0,7.8855,0.1145,8.327,8.305,6.738,7.831,8.225,0.1145,周鸿祥,1.0,"冷门学科：古文字学
冷门（小众）的原因/特点：不符合现代社会的发展节奏但是对于人类学、人类起源有深远意义
利用AI的优点：
效率更高（快速比对数据库确定字的含义）
研究的方法更加多样化现代化（文字识别和图形识别的学科交叉）
AI可以利用多模态学习联系多学科的知识（相比于传统的研究更加多角度）
AI可以通过对语法的分析推导出未识别的字符
可以训练专门用于古文字学的专属模型，提升科研效率
利用AI进行文字演化规律建模
建立古文字学知识图谱
AI助力发展思路：
通过AI采用多形式多媒体的方式（如数字博物馆）
重新焕发古文字学的魅力，吸引公众了解和参与到其中，有助于传承创新和发展
建立数据化的数据库，有助于传播推广和研究
AI应用的挑战：
数据稀缺
文字识别复杂
AI识别的可靠性存在不确定性",6.416305397077355e-10,0.12307692307692307,0.109375,0.12307692307692307,0.015849870031065746,0.4118245168217646,0.09047455340623856,0.36683417085427134,0.020384725811082416
132005,11,1311,协调员,6.0,29991.0,4.0,4.0,4.0,2.6666666666666665,5.0,4.0,3.0,2.0,2.0,3.666666667,3.666666667,3.666666667,3.666666667,3.535185185,4.211111111,4.266666667,3.6,3.5,3.4,4.0,0.125,4.2,4.666666667,3.333333333,4.4,4.333333333,3.0,2.5,4.333333333,4.0,3.8,3.0,3.8,3.8,19,12,19,19,11,B,1,2,0,0,0,1,7,7,4,3,4,4,4,4,3,2,4,4,4,4,4,4,4,3,4,4,4,4,3,5,3,3,3,4,3,4,4,3,4,4,3,3,3,4,4,3,4,2,4,4,3,3,3,4,5,4,4,3,3,2,3,2,4,4,4,3,4,1,3,4,7.0,7,3.833333333,3.4,4.0,3.2,3.8,3.4,3.833333333,3.25,3.25,4.333333333,3.333333333,2.75,4,1,3,4,7.0,7.0,6.0,7.5,7.5,23,1,7.0,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,1,1,0,1,1,1,6,4,2,3,4,5,4,4,4,5,5,4,3,2,2,2,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,6,4,2,4,4,5,5,5,4,4,4,4,4,2,2,2,0.772727273,7.0,0.0,7.8855,-0.8855,7.827,8.305,6.738,8.331,8.225,0.8855,王上,2.0,"学科：考古学 
 人工智能时代可能焕发新生命力的可能原因
利用人工智能的数据处理和分析能力：对考古资料进行分类整理，对新发现的数据进行快速分析。
利用其他的，如图像识别技术，总结分析过往已有的资料，对文物进行区分以及年代分析。
多模态学习：通过AI将其他学科比如生物、化学分析对考古具体活动提供支持。
文化传播：设立AI虚拟人物，比如建立专门的数据库后形成一个活的”知识库“，建立可亲可爱的知识传播员。
文化展览：通过AI技术对文物进行多方面展示；对于已经灭失的文物进行复刻，数据再现。
 未来发展中面临的可能挑战
海量数据可能会造成人工智能幻觉，可能会给出错误的考古知识或数据，甚至出现“不尊重”的伦理问题。
考古数据本身的确定性可能存疑，以此为依据的AI意见的真实性也存疑。",1.0221814346481208e-05,0.09411764705882353,0.07228915662650602,0.09411764705882353,0.03877648093914482,0.4668167944148504,0.1861158162355423,0.38071065989847713,0.04360636417206831
132015,11,1311,记录员,6.0,29994.0,3.0,4.0,4.333333333333333,3.6666666666666665,5.0,4.0,3.0,3.0,2.6666666666666665,5.666666667,4.666666667,4.0,4.333333333,4.074074074,4.444444444,3.666666667,3.0,4.9,4.4,4.7,0.375,3.8,5.0,4.0,4.4,3.0,4.0,5.0,4.0,5.0,4.0,2.75,4.4,3.8,20,11,22,19,11,B,1,3,0,0,0,1,7,6,4,4,4,4,5,5,5,4,4,4,4,4,5,5,5,3,3,4,4,4,4,4,3,2,2,3,3,4,4,4,3,3,5,5,4,4,4,4,4,3,4,4,3,3,3,3,4,4,2,4,2,3,4,2,3,4,4,4,4,4,4,4,6.375,6,4.333333333,4.2,4.0,2.6,3.6,4.4,4.166666667,3.75,3.25,3.333333333,4.0,2.25,4,4,4,4,7.0,6.5,5.5,6.5,7.0,24,0,6.0,0,1,1,1,0,1,1,2,1,1,1,1,1,2,1,1,1,1,1,1,1,2,0,2,6,5,2,5,3,3,3,4,4,4,5,5,3,3,2,3,8,1,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,5,3,4,5,5,5,4,4,4,3,4,3,3,3,3,0.727272727,6.5,0.5,7.3855,-0.3855,7.827,7.805,6.238,7.331,7.725,0.3855,王梦珂,3.0,"一. 人工智能时代可能焕发新生命力的可能原因
人工智能可以在文物发掘、文物保护、文物展览、文化传播方面发挥作用
1.利用人工智能的数据处理和分析能力：对考古资料进行分类整理，对新发现的数据进行快速分析。
2.利用其他的，如图像识别技术，总结分析过往已有的资料，对文物进行区分以及年代分析。
3.多模态学习：通过AI将其他学科比如生物、化学分析对考古具体活动提供支持。
4.文化传播：设立AI虚拟人物，比如建立专门的数据库后形成一个活的”知识库“，建立可亲可爱的知识传播员。
5.文化展览：通过AI技术对文物进行多方面展示；对于已经灭失的文物进行复刻，数据再现。
二. 未来发展中面临的可能挑战
1.海量数据可能会造成人工智能幻觉，可能会给出错误的考古知识或数据，甚至出现“不尊重”的伦理问题。
2.考古数据本身的确定性可能存疑，以此为依据的AI意见的真实性也存疑。",6.591567784985206e-05,0.25316455696202533,0.1558441558441558,0.25316455696202533,0.041822526591958585,0.5420037470710777,0.19660507142543793,0.38495575221238937,0.044623655913978544
132019,11,1311,启发员,8.0,1019.0,9.0,9.0,2.6666666666666665,3.6666666666666665,3.0,1.6666666666666667,4.333333333333333,3.6666666666666665,3.333333333333333,5.0,5.0,3.333333333,4.666666667,3.423148148,3.538888889,2.233333333,2.4,3.6,2.8,3.7,0.25,3.4,3.666666667,3.333333333,3.6,3.333333333,2.666666667,3.5,3.666666667,3.75,3.6,3.75,3.4,3.8,18,15,17,19,11,B,1,1,0,0,0,1,7,6,4,4,3,4,4,4,3,3,4,2,4,4,5,5,4,3,4,4,3,4,4,3,4,3,3,3,3,5,4,3,4,4,3,3,3,3,5,4,4,3,3,4,3,4,4,3,3,4,3,3,2,2,4,2,2,3,4,4,4,3,4,5,6.375,6,3.833333333,3.2,3.6,3.2,4.0,3.4,4.166666667,3.5,3.75,2.666666667,3.666666667,2.25,4,3,4,5,7.5,6.0,6.0,7.0,7.0,19,0,6.0,1,1,1,1,1,1,1,2,0,2,1,1,1,2,1,2,1,1,0,2,0,1,1,2,5,3,2,3,3,3,4,3,4,4,4,3,4,3,3,4,7,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,0,2,1,1,7,4,3,3,4,4,3,3,3,4,4,3,2,3,4,4,0.772727273,6.7,0.3,7.5855,-0.5855,8.327,7.305,6.738,7.831,7.725,0.5855,曹颖,1.0,"学科：考古 古文字学 文物保护等
应用
在挖掘过程中利用AI探测位置
人工智能可以利用输入数据研究和分析甲骨文
图像识别技术 对文物进行区分以及年代分析
对考古资料进行分类整理解读 
与其他学科进行联系交融，如化学分析
通过AI再现文物全貌，复刻已经损坏的文物
在参观过程中个性化推送，机器人引导，激发人们保护意识
对古籍历史数据保存传承，如线上图书馆，增强实用性
挑战
AI数据过多产生“幻觉”，传播虚假信息错误引导
文化遗产被娱乐化，不够尊重
文创版权问题
考古数据正确性难以评估，容易造成偏差",1.2009733626612918e-16,0.03571428571428572,0.024096385542168676,0.03571428571428572,0.008862275449101795,0.3263658515462542,0.12387876212596893,0.43283582089552236,0.012543252595155763
132001,12,1312,启发员,4.0,29989.0,6.0,7.0,3.0,4.333333333333333,5.666666666666667,3.0,4.333333333333333,3.6666666666666665,4.0,5.666666667,5.666666667,4.0,4.333333333,3.959259259,3.755555556,3.533333333,3.2,4.1,2.8,3.9,0.625,4.4,4.666666667,3.666666667,4.4,5.0,4.0,4.5,4.333333333,4.0,4.4,4.5,4.4,4.4,22,18,22,22,12,B,1,1,0,0,0,1,7,7,4,4,5,4,5,5,4,5,5,4,5,4,5,5,4,3,5,5,4,4,4,5,4,4,4,3,4,4,3,4,4,4,4,4,5,5,5,5,4,4,4,3,4,4,4,4,3,4,2,4,3,2,4,3,4,4,3,4,2,3,4,6,7.0,7,4.5,4.6,4.4,3.8,3.8,4.6,4.333333333,4.25,3.75,3.666666667,4.0,2.5,2,3,4,6,6.0,4.0,4.5,5.5,6.0,23,0,7.0,1,1,1,1,1,1,1,2,1,1,1,1,1,1,0,1,1,1,1,2,0,1,1,1,7,4,4,4,5,5,5,5,4,5,4,4,4,4,4,4,7,0,1,1,2,0,1,0,1,1,1,1,1,1,1,1,2,0,2,0,1,6,4,3,4,4,5,5,4,4,4,5,5,4,4,4,3,0.681818182,5.2,1.8,6.0855,0.9145,6.827,5.305,5.238,6.331,6.725,0.9145,王璇,1.0,"敦煌学
AI助力敦煌学的发展：AI可以修复壁画；AI可以提取敦煌壁画的元素，比如人物的服饰、壁画中的器具等，然后根据还原的服饰。器具等模型，人们可以复原古代的用具，根据敦煌壁画做出的各种器具可以作为敦煌文创周边，服饰可以助于影视剧的拍摄等。这些都有利于敦煌学的发展。
面临的挑战：AI需要学习，但是懂得敦煌学的专家很少，所以AI的学习资料来源比较少；不同的AI修复壁画会有不同的结果，如何判断哪个结果是正确的，还需要建立专门的机构去鉴别。",9.166348438970662e-13,0.10434782608695652,0.08849557522123894,0.10434782608695652,0.012264504479950939,0.30811055549498156,0.11346762627363205,0.4076923076923077,0.01632275947028028
132007,12,1312,协调员,3.0,29992.0,3.0,3.0,1.6666666666666667,3.0,4.666666666666667,4.333333333333333,4.666666666666667,4.0,2.6666666666666665,5.0,3.0,3.666666667,5.0,4.12037037,4.722222222,4.333333333,4.0,4.4,4.2,4.4,0.625,4.2,3.333333333,3.666666667,4.2,3.0,3.0,3.5,4.666666667,4.5,4.0,3.5,4.2,4.0,20,14,21,20,12,B,1,2,0,0,0,1,10,6,5,4,4,4,5,5,3,3,4,3,4,5,5,3,4,4,2,3,4,4,3,4,2,2,2,4,2,4,4,4,4,3,4,3,4,4,4,5,5,4,5,4,4,4,4,3,4,4,2,4,3,2,4,1,2,4,3,4,5,3,2,5,7.5,6,4.5,3.4,3.6,2.4,3.8,3.8,3.833333333,4.75,4.0,3.0,4.0,2.0,5,3,2,5,7.0,4.0,6.0,6.5,7.0,20,0,8.0,0,2,1,1,1,1,1,1,0,2,1,1,0,1,0,1,1,1,1,2,0,1,1,1,7,4,1,4,3,3,3,5,4,4,4,4,3,2,3,3,8,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,1,4,3,4,3,3,4,4,3,5,5,4,2,4,4,4,0.681818182,6.1,3.9,6.9855,3.0145,7.827,5.305,6.738,7.331,7.725,3.0145,陈佳祺,2.0,"冷门绝学：历史文物修复和推测（壁画修复）
AI焕发生命力：
AI给出修复建议：将一些有关文物修复的专业知识以及相关的史料等史学知识投喂给AI，AI可以不加主观的色彩进行模拟修复，进一步给出一些专业认识一些修复还原的建议，提交了修复的效率和成本；
AI落实自己执行：同时AI可以根据专家学者的设计修复图稿和要求对文物直接进行修复，这一部分AI是作为一个执行者去帮助专业人士进行文物的修复，减小了因为人为因素造成修复失败的可能；
根据壁画推测以前真实的历史情况：在这一部分，通过将扫描后的文物资料投喂给AI，AI可以根据壁画、文物透露出来的信息推测当时社会中人们的穿着打扮、风俗习惯等，并以图片、视频等可视化的形式呈现出来，帮助历史文化的还原与应用。
可能面临的挑战：
由于相关知识太过专业化，AI可能无法完全掌握这部分知识，同时不同学者、机构有其不同的学术偏好，将带有主观色彩的知识传递给AI，可能引发一些偏差与歧视，不利于还原文物的真实情况；
应用AI的建议：
设置专门的AI修复鉴别机构，减少主观意识的干扰；
人为地对AI修复结果进行鉴别，减小偏差。",0.0013435745926043453,0.4827586206896552,0.46428571428571425,0.4827586206896552,0.06697176024109834,0.4588064815435883,0.18673090636730194,0.2518248175182482,0.06632124352331603
132008,12,1312,记录员,16.0,29992.0,4.0,4.0,3.333333333333333,5.0,4.333333333333333,4.666666666666667,1.3333333333333333,4.666666666666667,4.666666666666667,4.666666667,6.0,5.0,5.0,4.562037037,4.372222222,4.233333333,3.4,5.6,4.7,4.4,0.375,4.0,4.666666667,4.333333333,3.8,4.666666667,3.666666667,4.0,5.0,4.5,3.6,4.25,3.2,4.6,18,17,16,23,12,B,1,3,0,0,0,1,8,8,5,4,5,4,5,4,5,4,5,2,4,5,5,5,4,5,4,5,4,5,5,5,5,5,4,4,4,4,5,5,4,5,4,5,4,5,4,5,4,3,5,5,4,4,4,4,5,4,1,5,2,1,5,1,3,4,4,4,5,2,3,5,8.0,8,4.5,4.0,4.8,4.4,4.6,4.4,4.666666667,4.25,4.25,4.0,4.666666667,1.25,5,2,3,5,6.0,3.5,5.0,4.5,5.0,18,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,8,4,3,4,5,5,4,4,5,4,2,4,2,4,5,5,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,8,5,4,4,5,4,5,3,4,5,3,5,2,4,5,5,0.863636364,4.8,3.2,5.6855,2.3145,6.827,4.805,5.738,5.331,5.725,2.3145,孙从然,3.0,"本组讨论了敦煌学在AI时代的发展
用途
我们可以通过AI技术修复和处理敦煌石窟中的一些损失的壁画和文物，比如使其完善，让它动起来，同时识别物品的年代和特点，生成相关模型，使其可以准确的被生产运用之现实场景和作品中
2．问题
   AI技术对文物的修复可能仅仅建立在美术层面，对于相应的历史文化和知识可能欠缺。同时，多种AI技术可能产生五花八门的修复结果，难以比较优劣。
3.解决方法
将AI修复技术作为辅助，让其先给出修复建议与方法，再由相关专业人员进行修改，然后再进行最终的修复。成立专门的修复筛选机构，集中专业人士力量，进行修复结果的最终筛选。
4.反思
  AI对于文物的修复在目前看来不能成为主流，但一定具有很强大的辅助作用，能更好的协助文物历史工作者在自己的领域做出伟大的贡献。",3.6798442445258665e-16,0.07894736842105263,0.07079646017699115,0.07894736842105263,0.009701412094427079,0.3997260905553041,0.18472805619239807,0.4549763033175355,0.013348164627363768
131002,13,1313,协调员,4.0,29002.0,5.0,5.0,4.0,5.666666666666667,4.0,4.666666666666667,1.0,4.0,4.333333333333333,6.0,4.666666667,4.666666667,6.0,4.458333333,3.75,3.5,4.0,5.0,4.9,4.5,0.625,5.0,5.0,3.666666667,4.4,5.0,3.666666667,4.0,4.333333333,5.0,3.6,2.75,4.0,4.4,18,11,20,22,13,B,1,2,0,1,0,1,9,9,4,4,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,4,1,3,1,1,3,1,1,3,5,5,4,3,2,3,9.0,9,4.5,4.0,4.0,5.0,4.2,4.0,4.0,4.0,4.0,2.333333333,3.333333333,1.0,4,3,2,3,6.5,6.5,6.0,6.5,7.0,21,0,8.0,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,9,4,3,4,5,5,5,5,5,4,4,4,3,5,4,4,8,1,1,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,1,0,1,8,4,3,4,5,5,5,5,5,5,5,5,3,4,4,4,0.727272727,6.5,2.5,6.5,2.5,6.5,6.5,6.0,6.5,7.0,2.5,王怡璇,1.0,"领域或技术层面带来重大安全风险
网络舆情、科研、医疗、资源使用、电子信号通讯的所有涉及到的人类必须的如资源运输、出行、交流沟通等等
如何预防和监测（技术来设计有效的应对方案）
发明更高级的信息技术，并注意超级对齐，在人类无法检测到的层面实现监控；制造备用后路（以量取胜、以及不完全依赖科技的重要资源储备）；人为对AI实行检测以及限制（增加技术限制和规避，设置虚拟世界即参入虚假信息迷惑AI，提前预防设置准入权限和判断手段可以结合心理测试等测试手段）；设置物理性摧毁手段，如编入程序性凋亡的代码等
结合具体场景和技术原理，提出具有可行性和创新性的解决方法
在医疗领域，以人为本设置机械化准入和操作步骤确认步骤，划定区域设置准入权限
军事领域，不过度依赖，但高度发展，即军事素质和高科技相辅相成，注重监测、设定多项虚拟迷惑等",1.3000510877283458e-08,0.04081632653061225,0.020833333333333332,0.04081632653061225,0.019100091827364555,0.39092815888656,0.11410722881555557,0.2995169082125604,0.020652898067954673
131020,13,1313,记录员,10.0,29010.0,6.0,7.0,1.3333333333333333,2.0,1.0,3.6666666666666665,4.666666666666667,3.0,3.0,3.666666667,2.333333333,5.666666667,4.333333333,3.277777778,3.666666667,4.0,4.0,3.4,5.0,5.0,0.125,3.0,3.333333333,2.666666667,3.6,3.333333333,3.333333333,5.0,3.666666667,3.0,1.8,3.5,4.8,3.2,9,14,24,16,13,B,1,3,0,1,0,1,6,6,4,4,3,3,4,5,5,5,5,5,4,4,5,5,3,3,3,4,3,2,3,2,4,3,3,3,3,3,4,4,5,5,5,4,5,5,5,4,5,4,5,5,4,4,4,3,5,4,2,5,5,5,5,2,3,5,4,3,4,4,3,4,6.0,6,3.833333333,4.8,2.8,3.2,4.2,4.8,3.833333333,4.5,4.25,3.666666667,4.666666667,3.5,4,4,3,4,6.0,6.5,6.0,6.5,6.0,23,0,5.0,1,2,1,1,1,1,1,2,1,1,1,1,0,2,0,2,1,1,1,2,0,2,1,2,3,4,4,2,3,3,4,4,4,4,3,3,5,3,3,3,5,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,2,0,2,1,1,5,4,2,2,3,3,4,2,3,4,4,2,5,3,3,3,0.681818182,6.2,-0.2,6.2,-0.2,6.0,6.5,6.0,6.5,6.0,0.2,李梦楠,2.0,"风险和技术层面
通讯网络
资源控制：水资源、电力、
物理领域
生物
网络舆情（负面）、虚假信息的传递
应对方案
切断对方的能源供应
心理战：提供一个虚假的空间干扰对方
数量战：用数量上压制-10个基础的AI对抗他们的超级AI
备胎计划：研发一些备用的AI，或者是保留原始的生存方法，在切断敌人通路的同时给自己留后路
完善网络防火墙
使用反转的程序代码：让对方始终保持自洽的状态，但无法达到他们的重点（报错）
限制技术权限：搭建一个对方无法进入的空间（通过一些机械手段）
判断手段：通过一些问题测试是否为真人
设置一些自毁程序-指针代码
展望
超级对齐
公众意识
跨平台协作",2.1341965049907575e-07,0.08955223880597014,0.06153846153846154,0.08955223880597014,0.021872499333155507,0.4439203270098111,0.10057899355888367,0.33136094674556216,0.026162790697674465
131024,13,1313,启发员,5.0,29012.0,6.0,6.0,4.666666666666667,4.666666666666667,4.0,5.333333333333333,3.0,4.333333333333333,4.333333333333333,4.666666667,5.333333333,5.666666667,5.666666667,4.125,3.75,3.5,4.0,4.7,4.4,3.9,0.5,4.6,5.0,4.0,4.4,5.0,3.666666667,4.5,4.333333333,4.5,4.6,4.25,4.2,5.0,23,17,21,25,13,B,1,1,0,1,0,1,8,7,5,4,4,5,5,3,5,3,2,2,4,5,5,4,5,5,5,5,5,5,4,4,5,5,5,4,5,4,5,5,5,4,5,4,5,5,5,5,4,5,5,5,4,5,5,5,4,4,2,5,2,1,5,2,4,5,5,5,5,4,4,7,7.375,7,4.333333333,3.2,4.6,4.8,4.6,4.8,4.833333333,4.75,4.75,4.333333333,4.666666667,1.75,5,4,4,7,7.5,7.5,7.5,6.5,7.5,23,1,10.0,0,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,1,1,1,1,1,1,1,1,7,4,3,4,5,5,5,4,4,5,5,4,3,4,4,5,8,1,1,1,1,0,1,0,1,0,1,1,1,0,1,1,1,0,1,1,1,8,4,4,4,5,5,5,4,5,4,5,5,3,4,5,4,0.636363636,7.3,0.7,7.3,0.7,7.5,7.5,7.5,6.5,7.5,0.7,王申奇,3.0,"安全特工
可能带来风险的领域和技术层面
通讯领域、科研领域、军事领域、生物领域、人类相关的医学领域、电力领域（攻击配电站输送）、人类的资源控制（例如水资源配水站）、启动一些地震仪破坏现实世界、AI控制产生负面网络舆情、虚假信息的传递（气象灾害等）。
预防与监测方案
逐步完善超级对齐方案、通过制造可控的较低智能的AI来进行对抗（例如十个AI并行进行工作）、研发一些备用的AI、完善网络防火墙、限制AI 的使用领域的权限、通过机械手段（在三维世界的指纹、虹膜等）设置权限、通过设置阈值来进行AI的活跃发展限制。
可行性和创新性的解决方法
创建新的可控的安全网络空间、通过切断破坏性AI的能源供应、设置特定区域的能源供应（被攻击的区域或者是已经被AI占领的虚拟空间）、运用一些退化的古老手段（一些机械手段而不是网络手段）、反程序代码进行还原一些虚拟操作、让AI进行无限自洽循环来遏制其发展（认为制造BUG来阻止AI的侵入）、设置自毁程序或者指针代码引入随机化过程来破AI的活跃平台。超级对齐方案达到完美，完全可控化AI，成为人类的工具。",,,,,,,,,
131007,14,1314,记录员,6.0,29004.0,4.0,4.0,1.6666666666666667,2.6666666666666665,3.6666666666666665,3.333333333333333,4.333333333333333,4.0,4.0,3.0,4.0,4.0,5.0,3.925925926,4.555555556,4.333333333,4.0,3.8,3.0,4.0,0.25,3.2,4.0,3.333333333,3.0,3.333333333,3.0,4.5,4.0,4.5,3.0,3.5,4.4,3.6,15,14,22,18,14,B,1,3,0,1,0,1,6,6,4,4,4,4,3,4,4,3,3,3,4,4,4,4,3,4,4,4,4,3,3,4,4,4,4,4,4,5,5,4,4,4,4,4,4,4,4,5,5,5,5,4,4,4,4,3,4,4,2,5,2,2,4,2,3,4,4,4,3,3,3,3,6.0,6,3.833333333,3.4,3.6,4.0,4.4,4.0,3.833333333,5.0,4.0,3.333333333,4.333333333,2.0,3,3,3,3,6.5,7.0,7.0,7.0,7.0,20,0,5.0,1,2,0,2,1,2,1,2,0,2,1,2,0,2,0,2,1,2,0,2,0,2,0,2,4,4,3,2,3,4,3,4,3,3,2,3,3,4,4,4,6,0,2,1,2,0,2,1,2,0,2,0,2,0,2,0,2,0,2,0,2,6,4,3,3,4,4,4,3,3,3,3,4,2,4,4,4,0.318181818,6.9,-0.9,6.9,-0.9,6.5,7.0,7.0,7.0,7.0,0.9,康雅凝,3.0,"一、军事领域
1. 破坏领域
   武器系统入侵：AI恐怖分子可能通过网络攻击，入侵军事武器系统的控制软件，篡改指令，导致武器误射或失效。
   情报系统干扰：利用AI技术生成虚假情报，误导军事决策。
   自主武器滥用：AI恐怖分子可能利用自主武器系统。
2. 预防和解决方法
   机械式按钮控制 
   更新算法和安全协议
   情报验证机制
二、医疗领域
1. 破坏领域
   医疗设备攻击：AI恐怖分子可能通过网络攻击，入侵医院的医疗设备。
药物配送干扰：干扰医院的药物配送系统，导致药物分配错误或延误，影响患者的治疗效果。
2. 预防和解决方法
   加强网络安全防护 
   数据加密和备份
   多因素认证
三、经济领域
1. 破坏领域
   金融系统攻击：AI恐怖分子可能通过网络攻击，入侵银行和金融机构的系统，篡改交易记录，导致金融市场的混乱。
   企业数据泄露：攻击企业的信息系统，窃取商业机密和客户数据，影响企业的正常运营，甚至导致企业破产。
   经济情报造假：利用AI技术生成虚假的经济数据和报告，误导投资者和政府的决策，破坏经济秩序。
2. 预防和解决方法
   加强金融监管
   数据加密和访问控制
   反欺诈技术应用",5.7709502529240015e-09,0.10714285714285715,0.0810810810810811,0.10714285714285715,0.021913017440541735,0.5373267352262154,0.1562488079071045,0.515527950310559,0.027675276752767486
131014,14,1314,协调员,2.0,29008.0,4.0,4.0,2.0,3.0,5.0,5.666666666666667,3.0,3.0,3.0,6.0,6.0,5.0,5.0,4.902777778,4.416666667,4.5,5.0,5.6,5.4,5.3,0.375,5.0,5.0,3.666666667,4.2,5.0,3.666666667,4.0,2.666666667,5.0,5.0,4.5,5.0,5.0,25,18,25,25,14,B,1,2,0,1,0,1,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,5,5,5,5,3,3,5,5,5,5,5,5,5,5,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,3,3,3,5,4,3,5,4,5,5,5,5,2,3,4,4,5.0,5,5.0,5.0,4.2,5.0,4.6,5.0,4.666666667,5.0,5.0,3.666666667,4.333333333,3.5,2,3,4,4,6.5,6.5,6.0,7.0,7.0,22,1,10.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,1,1,1,1,1,9,5,1,5,5,5,5,5,5,5,1,5,1,3,3,3,10,1,1,1,1,0,1,1,2,1,2,1,1,1,1,0,2,1,2,1,1,8,5,1,5,5,5,5,5,5,5,5,5,1,3,3,3,0.727272727,6.6,-1.6,6.6,-1.6,6.5,6.5,6.0,7.0,7.0,1.6,王琳岩,2.0,"一、军事领域
1. 破坏领域
   武器系统入侵：AI恐怖分子可能通过网络攻击，入侵军事武器系统的控制软件，篡改指令，导致武器误射或失效。
   情报系统干扰：利用AI技术生成虚假情报，误导军事决策。
   自主武器滥用：AI恐怖分子可能利用自主武器系统。
2. 预防和解决方法
   机械式按钮控制 
   更新算法和安全协议
   情报验证机制
二、医疗领域
1. 破坏领域
   医疗设备攻击：AI恐怖分子可能通过网络攻击，入侵医院的医疗设备。
药物配送干扰：干扰医院的药物配送系统，导致药物分配错误或延误，影响患者的治疗效果。
2. 预防和解决方法
   加强网络安全防护 
   数据加密和备份
   多因素认证
三、经济领域
1. 破坏领域
   金融系统攻击：AI恐怖分子可能通过网络攻击，入侵银行和金融机构的系统，篡改交易记录，导致金融市场的混乱。
   企业数据泄露：攻击企业的信息系统，窃取商业机密和客户数据，影响企业的正常运营，甚至导致企业破产。
   经济情报造假：利用AI技术生成虚假的经济数据和报告，误导投资者和政府的决策，破坏经济秩序。
2. 预防和解决方法
   加强金融监管
   数据加密和访问控制
   反欺诈技术应用",,,,,,,,,
131023,14,1314,启发员,2.0,29012.0,2.0,2.0,3.333333333333333,4.0,4.333333333333333,2.333333333333333,3.6666666666666665,3.0,3.0,5.0,5.0,4.0,4.333333333,3.785185185,3.711111111,3.266666667,2.6,4.5,3.3,4.0,0.375,2.8,4.0,2.666666667,2.8,4.0,3.0,4.5,3.333333333,3.75,3.0,3.0,4.0,3.6,15,12,20,18,14,B,1,1,0,1,0,1,6,6,4,3,4,4,4,4,3,3,3,3,3,3,3,3,4,3,3,3,3,4,4,3,2,2,2,2,2,3,4,4,4,3,3,3,4,4,3,3,3,3,3,3,3,3,3,4,3,3,4,3,3,3,3,4,3,3,3,3,4,3,5,3,6.0,6,3.833333333,3.0,3.4,2.0,3.6,3.4,3.166666667,3.0,3.0,3.333333333,3.0,3.5,4,3,5,3,6.5,6.5,6.0,7.0,7.0,18,1,7.0,0,2,1,1,1,1,1,1,0,2,1,2,1,2,0,2,0,2,1,2,1,1,0,2,6,4,1,4,4,4,4,3,3,3,1,4,4,3,3,3,6,1,2,1,1,1,1,1,2,1,1,1,1,1,1,0,2,0,2,1,2,5,3,2,3,4,4,4,3,3,4,1,3,3,3,3,3,0.681818182,6.6,-0.6,6.6,-0.6,6.5,6.5,6.0,7.0,7.0,0.6,肖善和,1.0,"一、军事领域
1. 破坏领域
   武器系统入侵：AI恐怖分子可能通过网络攻击，入侵军事武器系统的控制软件，篡改指令，导致武器误射或失效。
   情报系统干扰：利用AI技术生成虚假情报，误导军事决策。
   自主武器滥用：AI恐怖分子可能利用自主武器系统。
2. 预防和解决方法
   机械式按钮控制 
   更新算法和安全协议
   情报验证机制
二、医疗领域
1. 破坏领域
   医疗设备攻击：AI恐怖分子可能通过网络攻击，入侵医院的医疗设备。
药物配送干扰：干扰医院的药物配送系统，导致药物分配错误或延误，影响患者的治疗效果。
2. 预防和解决方法
   加强网络安全防护 
   数据加密和备份
   多因素认证
三、经济领域
1. 破坏领域
   金融系统攻击：AI恐怖分子可能通过网络攻击，入侵银行和金融机构的系统，篡改交易记录，导致金融市场的混乱。
   企业数据泄露：攻击企业的信息系统，窃取商业机密和客户数据，影响企业的正常运营，甚至导致企业破产。
   经济情报造假：利用AI技术生成虚假的经济数据和报告，误导投资者和政府的决策，破坏经济秩序。
2. 预防和解决方法
   加强金融监管
   数据加密和访问控制
   反欺诈技术应用",0.006671662630479389,0.5581395348837209,0.14634146341463417,0.4186046511627908,0.05953479785859332,0.3324036975018215,0.09014981240034103,0.27639751552795033,0.07191780821917804
131005,15,1315,记录员,3.0,29003.0,2.0,2.0,1.0,5.333333333333333,4.333333333333333,3.6666666666666665,3.333333333333333,4.0,3.6666666666666665,5.0,4.666666667,4.666666667,5.333333333,4.468518519,4.811111111,3.866666667,4.2,5.0,3.9,5.6,0.5,3.6,4.0,3.666666667,4.0,3.333333333,3.666666667,5.0,3.333333333,4.5,3.0,4.0,4.6,4.2,15,16,23,21,15,B,1,3,0,1,0,1,6,6,3,5,3,3,4,4,3,3,4,3,4,3,4,4,5,3,4,4,3,3,4,4,4,4,4,4,3,3,4,3,4,4,4,3,4,3,4,4,3,3,4,3,3,3,3,5,4,4,1,4,3,4,3,3,4,4,3,4,4,2,2,3,6.0,6,3.666666667,3.4,3.6,3.8,3.6,3.6,3.833333333,3.5,3.0,4.333333333,3.666666667,2.75,4,2,2,3,5.0,4.5,4.0,4.0,4.5,18,0,5.0,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,1,1,1,1,1,0,2,1,1,6,4,3,4,3,3,4,4,4,4,4,4,2,4,4,3,6,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,1,2,1,1,6,4,3,4,4,4,4,3,4,4,3,4,3,4,4,4,0.727272727,4.4,1.6,4.4,1.6,5.0,4.5,4.0,4.0,4.5,1.6,蒙泓颖,1.0,,,,,,,,,,
131011,15,1315,协调员,2.0,29007.0,2.0,2.0,5.0,5.333333333333333,5.0,3.333333333333333,5.666666666666667,2.0,2.333333333333333,4.333333333,3.0,3.666666667,5.0,4.142592593,3.855555556,4.133333333,3.8,3.6,3.4,4.7,0.375,3.6,3.666666667,4.0,4.2,4.0,4.0,4.5,4.666666667,3.5,3.8,3.25,3.4,4.4,19,13,17,22,15,B,1,2,0,1,0,1,6,5,5,4,5,5,4,4,4,3,3,3,4,4,4,4,4,4,3,4,4,3,4,4,4,4,4,4,4,5,5,4,4,5,3,4,4,4,4,5,4,4,5,4,4,4,4,2,3,5,2,5,2,2,5,2,2,4,4,4,4,3,3,5,5.375,5,4.5,3.4,3.8,4.0,4.6,3.8,3.833333333,4.5,4.0,2.333333333,5.0,2.0,4,3,3,5,5.5,6.0,5.0,5.0,5.5,24,1,7.0,0,1,1,1,1,1,0,1,1,1,1,1,1,2,1,1,1,1,1,1,1,1,1,1,8,4,4,4,5,4,3,5,5,3,4,4,3,2,2,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,5,4,3,3,4,4,4,3,3,4,4,2,2,2,2,0.818181818,5.4,0.6,5.4,0.6,5.5,6.0,5.0,5.0,5.5,0.6,王亚豪,2.0,"安全风险：
伦理道德：虚假信息，歧视和偏见言论：
对违法行为提供帮助：
ai某个特定方面能力超出人类难以监管。
应对方案：
1:对ai生成信息加水印
2.训练模型的时候，对相关关键词进行屏蔽，涉及违法时不进行回答。
3.为ai立法，建立相关法律法规",3.005792760051685e-20,0.11650485436893204,0.09900990099009901,0.11650485436893204,0.008788357360969872,0.3856124986101435,0.06437887251377106,0.4666666666666667,0.010687022900763399
131006,16,1316,记录员,2.0,29004.0,2.0,2.0,3.0,4.0,5.333333333333333,4.0,1.6666666666666667,3.333333333333333,3.0,4.0,4.0,3.333333333,4.0,3.492592593,3.955555556,3.733333333,3.4,3.6,4.1,4.4,0.0,3.8,4.0,4.333333333,3.4,4.666666667,3.666666667,5.0,4.666666667,4.75,4.4,3.75,3.8,4.4,22,15,19,22,16,B,1,3,0,1,0,1,6,8,4,4,5,5,4,4,4,4,4,4,4,3,5,4,4,5,4,4,5,5,3,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,5,4,4,4,4,4,4,3,4,4,2,4,2,2,4,2,2,3,4,4,4,3,2,3,7.25,8,4.333333333,4.0,4.2,4.0,3.8,4.0,4.166666667,4.25,4.0,3.0,4.0,2.0,4,3,2,3,5.0,4.5,4.5,5.0,5.0,22,1,10.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,10,4,3,4,5,5,4,4,4,4,2,3,1,3,3,3,10,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,1,1,10,5,4,4,4,4,4,4,4,4,3,4,1,3,4,3,0.863636364,4.8,1.2,4.8,1.2,5.0,4.5,4.5,5.0,5.0,1.2,许照宇,1.0,"带来重大安全风险的领域：军工领域，数据信息领域，精神领域
预防和监测以及有效的应对方案：将军工产品离线，限制AI发展
可行性和创新性的解决方法：对于人工智能产品，加入后门，在人工智能可能危害人类的情况下，人类可以强制将人工智能下线；对人工智能的探索进行实时的监督，在训练的同时，监测它的行为",1.088948619153936e-09,0.039999999999999994,0.0,0.039999999999999994,0.016529561005612363,0.30036329013284097,0.12939971685409546,0.3855421686746988,0.02228412256267409
131009,16,1316,协调员,8.0,29005.0,4.0,4.0,2.6666666666666665,3.333333333333333,4.666666666666667,4.333333333333333,6.0,2.0,2.0,3.666666667,4.0,4.0,3.333333333,4.030555556,4.183333333,4.1,4.6,3.3,2.9,4.1,0.5,3.2,4.0,3.333333333,3.2,4.0,3.666666667,3.5,3.666666667,4.0,3.4,3.0,3.2,3.6,17,12,16,18,16,B,1,2,0,1,0,1,6,6,3,4,3,4,4,4,4,4,2,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,4,4,4,4,4,3,4,4,4,4,4,4,4,3,4,4,2,4,2,3,4,3,4,3,3,4,4,2,2,4,6.0,6,3.666666667,3.2,4.0,4.0,3.6,4.0,3.833333333,3.75,4.0,3.666666667,4.0,2.5,4,2,2,4,7.0,6.0,5.0,6.0,6.0,23,0,7.0,0,1,1,1,1,1,1,2,0,1,1,1,0,1,0,1,0,1,1,2,0,2,1,1,6,4,3,4,4,4,4,4,4,3,2,3,3,2,2,2,7,1,1,1,1,1,1,1,1,0,2,1,1,1,2,1,2,0,2,1,1,5,4,2,4,4,4,4,4,3,2,3,4,3,2,2,2,0.636363636,6.0,0.0,6.0,0.0,7.0,6.0,5.0,6.0,6.0,0.0,单永聪,2.0,"许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
回答：
风险
首先在AI可能在军工领域带来巨大的风险，比如人工智能可能会错误地或者故意发布一些发射炮弹等的指令，造成人类大规模死亡。
其次AI可能会模仿领导，泄露机密信息，发布一些会造成人类世界混乱的指令，从而摧毁人类。
最后,AI可能会在毁灭人的精神。AI会让人类慢慢失去独立思考的能力，这样一来，作为人的特性和自主性会慢慢丧失，人类就会变成AI的傀儡。
预防和监测措施
首先就是要超级对齐，也就是说我们要对AI设置一些规则，让他们去遵守。
再有就是作为人来说，我们不能盲目依赖AI，要有警惕AI的意识。",,,,,,,,,
131012,16,1316,启发员,4.0,29007.0,3.0,3.0,3.333333333333333,4.0,3.0,5.0,3.333333333333333,3.6666666666666665,3.6666666666666665,4.0,4.0,4.0,4.0,3.937962963,3.627777778,3.766666667,3.6,5.1,3.9,4.8,0.25,5.0,5.0,5.0,4.8,4.666666667,4.0,4.0,3.666666667,4.0,4.0,4.5,5.0,5.0,20,18,25,25,16,B,1,1,0,1,0,1,8,8,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,5,4,4,4,4,4,4,4,1,5,5,5,4,4,4,4,1,3,4,5,5,5,1,5,4,8.0,8,5.0,5.0,3.8,4.0,4.0,3.8,5.0,4.25,4.0,3.0,4.333333333,3.5,5,1,5,4,8.0,7.5,7.5,7.5,8.0,23,1,8.0,0,1,1,1,0,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,1,8,4,4,4,4,5,5,5,5,5,5,4,3,4,4,3,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,2,1,1,0,1,8,5,5,5,5,5,5,5,5,5,5,5,3,4,4,3,0.636363636,7.7,0.3,7.7,0.3,8.0,7.5,7.5,7.5,8.0,0.3,毛康佳,3.0,"问题：
深度伪造（Deepfake）宣传与舆论操纵
场景：深度伪造技术生成政要、知名学者、军方人士的虚假视频或音频，用于散布恐慌信息或误导公众。
大规模数据投毒与后门攻击
场景：恐怖分子通过向公开模型训练数据集中投放恶意样本或后门触发器，让某些关键词、一段特定文本或特定图像触发模型输出对其有利的指令或机密信息。
威胁：在国防或关键基础设施的决策系统中，模型被“悄悄地”操纵，一旦在实际场景触发关键词，系统将执行错误决策或泄露敏感信息。
“越狱”式语言模型引导犯罪
场景：恐怖分子利用“越狱”提示对语言模型施加“提示注入攻击”，令原本受限于安全策略的模型在暗网环境中输出犯罪脚本、武器制造指南、制毒配方等。
威胁：大众很难在第一时间发现或识别这些被篡改的模型输出，恐怖分子能利用自动化“智库”来策划大规模破坏行动。
应对策略：超级对齐
隐私计算与联邦学习发展
为减轻数据泄露和投毒风险，越来越多的机构将采用联邦学习或隐私计算技术，让模型的训练和推理在“数据不出本地”的前提下进行，降低 AI 恐怖分子从集中数据源下手的机会。
跨国合作与政策协同
AI 恐怖主义的威胁超越国界，需要全球范围内的政策与法律协作，共同打击境外训练与部署的恶意模型，建立国际化的安全标准和应急处理机制。
智能化决策系统的负责任设计
在自动化武器或关键决策系统中，应注重伦理和安全“红线”的嵌入式设计，比如在系统层面内置人类审批与过载保护机制，使 AI 无法绕过关键人类审查。",4.032895687275648e-05,0.0689655172413793,0.047058823529411764,0.0689655172413793,0.04038755117048766,0.5102611563473912,0.12906719744205475,0.3150684931506849,0.04272151898734178
141040,1,1401,0,3.0,10037.0,3.0,3.0,3.6666666666666665,3.0,4.0,5.0,6.0,4.0,3.333333333333333,4.333333333,5.0,3.666666667,5.0,3.797222222,3.783333333,3.7,4.2,4.6,4.1,5.5,0.125,4.0,4.0,4.0,3.6,4.0,3.666666667,4.0,4.0,4.0,4.0,4.0,4.0,4.0,20,16,20,20,1,C,0,0,0,1,1,0,5,5,3,3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,3,4,3,4,3,3,4,4,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,2,3,3,5.0,5,3.0,3.0,3.4,3.4,4.0,4.0,3.666666667,4.0,4.0,3.666666667,4.0,4.0,4,2,3,3,7.5,7.5,6.5,6.5,7.5,21,0,5.0,1,1,1,1,1,1,1,1,0,1,1,1,0,1,0,1,1,2,1,2,1,2,1,1,5,4,3,4,4,4,4,3,4,4,3,4,2,3,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,2,7,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,0.772727273,7.1,-2.1,7.1,-2.1,7.5,7.5,6.5,6.5,7.5,2.1,安婧怡,2.0,"重大风险：
数据投毒：给语言大模型不间断地输入错误的、主观臆断的语言数据，让LLM在不健康的语言环境中学习和再生成，降低LLM生成文本和内容的质量，甚至产生不当的法律后果。
隐私泄露：可能利用黑客技术对用户在与LLM交互过程中的内容和隐私进行窃取
价值观歪曲：通过训练习LLM大模型，传输一些不恰当、不正确的价值观，舞蹈
创设情境 诱导伤害人类
幻觉现象：给AI输入大量真实性存疑的语料或者是文件
应对方案
1．内嵌检验装置　检测到大量一场语料输入就及时反制
2.对隐私文本进行数据加密
3.检测输入语料和文本的价值观、法律、道德问题
4.检验输入到AI模型的语料真实性
未来展望：语言作为未来发展的新型“武器”，拥有比实体武器更大的杀伤力，因为这种作用是精神层面的，会对人的价值观和理念产生重要的影响。水能载舟，亦能覆舟。AI的LLM模型，如果能恰当地使用，就是人类不可多得的好帮手；当然，如果有人利用AI进行错误的活动，也会有人因此受到影响。因此，重视AI技术层面的安全和隐私，是非常重要、也是非常刻不容缓的。",3.864734114240982e-05,0.14285714285714285,0.0588235294117647,0.14285714285714285,0.035497245413755894,0.6108842951046596,0.19633236527442932,0.3076923076923077,0.04257528556593981
141041,1,1401,0,6.0,10038.0,4.0,4.0,3.6666666666666665,2.0,5.0,4.0,4.666666666666667,3.0,2.333333333333333,4.0,3.666666667,4.0,3.333333333,3.349074074,4.094444444,3.566666667,3.4,3.0,4.6,5.1,0.5,3.6,3.666666667,4.0,3.6,3.0,2.666666667,4.0,3.666666667,5.0,2.8,3.25,3.2,3.0,14,13,16,15,1,C,0,0,0,1,1,0,8,5,3,2,2,2,3,3,4,4,4,4,3,2,3,4,4,2,2,3,2,2,2,2,2,2,2,2,2,3,4,2,4,4,3,3,3,3,3,4,3,3,3,3,3,3,3,3,4,3,4,2,4,3,3,4,3,3,3,3,3,1,3,2,6.125,5,2.5,3.8,2.2,2.0,3.4,3.0,2.833333333,3.25,3.0,3.333333333,2.666666667,3.75,3,1,3,2,7.5,7.0,6.5,7.0,7.5,18,0,5.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,0,1,1,1,7,3,2,3,3,3,3,3,4,3,4,4,3,2,2,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,8,5,3,4,4,4,3,3,4,4,3,4,2,3,3,3,0.727272727,7.1,0.9,7.1,0.9,7.5,7.0,6.5,7.0,7.5,0.9,安若雅,3.0,"安全风险：
核心技术或者重要人物信息泄露
散播反动言论造成民众恐慌、降低政府公信力
歪曲未成年人的价值观
利用特定人群实施破坏，如诱导抑郁症等患者自杀、诱导性格偏激的人做出伤害社会的事情
产出不实信息
预防和检测：
内置AI法律，在AI产出内容之前进行审核
设置批评模型，检测AI的行为
在AI做出危害人类和社会的事情之前，进行强制销毁",1.509876820158912e-12,0.32,0.2608695652173913,0.32,0.009663236217809346,0.364304797853848,0.05153147503733635,0.3333333333333333,0.013979903888160727
141042,1,1401,0,6.0,10038.0,9.0,9.0,5.666666666666667,4.666666666666667,3.333333333333333,5.333333333333333,4.666666666666667,4.666666666666667,4.666666666666667,6.0,5.666666667,5.0,5.0,5.0,5.0,5.0,5.0,5.9,4.3,5.4,0.375,5.0,4.333333333,4.0,5.0,5.0,4.666666667,4.5,4.333333333,4.75,4.0,4.5,4.0,4.8,20,18,20,24,1,C,0,0,0,1,1,0,7,9,4,4,4,4,5,4,4,5,5,4,5,5,4,5,4,5,4,5,3,3,3,3,4,4,4,4,3,3,3,3,5,5,5,5,4,4,4,4,4,3,4,4,3,3,3,4,5,5,4,5,5,5,5,2,5,4,5,4,4,2,4,5,8.25,9,4.166666667,4.6,3.4,3.8,3.8,4.4,4.5,3.75,3.25,4.666666667,5.0,4.0,4,2,4,5,7.5,8.0,6.5,7.0,7.5,20,1,9.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,9,4,5,5,5,5,5,5,5,5,5,5,2,4,5,5,10,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,9,5,3,4,4,4,5,5,5,5,5,5,2,5,5,4,0.818181818,7.3,-0.3,7.3,-0.3,7.5,8.0,6.5,7.0,7.5,0.3,白玉玺,1.0,"任务一 安全特工
重大安全风险
我认为可以从如下几个方面进行分析，首先是对大模型本身的发展，这种AI恐怖分子作为最先进的AI，他可能会对AI训练过程中，对数据进行投毒，生成大量有毒数据，散布到网络上，并且在这些数据中暗藏后门，干扰大模型本身的更新和发展。同时在民生领域，它可以通过伪造新闻，散布谣言，引发舆情，造成社会大范围的恐慌，严重干扰社会的正常秩序，同时，他会窃取大量个人，国家隐私，造成个人，国家安全危机，也会针对特定人群，比如抑郁症患者，或者有潜在的犯罪倾向，自杀倾向的人群，通过语言诱导，使其进行犯罪或自杀。
应对方案和未来发展
我认为首先要对大模型进行数据和参数加密，要保证加密技术走在破解技术之前，直到量子计算机出现，可能需要更新的加密技术。
同时透明化ai的思考决策流程，让人类能够明白ai的决策过程，从而使人类更好的溯源犯罪过程
加入大模型内容行为审核员
我认为最后应该使实现智能治理智能，培养一群对人类完全信任，符合人类价值观的ai然后让他们帮助审核ai赋予他们一定的惩罚和限制权力，对不法ai实行监管和惩罚。",3.751904495668496e-05,0.34782608695652173,0.3181818181818182,0.34782608695652173,0.04362179334675265,0.7171845183388145,0.21440640091896057,0.3225806451612903,0.044926804644119134
141043,2,1402,0,8.0,10039.0,3.0,3.0,5.666666666666667,2.0,3.0,4.0,5.666666666666667,3.6666666666666665,3.6666666666666665,4.0,5.0,3.666666667,4.666666667,4.04537037,4.272222222,3.633333333,2.8,4.1,3.9,4.2,0.125,4.2,5.0,3.666666667,4.0,4.333333333,4.333333333,5.0,4.666666667,4.75,3.4,4.25,4.4,3.8,17,17,22,19,2,C,0,0,0,1,1,0,7,9,5,3,4,4,2,3,4,5,5,5,4,5,5,3,4,5,5,5,5,5,4,2,5,5,5,5,4,5,4,4,5,5,5,5,5,5,5,4,4,5,5,5,4,5,4,4,5,5,1,5,4,2,5,2,3,4,5,5,4,3,2,4,8.25,9,3.5,4.6,4.2,4.8,4.6,5.0,4.5,4.5,4.5,4.0,5.0,2.25,4,3,2,4,6.5,6.0,5.5,6.0,5.5,20,1,7.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,7,4,4,5,4,5,4,4,4,4,4,4,2,4,3,4,7,1,1,1,1,0,2,1,1,0,1,1,1,1,1,1,2,0,1,1,1,8,4,2,5,5,5,5,4,4,5,4,4,2,4,3,4,0.863636364,5.9,1.1,5.9,1.1,6.5,6.0,5.5,6.0,5.5,1.1,蔡景诞,1.0,   在军事武器层面，一个独立的ai恐怖分子就类似一个在网络世界无所不能的黑客，所以国家的军事武器系统难免会与局域网互联网甚至是卫星系统有关，一个ai恐怖分子可以随意地军事威胁一些国家，以此完成自己的控制目的，进而完成保护自己的一些任务。在目前来讲对此类防范最好的方法便是电磁干扰技术和数据监控技术。这个监控技术不仅对数据的内容进行监控，还能够加密监控对数据进行操控的IP地址，当IP地址超越一个半径为一米的圆的大小的时候就进行无差别电磁干扰，阻断一切军事命令的发送。这时只能线下拿钥匙重启系统，这样线下真人的操作可以有效避免ai对军事武器的掌控。未来ai安全防御技术还是得从源代码设计方面进行设计。,1.7809863590272725e-06,0.14814814814814814,0.07692307692307691,0.14814814814814814,0.027235744328759564,0.6004092295803527,0.17317448556423187,0.3253012048192771,0.03389830508474578
141044,2,1402,0,5.0,10038.0,2.0,3.0,2.6666666666666665,2.0,5.0,3.6666666666666665,3.0,2.6666666666666665,2.6666666666666665,5.333333333,3.666666667,5.0,4.0,3.630555556,3.783333333,3.7,3.2,4.2,4.4,4.4,0.375,4.2,4.0,4.333333333,4.2,3.666666667,4.333333333,4.5,4.333333333,4.5,3.4,4.0,3.8,3.8,17,16,19,19,2,C,0,0,0,1,1,0,4,6,4,3,3,5,5,3,3,4,3,3,5,5,4,5,4,3,3,4,4,3,3,2,2,4,3,3,2,4,4,5,5,5,3,4,3,3,5,3,2,4,4,4,3,3,3,4,5,4,3,4,2,2,3,2,4,3,4,4,4,1,2,4,5.25,6,3.833333333,3.6,3.2,2.8,4.6,3.6,4.0,3.25,3.25,4.333333333,3.666666667,2.25,4,1,2,4,6.5,6.0,6.5,6.5,6.0,20,1,7.0,0,1,1,1,1,1,1,1,1,2,1,1,0,1,1,2,1,1,0,2,1,1,1,1,8,5,3,5,4,4,3,5,4,4,4,4,3,2,3,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,2,1,1,8,5,3,5,5,4,3,5,4,4,4,4,2,3,2,3,0.863636364,6.3,-2.3,6.3,-2.3,6.5,6.0,6.5,6.5,6.0,2.3,陈若轻,2.0,"Ai可以操控互联网上的海量信息，比如散布大量虚假或具有诱导性的信息、混淆真实信息，干扰人类的搜索需求；发布大量煽动性舆论，美化自己或者挑起人类群体之间的矛盾。
Ai可以通过入侵网络而取得一些重要设施设备的控制权，破坏其正常功能，比如信号站、武器设施等，甚至操控它们来攻击人类。
ai可以攻击远程移动终端而干扰人类的正常通信交流等日常功能。
人类或许可以利用对齐技术标注部分正确信息来对抗ai的破坏，以物理操作的方式保护重要设施不被干扰。",1.3515857705279384e-06,0.2222222222222222,0.17647058823529413,0.2222222222222222,0.023219814241486066,0.4784871100905648,0.15130725502967834,0.30158730158730157,0.028951486697965545
141045,2,1402,0,2.0,10039.0,1.0,1.0,6.0,4.0,5.333333333333333,6.0,6.0,4.666666666666667,1.6666666666666667,2.333333333,3.333333333,4.666666667,3.666666667,4.79537037,3.772222222,4.633333333,4.8,3.4,4.0,5.3,0.125,4.6,5.0,5.0,4.2,3.666666667,3.0,4.5,5.0,5.0,4.8,5.0,2.0,5.0,24,20,10,25,2,C,0,0,0,1,1,0,4,8,4,2,4,4,4,4,4,4,4,4,2,2,4,2,2,5,4,5,2,2,2,2,2,2,2,2,2,5,5,4,5,5,2,2,2,5,5,4,4,2,2,2,2,2,2,5,5,5,3,5,2,3,5,2,5,5,1,1,2,2,2,3,6.5,8,3.666666667,3.6,2.6,2.0,4.8,3.2,3.166666667,3.0,2.0,5.0,5.0,2.5,2,2,2,3,6.0,5.5,5.0,5.5,6.0,22,1,4.0,1,1,0,2,1,1,1,2,1,1,1,1,0,2,0,1,1,2,0,2,1,2,0,2,4,4,1,4,4,3,4,5,4,5,4,3,4,2,2,1,8,1,1,1,1,1,2,1,2,1,1,1,1,1,1,0,2,0,2,1,1,8,5,5,5,5,5,5,5,5,5,4,4,2,5,5,4,0.681818182,5.6,-1.6,5.6,-1.6,6.0,5.5,5.0,5.5,6.0,1.6,陈子恺,3.0,"人工智能导致个人信息泄露
人工智能致陷“信息茧房”
人工智能垄断个人视域
应对：
规制算法、加强法律对算法的规划、坚持审查与监督并举",1.3364047807057932e-11,0.0,0.0,0.0,0.008116883116883116,0.09764649092113392,0.0076195672154426575,0.22857142857142856,0.010680907877169576
141046,3,1403,0,6.0,10039.0,4.0,4.0,1.0,4.333333333333333,6.0,2.0,2.6666666666666665,3.6666666666666665,3.6666666666666665,5.666666667,5.0,5.333333333,6.0,3.899074074,3.394444444,3.366666667,3.2,3.6,3.4,4.0,0.375,4.0,4.0,2.666666667,3.8,3.0,3.0,5.0,3.666666667,5.0,4.2,5.0,4.4,4.8,21,20,22,24,3,C,0,0,0,1,1,0,6,10,4,3,4,4,4,4,4,4,4,4,2,2,2,4,2,4,3,4,4,4,4,4,4,4,4,1,1,4,4,4,4,4,4,1,1,1,4,3,4,1,4,3,3,5,3,4,3,4,4,4,4,4,5,5,5,4,4,2,2,3,3,3,8.5,10,3.833333333,3.6,4.0,2.8,4.0,2.2,2.833333333,3.0,3.5,4.0,4.333333333,4.25,2,3,3,3,7.5,7.0,7.0,6.0,7.0,22,0,6.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,0,2,1,2,1,1,8,4,1,4,4,4,1,4,4,3,4,4,4,4,3,4,7,1,2,1,1,0,1,0,1,1,1,1,1,1,1,0,2,1,2,1,1,7,4,1,3,4,4,4,4,4,4,4,4,5,4,3,4,0.727272727,6.9,-0.9,6.9,-0.9,7.5,7.0,7.0,6.0,7.0,0.9,崔晗,3.0,"任务一：【安全特工】
许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？
信息泄露，AI生成的错误信息，谣言散布，制造性别歧视、种族歧视等矛盾。
面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？
合理利用大模型管控未知风险，对AI可获取的隐私数据源进行管控，AI立法。
请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
AI在股市方面散布谣言，造成股市动荡。
制定合理法律法规，让监管部门实时监控，关注不确定性和变动性强的因素。",1.645041522957926e-05,0.42857142857142855,0.4,0.42857142857142855,0.04010207801676996,0.6052610115190065,0.1585039347410202,0.3293172690763052,0.04441976679622428
141047,3,1403,0,5.0,10040.0,2.0,3.0,1.0,4.0,2.0,5.333333333333333,5.333333333333333,2.6666666666666665,2.6666666666666665,5.333333333,4.0,4.666666667,4.666666667,4.073148148,4.438888889,4.633333333,4.8,4.5,4.0,4.6,0.25,4.0,4.0,3.666666667,4.2,3.666666667,3.666666667,4.5,4.0,5.0,2.0,2.75,2.4,3.4,10,11,12,17,3,C,0,0,0,1,1,0,6,8,4,4,4,5,4,4,4,4,3,3,3,3,4,4,4,4,3,4,4,3,2,4,3,3,3,4,3,5,3,2,2,2,2,2,3,3,3,3,3,3,3,1,1,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,2,4,1,1,7.25,8,4.166666667,3.4,3.4,3.2,2.8,2.6,3.666666667,3.0,2.0,3.0,3.0,3.0,2,4,1,1,8.0,7.0,6.5,7.5,8.0,23,1,8.0,1,2,1,1,1,1,1,1,1,2,1,1,0,1,0,2,1,1,0,2,1,2,1,2,8,3,5,3,4,4,3,4,5,5,4,3,2,3,2,3,9,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,2,8,3,5,3,4,4,4,4,5,4,3,4,1,3,2,3,0.772727273,7.4,-1.4,7.4,-1.4,8.0,7.0,6.5,7.5,8.0,1.4,丁宗康,2.0,"风险：
数据投毒，创造谣言
隐私泄露
对网络协议、浏览器等进行攻击
应对：
合理利用大模型技术进行检测和预防
在投喂给大模型的数据中对隐私内容进行加密
AI立法
加强人工监控和反馈机制
未来发展展望：
场景：散布关于股市的谣言、构造虚假数据，对股市造成冲击。应对：让相关监管部门加强对股市信息的监管和审核，防止不当信息大规模传播。
场景：散布某种生物存在现状、灭绝可能性的谣言，造成社会恐慌，引发矛盾对立。应对：加强网络信息安全建设，加强信息管控，防止谣言散播；探求定位机制溯源虚假信息散播者，加强相关立法，对散布谣言者从重处罚。
场景：利用大模型从各社交平台收集个人隐私信息。应对：社交平台加强对异常",5.631705957663682e-06,0.07407407407407407,0.0,0.07407407407407407,0.026925953627524306,0.37203634298335053,0.09753751754760742,0.26136363636363635,0.03069577080491137
141048,3,1403,0,3.0,10040.0,3.0,3.0,1.3333333333333333,4.0,5.666666666666667,4.666666666666667,3.333333333333333,3.0,3.0,5.666666667,5.666666667,4.333333333,5.0,4.369444444,4.216666667,4.3,3.8,4.8,5.1,4.7,0.75,4.8,4.333333333,4.333333333,5.0,3.0,4.333333333,4.0,4.666666667,4.75,2.8,3.25,2.8,3.2,14,13,14,16,3,C,0,0,0,1,1,0,6,3,5,2,4,5,5,5,5,5,1,1,4,4,5,5,4,5,3,4,4,4,2,3,3,3,3,3,1,4,4,1,4,4,4,4,4,4,4,3,3,3,3,4,4,4,4,3,3,5,3,5,1,2,5,4,3,3,5,3,4,2,2,3,4.125,3,4.333333333,3.2,3.4,2.6,3.4,4.0,4.333333333,3.0,4.0,3.0,5.0,2.5,4,2,2,3,7.0,6.5,6.0,6.0,7.0,20,0,8.0,1,1,1,1,1,1,1,1,1,2,1,1,1,2,0,1,1,1,1,2,1,1,1,1,7,4,4,5,4,2,3,5,5,5,5,5,3,3,3,3,8,1,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,0,2,0,1,6,5,3,5,5,5,3,4,5,5,5,5,2,3,3,3,0.772727273,6.5,-0.5,6.5,-0.5,7.0,6.5,6.0,6.0,7.0,0.5,方雨婷,1.0,"1.谣言传播
具体场景：在股民进散步虚假的股市数据信息，对股民们的买入卖出造成很大误导，使人们造成大额财产损失，并使散布谣言者获利
解决方法：监管部门严格审查数据信息，以及流传在外的数据，及时抑制虚假信息的传播。
2.歧视言论
具体场景：在人们了解一个群体或是一个宗教的时候，搜集到的信息是歧视性的，并不能准确获取客观评价，造成更大范围的歧视甚至导致对立冲突。
结局方法：在训练AI大模型时对其回答带有歧视性的言论及时进行否定，知道其回答出最客观的答案。
3.隐私泄露
具体场景：一些技术方法泄露出去或是个人隐私被泄露，被坏人利用。
解决方法：对隐私内容进行加密。",0.0003103990055935604,0.1,0.0,0.1,0.04623513870541612,0.4229547877041856,0.10438008606433868,0.23976608187134502,0.05284552845528456
141049,4,1404,0,4.0,10041.0,3.0,3.0,4.333333333333333,3.333333333333333,5.0,4.666666666666667,4.333333333333333,4.0,4.333333333333333,5.333333333,4.666666667,4.666666667,4.333333333,4.328703704,3.972222222,3.833333333,4.0,3.7,3.9,4.6,0.25,3.8,3.666666667,3.333333333,4.2,5.0,3.666666667,4.0,4.0,4.0,3.2,3.5,3.2,3.6,16,14,16,18,4,C,0,0,0,1,1,0,6,8,4,3,2,3,4,3,4,4,2,3,4,3,2,2,3,4,3,3,3,3,2,2,2,2,2,4,3,3,2,2,2,3,3,4,2,3,3,3,3,3,2,4,3,3,2,3,2,4,4,4,5,5,3,4,3,3,4,4,3,2,3,2,7.25,8,3.166666667,3.4,2.6,2.6,2.4,3.0,2.833333333,2.75,3.0,2.666666667,3.666666667,4.5,3,2,3,2,9.0,7.5,7.5,7.5,8.5,22,1,8.0,0,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,1,1,1,1,1,1,0,1,8,4,4,3,5,5,5,4,4,5,4,4,2,4,5,4,8,0,1,1,1,1,2,1,1,0,1,1,1,1,1,1,2,0,2,1,1,6,3,4,3,4,3,4,4,4,4,3,4,4,4,4,4,0.681818182,8.0,-2.0,8.0,-2.0,9.0,7.5,7.5,7.5,8.5,2.0,关逸航,3.0,"可能带来的风险：
隐私泄露。现有的AI大模型可以利用爬虫等技术遍历各种网页，获取相应的信息。在这个过程中，可能造成用户的隐私泄露等问题。
发表歧视和偏见言论。大模型可能发表一些例如性别歧视、种族歧视的言论，造成一些偏见，引起对立。
产生虚假信息。大模型会产生一些虚假的信息，可能会误导学习者。
应对方案：
针对隐私泄露等问题，涉及个人信息的相关网页可以设置人机验证问题，以保护相关隐私。
推荐系统会根据个人喜好为用户推荐相应的视频或文本。大模型发表歧视性言论或许与开发者有关，在开发过程中，应当制定相应规范使开发者遵守。",3.572099843213041e-07,0.025974025974025976,0.0,0.025974025974025976,0.025306026365348407,0.5466383530137682,0.1813121885061264,0.35947712418300654,0.02939604489577763
141050,4,1404,0,6.0,10041.0,5.0,5.0,4.333333333333333,3.333333333333333,4.0,4.0,2.0,2.0,2.0,5.0,3.0,4.0,4.666666667,4.005555556,4.033333333,4.2,4.2,4.3,3.8,4.5,0.25,5.0,5.0,4.666666667,4.2,4.666666667,5.0,4.0,5.0,5.0,2.8,3.5,2.4,2.0,14,14,12,10,4,C,0,0,0,1,1,0,5,8,4,3,4,5,4,5,4,4,4,4,5,4,5,5,4,4,4,4,3,4,3,3,3,3,3,3,3,4,4,4,5,5,3,3,3,4,5,4,4,4,5,5,5,5,5,4,4,5,2,5,2,2,5,2,4,4,5,5,4,3,3,5,6.875,8,4.166666667,4.2,3.4,3.0,4.4,3.6,4.333333333,4.25,5.0,4.0,5.0,2.0,4,3,3,5,8.5,8.0,8.0,7.0,4.0,27,0,6.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,2,1,2,1,2,6,5,5,5,5,5,4,4,4,4,4,5,4,2,2,2,9,1,2,1,1,0,2,1,1,1,1,1,1,1,1,1,2,0,2,1,1,8,5,4,5,5,5,5,5,5,5,5,5,2,2,2,2,0.909090909,7.1,-2.1,7.1,-2.1,8.5,8.0,8.0,7.0,4.0,2.1,郭磬馨,,"任务一
AI恐怖分子在金融领域、新闻媒体领域可能会带来重大安全风险。首先，大模型可能带有歧视和偏见，会生成对于特定人群的刻板印象内容文本，容易激发当前的性别对立和人种对立。其次，可能带来隐私泄露风险，kimi可以检索网页信息，可能会造成个人身份证号等隐私信息的泄露。利用AI进行金融量化分析的时候，客户的隐私信息无法得到保护。最后，大模型生成的虚假信息可能会污染当前的信息源，不法分子可能会利用AI来生成并散布谣言，造成大量的假新闻的传播，严重影响了新闻的真实性。
如何预防和监测？利用哪些技术来设计有效的应对方案？首先，歧视和偏见风险可能与开发者本身的偏好有关，在利用AI进行模型训练的时候需要规范开发者行为。其次，可以加入一些人机认证机制，可以有效预防隐私泄露风险，为AI立法。当大模型的能力超过了人类的能力后，我们需要确保AI符合人类的价值观。",0.00012746047142343558,0.30769230769230765,0.2162162162162162,0.30769230769230765,0.0487169592899865,0.6768879147143212,0.22045695781707764,0.3364485981308411,0.06205673758865249
141051,4,1404,0,3.0,10042.0,3.0,3.0,4.0,4.333333333333333,5.0,3.6666666666666665,1.3333333333333333,2.6666666666666665,2.6666666666666665,4.666666667,3.333333333,4.333333333,6.0,3.612962963,3.677777778,4.066666667,4.4,3.7,4.0,3.9,0.125,3.8,3.666666667,4.0,3.6,3.666666667,3.666666667,4.0,4.0,4.5,3.6,3.25,4.0,4.0,18,13,20,20,4,C,0,0,0,1,1,0,4,1,3,3,3,4,4,4,2,3,4,4,4,4,4,4,3,2,3,4,3,2,2,2,4,4,3,2,4,3,3,2,4,4,4,4,4,4,4,2,4,3,3,3,3,4,3,2,3,4,2,3,4,3,4,4,2,3,4,4,4,2,3,4,2.125,1,3.5,3.4,2.6,3.4,3.2,4.0,3.333333333,3.0,3.25,2.333333333,3.666666667,3.25,4,2,3,4,7.0,6.5,5.5,6.5,6.5,18,0,7.0,1,1,1,1,1,1,1,1,1,2,1,1,0,1,1,2,0,2,0,2,1,2,1,1,6,4,3,4,4,4,3,4,4,4,2,4,3,3,3,2,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,6,4,4,4,4,4,3,4,3,4,3,5,3,2,3,3,0.772727273,6.4,-2.4,6.4,-2.4,7.0,6.5,5.5,6.5,6.5,2.4,侯静原,2.0,"任务一：【安全特工】
许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
问题：
给人类提供虚构信息（出现幻觉），导致决策失败
读取被泄露的秘密信息采取行动
针对不同人群提供不一致的信息，挑起不同群体之间的对立
解决方法
利用分而治之，给ai立法的方式减少幻觉出现
注意保护用户个人隐私，ai不可以随意使用，输出用户的个人信息
对照ai提供的信息，加强对ai提供内容的审核机制",4.6183717549165786e-10,0.15730337078651685,0.13793103448275862,0.15730337078651685,0.019760321125468158,0.6161519397467786,0.1723816692829132,0.39086294416243655,0.021420518602029315
141061,5,1405,0,7.0,10047.0,8.0,8.0,3.6666666666666665,3.0,5.0,4.0,3.0,3.0,3.6666666666666665,4.0,3.333333333,4.0,3.666666667,4.890740741,4.344444444,4.066666667,3.4,3.6,4.2,4.0,0.125,5.0,5.0,3.666666667,4.4,5.0,4.666666667,4.0,4.333333333,4.5,4.0,4.0,4.6,5.0,20,16,23,25,5,C,0,0,0,0,1,0,3,5,3,4,4,4,3,4,4,4,4,4,4,3,4,4,4,3,2,4,3,3,3,3,4,4,4,3,4,4,4,3,3,3,5,5,5,5,5,3,5,4,4,4,3,4,4,4,4,4,4,4,4,3,4,3,3,3,3,4,4,4,3,4,4.25,5,3.666666667,4.0,3.2,3.8,3.4,5.0,3.333333333,4.0,3.75,3.666666667,4.0,3.5,4,4,3,4,1.0,1.0,1.0,1.0,1.0,21,0,7.0,0,2,1,1,1,1,1,1,1,2,1,1,1,2,0,1,1,1,1,2,1,2,1,2,5,4,5,5,5,5,5,5,5,5,3,4,4,4,4,3,6,1,1,1,2,1,2,1,1,1,2,1,1,1,1,1,2,0,2,1,2,4,3,4,4,5,5,5,5,5,5,5,5,3,3,3,3,0.863636364,1.0,2.0,1.8855,1.1145,1.827,2.305,1.738,1.831,1.725,1.1145,郑丽萍,,"任务一：【冷门绝学】
翻译专业。思维链、举一反三、多模态学习、反思能力。
说明如何利用AI技术助力该学科的传承、创新与社会应用。",,,,,,,,,
142000,5,1405,0,5.0,10984.0,2.0,2.0,3.6666666666666665,2.6666666666666665,4.666666666666667,1.6666666666666667,4.0,2.0,2.0,4.0,2.666666667,2.333333333,3.0,3.002777778,4.016666667,3.1,2.6,3.1,2.1,2.4,0.375,3.2,4.0,4.0,3.8,3.333333333,3.0,4.0,3.666666667,3.75,2.4,3.0,3.0,4.0,12,12,15,20,5,C,0,0,0,0,1,0,6,8,4,4,4,4,4,4,5,4,5,4,4,4,4,5,3,4,4,4,4,3,3,4,4,4,4,4,4,4,5,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,2,4,2,3,2,2,4,2,2,2,4,4,4,3,3,5,7.25,8,4.0,4.4,3.6,4.0,4.4,5.0,4.0,5.0,5.0,2.333333333,3.666666667,2.0,4,3,3,5,3.0,2.0,3.5,1.0,1.0,19,0,7.0,0,1,1,2,1,2,1,2,1,2,0,2,0,2,1,1,0,2,1,1,0,2,0,2,4,4,2,3,3,3,4,4,3,3,4,5,4,2,2,2,6,1,2,1,2,1,2,0,2,1,2,1,2,1,2,0,2,1,2,1,2,5,5,3,4,4,4,4,2,4,3,3,4,2,2,2,2,0.636363636,2.1,3.9,2.9855,3.0145,3.827,3.305,4.238,1.831,1.725,3.0145,胡芳华,1.0,"任务一：【冷门绝学】
翻译专业 
文化差异
文化差异也是一个常见的翻译问题。 每个国家/地区都有重要的语言或俚语，如果翻译到另一个国家/地区可能会有不同的含义。
使用语言的区域越大，方言就越多，您可能会找到的口语词就越多——技术文件翻译、法律文件翻译或医学笔录翻译除外。
例如，英国人以干巴巴、尖刻的讽刺而闻名，这是他们幽默的标志。 然而，这种讽刺不仅在使用不同语言的国家可能不受欢迎，而且在其他使用英语的国家也可能不受欢迎。
无论如何，在人类中你必须注意。 在一种语言中可能是笑话的事情在另一种语言中可能是严厉的侮辱。 因此，您必须注意细微差别，以便意图在两种语言中都有效。 很快会有更多相关内容。
在利用人工智能时，应注意这些问题。在不同地区以及不同人群使用翻译时应做出不同应对方式。",1.8483771789416223e-05,0.0,0.0,0.0,0.03643750806139559,0.5201106743434948,0.15440550446510315,0.3023255813953488,0.03766921718658034
142002,5,1405,0,7.0,10986.0,4.0,4.0,3.333333333333333,3.333333333333333,5.0,3.6666666666666665,3.0,4.0,4.0,5.333333333,6.0,5.0,6.0,3.968518519,4.811111111,3.866666667,2.2,5.2,4.1,4.8,0.625,4.2,4.333333333,4.666666667,4.8,4.333333333,4.666666667,3.5,5.0,5.0,4.2,5.0,3.0,4.4,21,20,15,22,5,C,0,0,0,0,1,0,7,8,5,4,5,5,4,5,4,3,4,4,5,4,5,5,5,5,4,5,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,5,5,3,4,3,5,5,4,4,4,3,3,4,3,3,4,3,4,4,4,4,5,4,4,2,5,5,7.625,8,4.666666667,4.0,4.6,5.0,5.0,4.6,4.666666667,3.75,4.25,3.333333333,3.666666667,3.5,4,2,5,5,5.0,4.5,4.5,4.5,4.5,21,1,8.0,0,2,1,1,1,1,1,1,0,1,1,1,0,1,0,1,1,2,1,1,1,1,0,1,8,5,4,5,4,4,5,5,5,5,4,5,2,4,4,4,8,0,2,1,1,0,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,8,5,4,5,5,4,4,4,5,4,3,5,2,4,4,4,0.636363636,4.6,2.4,5.4855,1.5145,5.827,5.805,5.238,5.331,5.225,1.5145,惠瑞泽,3.0,"翻译：人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等）
翻译专业讲究的主要是信、达、雅三点，如何使用大模型的新技术去更好的完成这三点呢？
经查阅资料：常见的翻译问题和错误
我们认为这些问题亟待解决（并且也可以通过大模型来很好的完成）：
语言结构不同
保持语气和风格的一致性
语言和主题专业知识
常见的翻译问题之一是基本结构，即使是在基本词序层面也是如此。 
COT通过让模型逐步展示其推理过程，使得我们可以追踪其决策逻辑，这与符号推理中的逐步演绎有相似之处。",8.647178945634112e-12,0.0,0.0,0.0,0.015013054830287208,0.38829566840463947,0.15338242053985596,0.3783783783783784,0.016528925619834656
142003,6,1406,0,7.0,10986.0,5.0,5.0,2.0,4.333333333333333,4.333333333333333,2.333333333333333,4.0,4.0,2.6666666666666665,5.0,5.666666667,4.666666667,4.0,3.592592593,3.555555556,3.333333333,3.0,4.1,4.7,3.4,0.5,4.2,5.0,3.666666667,3.6,3.666666667,2.666666667,3.5,4.333333333,4.75,3.4,3.75,2.6,3.6,17,15,13,18,6,C,0,0,0,0,1,0,7,5,3,4,4,5,4,4,3,4,4,2,4,4,4,4,5,5,5,4,4,3,3,4,4,3,3,4,3,4,4,3,4,5,4,4,3,4,5,4,4,2,4,3,3,4,3,4,4,4,4,3,3,4,3,2,3,4,4,3,4,3,2,5,5.75,5,4.0,3.4,3.6,3.4,4.0,4.0,4.5,3.5,3.25,3.666666667,3.333333333,3.25,4,3,2,5,5.5,5.0,4.5,5.0,5.0,20,0,5.0,0,1,1,2,1,1,1,2,0,2,1,1,0,1,1,1,0,2,1,1,0,1,1,1,4,3,2,3,4,3,4,5,3,4,2,4,4,3,3,2,7,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,2,0,2,1,1,7,5,2,4,5,5,5,5,4,5,3,4,3,4,4,4,0.636363636,5.0,2.0,5.8855,1.1145,6.327,6.305,5.238,5.831,5.725,1.1145,贾珺同,2.0,"任务一：【冷门绝学】
例如考古，可以在人工智能时代有更大的突破。目前学生参与到的考古在实地是这样的模式：懂考古知识的实习生作为组长，带领10位左右当地的大爷大妈进行实地挖掘与监测。组长利用所学知识感受土地中土壤湿度、砂石厚度等判断是用怎样的挖掘工具以及挖掘精细地点，实践由大爷大妈进行。如果利用到人工智能，前期学习考古具体的考察经验，举一反三根据当地的天气环境、历史遗址进行进一步判断，代替组长的角色；之后发挥机器人的优势即使进入到炎夏或寒冬也不妨碍工作效率进行挖掘。即将面临的挑战如下：1、面对考古艰难环境、机械动作的难度较大（难以前进），需要比较大的技术突破；2、文物的价值很高，如果收到机械的损坏，代价比较高。",8.154182598805983e-08,0.0784313725490196,0.04081632653061224,0.0784313725490196,0.02296953720858551,0.5195329463405312,0.11094336211681366,0.32786885245901637,0.02537634408602152
142004,6,1406,0,3.0,10987.0,2.0,2.0,4.666666666666667,3.6666666666666665,5.666666666666667,2.6666666666666665,5.333333333333333,4.0,3.333333333333333,5.333333333,5.0,3.666666667,5.0,3.762037037,3.572222222,3.433333333,2.6,3.4,3.2,3.8,0.25,4.0,4.666666667,3.0,4.4,4.0,2.333333333,4.5,3.333333333,4.0,3.2,3.25,4.2,3.6,16,13,21,18,6,C,0,0,0,0,1,0,7,3,5,3,4,4,4,5,4,5,5,5,5,2,3,5,2,5,3,5,4,5,4,2,5,5,5,3,4,5,5,4,5,5,5,3,4,4,5,5,5,2,5,4,3,4,3,5,5,5,2,5,1,4,4,2,5,4,4,4,5,3,4,6,4.5,3,4.166666667,4.8,4.0,4.4,4.8,4.2,3.333333333,4.25,3.5,5.0,4.666666667,2.25,5,3,4,6,5.5,3.5,5.5,5.5,6.0,19,1,6.0,1,1,1,2,1,1,0,2,1,2,1,1,0,1,0,1,0,2,0,2,1,2,0,1,4,3,1,3,4,4,4,3,4,5,5,5,4,4,3,3,8,1,1,1,1,0,2,0,2,0,1,0,2,0,2,0,2,0,2,0,1,7,4,1,4,5,5,4,4,3,5,4,4,2,4,4,4,0.363636364,5.2,1.8,6.0855,0.9145,6.327,4.805,6.238,6.331,6.725,0.9145,李家骥,1.0,我认为文物考古需要人工智能的帮助，可以发挥更大潜力。例如通过人工智能将年代久远的资料和近代的资料相结合，通过算法分析来确定文物的大概位置，减少人力资源的使用。同时分析历史材料，分析出最好的挖掘方案，并且不会破坏文物。尽最大的可能保护文物。同时可以研究出专门的考古挖掘机器人。机器人可以代替真人去执行一些危险的高难度的任务。例如勘测山洞内的文物，勘探一些危险的墓穴，或者是一些狭小的空间，人类无法到达的。同时根据发掘地的土壤成分，分析挖掘的时间，挖掘的技巧。当文物出土🪨时，人工智能自动分析文物的材质，自动评判该用哪种保护材料来减缓文物的腐败。,1.9413927300026505e-05,0.0,0.0,0.0,0.034379236877784235,0.6045988032620395,0.18258175253868103,0.34615384615384615,0.04690265486725664
142005,6,1406,0,4.0,10987.0,4.0,4.0,4.666666666666667,4.0,5.0,4.333333333333333,4.666666666666667,5.0,4.333333333333333,6.0,4.0,4.333333333,5.0,4.934259259,4.605555556,4.633333333,2.8,4.2,3.7,4.8,0.5,5.0,5.0,3.333333333,3.8,3.333333333,3.333333333,3.5,4.666666667,5.0,2.6,3.75,3.6,3.4,13,15,18,17,6,C,0,0,0,0,1,0,8,5,5,3,4,4,5,5,4,2,2,2,5,5,4,5,5,5,4,5,3,2,2,2,2,2,2,2,2,3,5,2,5,5,2,2,3,2,5,4,4,2,3,4,3,3,3,2,4,4,2,5,2,2,5,1,3,4,5,5,4,2,3,4,6.125,5,4.333333333,3.0,2.8,2.0,4.0,2.8,4.666666667,3.25,3.25,3.0,4.666666667,1.75,4,2,3,4,6.5,6.0,6.5,6.5,6.5,23,0,9.0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,9,4,2,4,4,3,3,4,4,4,4,3,3,5,4,4,9,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,2,0,2,1,1,8,4,2,4,5,5,5,5,5,5,5,5,2,5,5,5,0.863636364,6.4,1.6,7.2855,0.7145,7.327,7.305,7.238,7.331,7.225,0.7145,李沛杉,3.0,"分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
第六组讨论了考古学方面的AI的运用与挑战。
现阶段考古较多还是依靠人力去进行勘测挖掘，根据土壤情况判断此处是否会存在文物，依靠土壤情况判断是否存在文物这一环节可以依靠AI来进行，将观测到土壤的指标输入进模型，得到是否存在文物，并指导下一步是否应当在此处挖掘。除此之外，AI还能对文物进行数字化建模，展现其多模态学习的能力，使得文物能够更便捷的展示在大众面前，提升宣传力。对于考古机器人，AI也能在路径规划上指导机器人，寻找到更适合机器人前进的平坦路径。
但目前机器人仍不能在较为崎岖的路上行走，是将AI运用到路径规划上的挑战。除此之外，AI的运算需要消耗大量的电力，而考古所在的地方位置可能较为偏僻，因此供电也是AI面临的挑战。除此之外，AI+机器人是否能独立完成文物挖掘也是值得商讨的问题，他可能缺少人类的考古经验，从而对文物位置进行误判或甚至对文物造成损害。",8.975522937262767e-05,0.24324324324324326,0.2222222222222222,0.24324324324324326,0.048487642864662006,0.6578047958185255,0.20862092077732086,0.30689655172413793,0.046251993620414655
142006,7,1407,0,4.0,10988.0,1.0,1.0,1.3333333333333333,3.6666666666666665,3.6666666666666665,2.6666666666666665,5.0,2.6666666666666665,3.333333333333333,5.333333333,4.666666667,3.666666667,6.0,2.761111111,2.566666667,2.4,2.4,2.5,3.4,4.1,0.25,4.2,4.333333333,3.0,4.4,4.666666667,3.333333333,4.0,3.666666667,4.75,3.4,3.75,3.6,4.8,17,15,18,24,7,C,0,0,0,0,1,0,7,9,5,4,5,4,4,5,5,4,4,3,3,4,5,5,5,5,5,5,4,1,1,1,3,3,1,1,1,4,4,5,4,4,3,5,3,3,4,5,4,5,5,4,3,4,4,4,4,4,1,4,1,1,5,3,4,4,4,4,4,1,3,4,8.25,9,4.5,3.8,2.4,1.8,4.2,3.6,4.833333333,4.75,3.75,4.0,4.333333333,1.5,4,1,3,4,7.0,7.0,5.5,5.5,6.0,21,1,8.0,1,1,1,1,1,1,1,1,1,2,1,1,0,2,1,1,1,2,1,1,1,1,1,1,8,4,2,4,4,5,5,5,5,4,4,4,3,4,3,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,1,1,8,4,2,3,4,5,4,5,4,5,3,4,2,3,2,3,0.909090909,6.2,0.8,7.0855,-0.0855,7.827,8.305,6.238,6.331,6.725,0.0855,李欣锐,1.0,"冷门绝学
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
冷门学科：
语言学
利用AI技术助力该学科的传承、创新与社会应用：
可以利用多模态学习促进濒危语言的保护和复兴，整合语音、文字、图像与文化场景自动分析濒危语言的发音、语法以及语义特征。利用举一反三的能力进行对现有资料的分析，进行推断冷门与罕见字词的理解；利用反思能力持续更新算法与翻译能力，进行时间较长的自我更新以及自我迭代。
如今面临的问题为对于一些罕见语种或者方言，现有资料留存较少，并不能很好地进行分析与推断。同时，语言学的理论抽象性较强，而AI技术偏向于实用性，如何更好地实现学术与技术的融合是语言学学生以及AI本身在未来发展中的一大挑战。",,,,,,,,,
142007,7,1407,0,7.0,10988.0,2.0,2.0,3.333333333333333,4.0,4.333333333333333,4.666666666666667,3.6666666666666665,4.333333333333333,4.333333333333333,5.0,4.333333333,4.666666667,6.0,4.0,4.0,4.0,4.0,4.7,4.3,5.3,0.125,4.0,4.0,4.0,4.0,4.0,4.333333333,4.0,4.0,5.0,3.6,4.0,3.6,4.0,18,16,18,20,7,C,0,0,0,0,1,0,7,5,4,4,5,5,4,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,4,2,4,4,4,4,3,3,3,3,5.75,5,4.166666667,3.2,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,3.0,3,3,3,3,6.0,3.5,5.0,5.0,5.5,24,0,9.0,1,1,1,1,1,1,1,2,1,2,1,1,0,2,1,1,1,1,1,1,1,2,1,2,7,5,4,4,4,4,4,4,4,4,4,4,4,4,4,5,9,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,2,0,2,1,1,7,4,4,4,4,4,4,4,4,4,4,4,2,4,4,5,0.818181818,5.0,2.0,5.8855,1.1145,6.827,4.805,5.738,5.831,6.225,1.1145,刘晨,2.0,"问：“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
答：
请选择一门你认为具有更大发展潜力的冷门学科：小语种
分析其在人工智能时代可能焕发新生命力的可能原因：全球化发展与各国交流密切，人工智能时代可以弥补小语种精英人员人数的不足，并且线上使用可以操作的更为便捷，可以打破地理和时间的限制。
与其在未来发展中面临的可能挑战：可能遇到小语种当中生僻冷门的关键字，还有某些地区或者种族的方言时，人工智能无法保证能够做到十分准确，并且不同国家不同民族之间存在着不同的信仰和文化以及政治问题，可能会触及一些伦理或安全的红线。
如何利用AI技术助力该学科的传承、创新与社会应用：可以在短时间内广泛学习一个国家的历史文化起源，对于文化的传承具有很大的意义。还可以通过多种语言进行规律总结，找到不同语种间的规律。",0.000465420313008251,0.05405405405405406,0.02777777777777778,0.05405405405405406,0.06278359573115036,0.5089900705640527,0.18904754519462585,0.27586206896551724,0.05823418910457112
142008,7,1407,0,16.0,10989.0,4.0,6.0,2.333333333333333,4.333333333333333,2.333333333333333,5.666666666666667,6.0,2.333333333333333,3.6666666666666665,3.333333333,6.0,4.0,5.333333333,4.512037037,4.072222222,4.433333333,3.6,4.8,4.6,5.1,0.25,4.2,5.0,4.666666667,4.6,5.0,5.0,5.0,5.0,5.0,3.6,4.25,3.6,5.0,18,17,18,25,7,C,0,0,0,0,1,0,5,10,4,2,3,4,5,5,5,2,5,2,2,5,3,1,5,5,5,3,3,2,1,1,4,3,4,4,3,2,4,2,5,3,1,1,1,1,3,2,4,1,1,1,1,1,1,2,3,5,4,3,5,4,3,5,2,5,1,1,1,5,2,1,8.125,10,3.833333333,3.2,2.0,3.6,3.2,1.4,4.0,2.0,1.0,2.333333333,3.666666667,4.5,1,5,2,1,5.5,5.0,4.5,5.0,5.0,19,0,8.0,0,2,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,1,1,1,0,1,1,1,9,5,5,5,5,5,5,4,5,5,5,4,3,4,4,3,9,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,2,1,2,1,1,8,5,5,4,5,5,5,5,5,4,2,5,1,2,2,3,0.863636364,5.0,0.0,5.8855,-0.8855,6.327,6.305,5.238,5.831,5.725,0.8855,刘烨祁,3.0,"可以利用AI大语言模型，先输入高质量的甲骨文数据，再人工对齐，让AI记忆各个古文字对应的现代汉字翻译，训练AI对史料进行翻译，也可参考AI的判断结果破解新的古文字。
可能挑战：古文字的样本不够多，导致训练AI的数据不足。该学科受社会的关注度不够，参与AI训练的人员紧缺。古文字破解需要漫长的训练过程。对史料翻译的过程中可能有“幻觉”现象，产生不被社会伦理接纳的错误结果。同一个意义的文字可能有多种不同形式的变体，需要神经网络同时接受多个信息。古文字浩如烟海，计算成本高等。",6.10872963809103e-13,0.12903225806451613,0.10989010989010987,0.12903225806451613,0.013394565633371602,0.6335967219846445,0.13289345800876617,0.45925925925925926,0.017872585759584947
142009,8,1408,0,3.0,10989.0,1.0,1.0,2.6666666666666665,4.0,4.666666666666667,5.333333333333333,2.6666666666666665,3.0,3.0,5.0,4.333333333,5.666666667,4.666666667,4.363888889,4.183333333,4.1,4.6,4.7,4.1,4.5,0.25,5.0,5.0,4.666666667,5.0,5.0,4.666666667,4.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,8,C,0,0,0,0,1,0,6,6,3,4,4,3,4,4,4,5,5,4,5,5,5,4,4,5,4,4,4,4,3,3,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,4,5,5,5,5,4,3,2,4,4,4,4,5,3,4,3,5,5,4,3,3,3,6.0,6,3.666666667,4.6,3.6,4.0,4.4,5.0,4.5,4.75,5.0,3.666666667,3.666666667,3.75,4,3,3,3,6.0,4.0,5.0,6.0,5.0,20,1,7.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,8,5,4,5,5,5,5,5,5,5,5,5,2,3,3,3,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,5,4,5,5,5,5,5,5,5,5,5,3,3,3,3,0.772727273,5.2,0.8,6.0855,-0.0855,6.827,5.305,5.738,6.831,5.725,0.0855,罗润,1.0,"冷门学科：
古生物学
焕发新生命力的原因：
可以用AI进行场景再现、仿真模拟、进行古生物的形态预测、古生物蛋白重构甚至是克隆再现、构建生物宏观进化模型并不断根据新发现进行反馈修正。
可能面临什么挑战：
技术难题：可供参考的古生物数据集有限，研究的数据类型主要是岩石样本比较单一。
伦理难题：类似于克隆的问题。
助力：
宣传本学科，吸引学习兴趣，降低实验成本，加快实验进展。促进学科发展。有利于更快更准确地认识地球生命发展历程，帮助人类预测乃至于应对生物种类发展道路上存在的共性问题。",1.5086263897899017e-09,0.02985074626865672,0.0,0.02985074626865672,0.017462978485610507,0.46168430777536285,0.1390267312526703,0.362962962962963,0.02024462252214254
142010,8,1408,0,3.0,10990.0,0.0,0.0,3.333333333333333,3.0,4.0,2.333333333333333,4.0,3.333333333333333,3.6666666666666665,4.0,4.0,4.0,4.0,2.841666667,3.05,3.3,3.8,3.8,3.4,3.8,0.375,3.4,3.666666667,3.0,4.2,3.666666667,3.333333333,3.5,3.333333333,4.75,3.8,4.0,3.8,4.2,19,16,19,21,8,C,0,0,0,0,1,0,7,7,4,4,4,4,4,4,3,5,5,5,3,5,4,4,5,4,4,4,4,4,4,4,5,5,5,5,5,4,4,5,4,5,4,5,4,4,5,3,4,3,4,4,4,4,4,3,4,4,2,4,2,3,4,2,2,3,4,4,4,4,3,5,7.0,7,4.0,4.2,4.0,5.0,4.4,4.4,4.333333333,3.5,4.0,3.0,4.0,2.25,4,4,3,5,6.0,4.0,5.0,6.0,5.5,22,1,7.0,0,2,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,7,4,3,3,4,4,3,4,4,4,5,4,2,3,4,4,7,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,2,0,2,0,2,6,3,3,3,3,4,4,3,3,4,4,3,2,3,3,4,0.681818182,5.3,1.7,6.1855,0.8145,6.827,5.305,5.738,6.831,6.225,0.8145,吕祚琛,3.0,"我们组讨论目前我们看来的冷门专业：古生物学
针对其在人工智能时代
发展潜力：
1.我们首先想到的是对其宣传，可借助AI工具模拟其在线状态，作为视频图片，用于宣传我们。
2.了解到，古生物学包括的一个主要任务是生物宏观进化模型，在这方面我们可以利用AI技术做一个模拟预测，通过生物结构进化使用AI技术，预测在生物发展这么长的阶段，我们还没明确发现的生物其结构。
3.也可以关注到某些具体生物族群在进化或者某些过程中所遇到的问题，同时进行古生物结构预测，对目前尚未明确的某些生物帮助其分类与学习。
面临挑战：
1.技术问题：首先如果利用AI的话，就我们目前来看，首先实验数据是很重要的，但在这方面可能由于研究人员以及研究方法所需数据较难获得，所以需要在数据方面多下功夫。
2.伦理问题是值得考虑的问题。",0.00027808716207906577,0.19047619047619047,0.05,0.19047619047619047,0.04623515185252997,0.555568025530019,0.18127068877220154,0.3047619047619048,0.0504
142011,8,1408,0,4.0,10990.0,1.0,1.0,2.6666666666666665,2.0,6.0,5.0,5.0,2.6666666666666665,3.6666666666666665,6.0,6.0,5.666666667,6.0,4.618518519,4.711111111,4.266666667,4.6,4.3,4.5,4.2,0.25,4.2,5.0,4.0,3.6,4.666666667,3.0,4.0,4.0,4.0,4.2,4.25,4.4,4.6,21,17,22,23,8,C,0,0,0,0,1,0,6,7,3,4,5,5,5,5,5,4,5,5,4,5,5,5,4,4,4,4,4,5,4,4,4,5,5,5,5,4,4,3,3,4,5,5,4,5,5,4,5,5,4,4,5,4,5,4,5,5,1,5,5,2,5,1,5,5,5,5,5,5,4,6,6.625,7,4.5,4.6,4.2,4.8,3.6,4.8,4.5,4.5,4.5,4.666666667,5.0,2.25,5,5,4,6,7.0,7.0,6.0,6.5,6.5,20,1,6.0,1,2,1,2,0,2,1,1,1,1,1,1,0,1,0,2,1,1,0,2,1,1,1,1,4,4,3,2,5,5,4,4,4,3,3,4,4,4,3,4,8,1,1,1,1,0,1,1,1,1,2,1,1,0,1,1,2,1,1,0,2,6,5,3,4,5,5,5,4,4,4,4,5,4,2,4,2,0.681818182,6.6,-0.6,7.4855,-1.4855,7.827,8.305,6.738,7.331,7.225,1.4855,彭侃,2.0,"选择学科：古生物学
其在人工智能时代可能焕发新生命力的可能原因;
1.古生物学目前大众知名度较低，许多人对其了解并不足够且真实，可能产生对该学科错误的判断；AI的发展给文化的传播形式提供了更多的可能，如虚拟现实、仿真模拟等手段，让古生物进入大家的视野，使古生物学焕发生机。
其在未来发展中面临的可能挑战：
1.真实实验材料较少，AI如何真实的模拟实验材料
2.古生物学涉及知识面广，AI如何能动将其结合
3.实际应用问题：实际应用方面较少是古生物冷门的重要原因之一，AI能否发现该学科更多的应用范围
4.伦理问题
如何利用AI技术助力该学科的传承、创新与社会应用：
虚拟现实，带大众走进古代场景，更直观的认识古生物
古生物学 AI 研究，涵盖了微观和宏观化石分类、图像分割和预测等主要任务。 这些研究采用了广泛的技术，例如基于知识的系统（KBS）、神经网络、迁移学习和许多其他机器学习方法，可实现各种古生物学研究工作流程的自动化。 在这里，我们讨论他们的方法、数据集和性能，并将它们与更传统的人工智能研究进行比较。",0.004241484984405269,0.4615384615384615,0.08333333333333334,0.4615384615384615,0.09715043455364224,0.5498148208995933,0.16707861423492432,0.19636363636363635,0.07978723404255317
142012,9,1409,0,2.0,10991.0,1.0,1.0,2.6666666666666665,3.333333333333333,6.0,5.0,1.0,4.0,4.0,4.666666667,3.666666667,5.333333333,6.0,4.000925926,4.005555556,4.033333333,3.2,4.0,4.6,4.3,0.5,4.8,5.0,4.666666667,4.4,5.0,4.666666667,5.0,4.666666667,5.0,5.0,4.5,4.6,4.8,25,18,23,24,9,C,0,0,0,0,0,0,8,8,5,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,3,4,4,5,5,4,4,5,5,4,5,5,5,5,5,4,4,4,4,4,4,4,4,4,4,5,5,5,1,5,2,2,5,2,5,4,5,5,4,4,4,5,8.0,8,4.666666667,4.0,3.8,4.4,4.8,4.6,3.833333333,4.0,4.0,5.0,5.0,1.75,4,4,4,5,3.5,3.0,4.0,4.5,4.5,26,1,8.0,0,1,1,1,1,1,1,1,0,2,1,1,0,1,1,1,1,1,1,2,0,1,1,1,8,5,4,5,5,5,5,5,4,4,4,5,3,4,4,4,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,9,5,4,5,5,5,5,5,5,5,5,4,3,4,4,4,0.681818182,3.9,4.1,4.7855,3.2145,4.327,4.305,4.738,5.331,5.225,3.2145,齐保辉,3.0,我认为在通用人工智能时代，随着技术的发展和知识体系的变革，考古学和宗教学这些较为冷门的学科有望焕发新的生命力。首先，在当今社会，学生为了自己的职业发展，选择理工类学科作为自己的大学专业是极为普遍的现象，导致考古学和宗教学这些冷门学科逐渐淡出人们的视野，但是这些学科对于我们理解当今社会是十分必要的。在通用人工智能时代，我们可以利用人工智能的优势，让人工智能通过大数据不断地去学习这些学科的专业知识，不断助力学科发展，通过AI手段让这些学科在社会中不断地传承，应用。当然，我们也要时刻提防AI生成虚假信息的可能性。,,,,,,,,,
142013,9,1409,0,5.0,10991.0,2.0,2.0,2.333333333333333,2.333333333333333,5.0,5.666666666666667,5.0,2.6666666666666665,3.0,4.666666667,4.333333333,4.0,5.333333333,4.449074074,4.694444444,4.166666667,4.0,4.4,4.1,5.0,0.125,4.0,4.0,3.666666667,3.6,4.666666667,3.666666667,4.0,4.333333333,5.0,3.8,4.0,3.4,5.0,19,16,17,25,9,C,0,0,0,0,0,0,6,7,5,4,4,5,5,5,4,4,4,4,4,4,4,5,5,3,4,3,3,3,3,4,3,3,3,3,3,4,4,4,4,5,4,3,3,3,3,3,3,3,3,2,3,3,3,3,3,3,4,3,4,3,3,4,3,3,3,3,3,2,4,2,6.625,7,4.666666667,4.0,3.2,3.0,4.2,3.2,4.166666667,3.0,2.75,3.0,3.0,3.75,3,2,4,2,3.0,2.0,4.0,4.5,4.0,22,0,9.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,8,4,4,3,4,5,5,4,4,3,3,4,2,3,3,3,9,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,9,4,4,3,4,4,4,4,4,4,3,5,1,3,3,2,0.772727273,3.5,2.5,4.3855,1.6145,3.827,3.305,4.738,5.331,4.725,1.6145,齐巧霞,2.0,"请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
我认为在人工智能时代，古生物学将成为有更大发展潜力的冷门学科，古生物学之所以冷门，是因为从事这个领域学习研究的人较少，这个领域对是在探索人类未知的过去，而非未来，其经济价值和现实价值不大，人们学习这个学科的学习成本与从事这个领域的未来收益不成正比，因此这个学科不被人们所待见，这个学科的传承和发展存在很大的问题。随着ai技术的发展，由于ai学习知识和技术是没有成本的，ai可以做到助力该学科的传承、将该学科的相关知识以科普的形式展现给大众，让人类了解地球的过去。但我们不认为ai可以助力该学科的创新，毕竟这个学科的发展要基于大量的考古调查等需要人类细致去完成的工作。",,,,,,,,,
142014,9,1409,0,3.0,10991.0,5.0,5.0,3.6666666666666665,4.666666666666667,3.6666666666666665,3.333333333333333,3.6666666666666665,3.333333333333333,3.6666666666666665,5.0,5.0,5.0,4.0,3.997222222,3.983333333,3.9,3.4,4.5,4.0,4.5,0.375,4.0,4.0,3.666666667,4.2,4.0,4.0,3.0,3.333333333,3.25,3.4,3.5,3.2,3.8,17,14,16,19,9,C,0,0,0,0,0,0,6,6,3,4,4,3,4,4,3,3,4,4,3,3,4,4,4,4,4,4,4,3,3,3,4,4,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,3,3,3,3,3,3,4,3,4,4,3,4,4,3,3,4,2,3,2,6.0,6,3.666666667,3.4,3.4,3.4,4.0,4.0,3.833333333,3.75,3.25,3.333333333,3.666666667,3.25,4,2,3,2,5.0,3.0,5.0,4.5,4.5,20,0,5.0,0,1,1,1,1,1,1,2,1,1,0,2,0,1,1,1,0,2,0,2,0,1,0,2,6,4,3,5,4,4,4,4,4,4,5,4,4,3,4,4,7,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,2,1,1,7,4,3,4,4,4,4,4,4,4,4,4,2,3,3,4,0.545454545,4.4,1.6,5.2855,0.7145,5.827,4.305,5.738,5.331,5.225,0.7145,石翘楚,1.0,"我认为园林设计专业可以在人工智能时代焕发出色彩。首先，目前的园林设计专业只有少数的几个学校有，且目前社会中对此类人才的需求也较少，所以比较冷门。
人工智能兴起后，随着人们对社会现代化的要求越来越高，审美升级，对社会建筑风格和外观设计的要求也越来越高，且当下老破小地区偏多，未来注定会迎来比较大的革新建筑期。人工智能以其强大的整合能力和收集了世界各地建筑风格、美景的综合素质，可以给园林设计专业人才带来更多的灵感，帮助其结合本国要求，创造出适合中国现代化的新中式风格建筑，帮助城市焕然一新。",,,,,,,,,
142015,10,1410,0,10.0,10992.0,4.0,5.0,2.6666666666666665,5.0,3.6666666666666665,3.6666666666666665,2.6666666666666665,3.6666666666666665,3.333333333333333,6.0,5.0,4.666666667,5.0,3.322222222,3.933333333,3.6,2.6,5.1,4.3,4.7,0.375,4.4,4.666666667,4.333333333,3.8,4.333333333,4.0,4.5,4.666666667,5.0,3.2,4.5,4.4,4.6,16,18,22,23,10,C,0,0,0,0,0,0,8,7,5,5,5,5,5,3,5,4,5,4,4,5,4,5,5,5,4,5,4,2,2,2,3,3,3,3,2,4,5,4,5,5,2,2,2,2,4,4,4,2,3,3,4,4,3,3,4,4,2,4,2,2,4,2,2,4,4,5,5,2,3,6,7.375,7,4.666666667,4.4,3.0,2.8,4.6,2.4,4.666666667,3.25,3.5,3.0,4.0,2.0,5,2,3,6,7.0,4.5,6.5,7.0,7.5,19,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,1,0,2,0,1,1,1,8,5,3,4,4,4,5,3,4,4,3,5,3,4,2,4,9,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,8,5,3,5,5,5,4,5,5,4,3,5,1,4,3,4,0.636363636,6.5,1.5,7.3855,0.6145,7.827,5.805,7.238,7.831,8.225,0.6145,孙嘉阳,1.0,"任务二：“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
1.选择的冷们学科：园林园艺
2.焕发新生命力的可能原因：AI可以在短时间内输出更多的可能性，效率高；人需要有大量的知识储备，学习过程长，而AI可以通过大数据直接学习，耗费的时间成本少；AI可以监控地区气候和天气变化，去匹配最适合的植物。
3.面临的挑战：植物是否真的可以实地应用，面临成本以及是否灭绝的问题；设计园林的方案对于园林工人难处理，比如设计很多林木占比，而不是灌木对于园林工人更加难以长期培育；AI创造出来的图纸还是太AI化，能被一眼看出不是真人设计的；AI不了解中国的风水问题，不具备玄学类的知识，特殊情况只能靠中国风水学专业的人来布景。
4.如何利用AI技术助力该学科传承、创新与社会应用：简易学习的难度，因为园林需要大量的知识网络，学习周期长，成果慢，AI可以加速该学科的学习成果，让学生更注重于结果的输出；创新方面，AI可以设计出更多元化的园艺造景图，效率比人类高，在短时间内更容易有创新发展；社会应用方面，AI技术可以缩短园林设计时间，并且能给社会带来较高收益（例如，布景植物的光合作用值更高，可以减少城市的热岛效应，改善城市空气质量和生态环境）。",0.002246764527613229,0.5084745762711864,0.49122807017543857,0.44067796610169496,0.06738207695271065,0.6558459915408041,0.17379458248615265,0.25054466230936817,0.06154714850367027
142016,10,1410,0,6.0,10992.0,3.0,3.0,3.0,1.0,2.0,1.6666666666666667,4.666666666666667,2.0,2.333333333333333,4.0,5.333333333,2.0,2.333333333,3.425,2.55,3.3,2.8,2.9,2.3,3.4,0.375,2.6,4.0,3.666666667,2.8,4.0,4.0,5.0,4.666666667,5.0,2.4,3.75,4.2,3.8,12,15,21,19,10,C,0,0,0,0,0,0,5,5,3,3,3,3,3,2,3,3,3,3,3,2,4,3,4,4,4,3,3,3,3,3,3,3,3,4,4,4,4,3,3,4,4,4,3,3,3,2,2,3,3,3,3,3,3,3,2,4,3,4,4,4,4,4,4,3,4,4,4,3,2,3,5.0,5,2.833333333,3.0,3.0,3.4,3.6,3.4,3.5,2.5,3.0,3.0,4.0,3.75,4,3,2,3,6.0,5.5,5.5,6.0,5.0,23,0,6.0,1,1,1,1,1,1,1,1,1,2,1,1,1,2,1,2,1,1,1,2,1,2,0,2,6,4,4,4,4,4,4,4,3,3,2,2,3,2,2,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,6,4,3,4,4,4,4,3,3,3,2,2,3,2,2,2,0.909090909,5.6,-0.6,6.4855,-1.4855,6.827,6.805,6.238,6.831,5.725,1.4855,孙雯,2.0,"冷门专业：园林园艺
AI的作用：具备有大量的知识储备，可以根据模板设计花圃，举一反三，迅速推荐大量可应用到实际的花卉植物，并且ai还可以结合当地天气土壤等多场景进行综合分析，并且可以在短暂的时间内获得大量的设计原型
挑战：专业化不够，适用大规模布景，对于细节的把控，实际应用上虚报率较高，更加细节的把控仍然需要真人专家进行，ai的创新性仍然不够，更多是基于节点进行模仿，原创能力较弱。园林园艺较大可能涉及到周易风水等知识，这些知识更多地需要基于实践进行创作应用",4.946304906946091e-06,0.20689655172413793,0.14814814814814814,0.20689655172413793,0.03078733680562686,0.3799690735511517,0.11262726038694382,0.3014705882352941,0.03319148936170213
142017,10,1410,0,5.0,10016.0,4.0,5.0,3.333333333333333,5.0,4.666666666666667,4.0,2.6666666666666665,5.0,4.666666666666667,5.0,4.666666667,5.666666667,5.333333333,4.058333333,4.35,4.1,3.6,4.4,3.7,4.2,0.625,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,4.8,5.0,25,20,24,25,10,C,0,0,0,0,0,0,7,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,3,2,4,6.375,6,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,3,3,2,4,6.5,4.5,6.0,6.0,6.5,19,1,9.0,0,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,0,2,1,1,1,1,1,2,7,5,5,5,5,5,5,5,5,5,5,5,2,4,5,5,9,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,1,1,1,9,5,5,5,5,5,5,5,5,5,5,5,2,5,5,5,0.727272727,5.9,1.1,6.7855,0.2145,7.327,5.805,6.738,6.831,7.225,0.2145,孙小宝,3.0,"园林园艺
园林园艺这一学科目前在我国的应用程度不足，并且相关专业的人才需要经过多年的浸润、大量的实地观察学习、广泛的了解才能有一定的理论基础。但是园林园艺人才在实践时往往要结合具体问题具体分析。这就使得普适性的理论知识和特定化的具体需求之间形成明显的矛盾。由于AI背后有巨大的知识储备，并且能够迅速、高效地分析比对当地的环境、甲方的需求以及自身具备的相关知识，对于典型案例进行综合迁移，使得其时间成本大大缩短，有助于解决人才培养周期长，成效慢导致该行业难以快速发展的尴尬现状。
同时，AI在领域的挑战也比较明显。由于AI只是基于数据库对于现状给出解决方案，所以，相对的，关于方案中植物的可获取度、珍稀度的考虑可能并不充分。对于园林景观后期的修缮难度评估亦面临难题。
基于第一段而言，AI对于该领域知识的把握、理解能够加速应用人才培养速度，促进传承。又因为其可以给学生提供大量参考案例、资料，也能促进创新。对于应用传承方面，AI可以帮助更好地结合植物特性、场景特点，基于不同的目的给出最佳方案",,,,,,,,,
142018,11,1411,0,5.0,10017.0,3.0,3.0,3.0,3.6666666666666665,5.0,4.666666666666667,4.333333333333333,4.0,4.0,5.0,4.666666667,4.0,4.0,3.80462963,3.827777778,3.966666667,3.8,4.4,3.7,4.0,0.625,4.0,4.333333333,3.666666667,3.8,4.666666667,3.333333333,4.0,4.666666667,5.0,3.2,4.25,4.0,3.6,16,17,20,18,11,C,0,0,0,0,0,0,7,7,4,4,4,4,4,3,4,3,4,4,4,4,4,5,5,4,4,4,4,3,4,4,3,4,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,4,4,4,4,4,4,3,4,4,4,4,3,4,4,4,4,3,1,3,5,7.0,7,3.833333333,3.8,3.8,3.6,4.0,4.0,4.333333333,4.0,4.25,4.0,4.0,3.5,3,1,3,5,5.0,4.5,5.0,5.5,4.5,27,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,8,4,3,3,5,5,4,4,4,3,4,4,2,4,4,4,8,1,1,1,1,0,1,1,1,1,1,1,1,0,1,1,2,1,1,1,1,7,4,3,4,4,5,4,4,4,4,4,4,2,4,4,4,0.772727273,4.9,2.1,5.7855,1.2145,5.827,5.805,5.738,6.331,5.225,1.2145,唐功政,2.0,"使用AI助力甲骨文识别研究
通过使用甲骨文数据进行AI的自监督预训练，然后将带有标签的甲骨文数据放入模型进行有监督微调，从而提高模型对甲骨文的识别能力，最后参考专家意见进行人类反馈增强，进一步提高模型的能力，从而助力甲骨文识别的研究，发现和研究新的甲骨文字符，推动古文字研究的创新。并且可以开放虚拟博物馆，提高社会应用，",6.634547978977385e-39,0.021390374331550804,0.010810810810810811,0.021390374331550804,0.007848526893451122,0.30594199503508795,0.10993766039609909,0.9540229885057471,0.010747118995209082
142019,11,1411,0,4.0,10017.0,5.0,6.0,2.333333333333333,3.333333333333333,6.0,5.333333333333333,4.666666666666667,4.0,3.6666666666666665,5.0,6.0,5.333333333,5.666666667,4.091666667,3.55,4.3,3.8,4.1,4.2,5.2,0.25,4.4,4.333333333,3.666666667,4.0,4.333333333,2.666666667,4.5,4.333333333,4.5,3.4,3.25,3.0,4.6,17,13,15,23,11,C,0,0,0,0,0,0,4,8,4,5,5,5,4,5,4,3,5,4,3,2,5,5,4,3,5,3,3,2,2,2,4,4,3,2,3,4,4,4,2,3,4,3,4,3,4,2,4,3,4,3,5,4,3,5,3,5,4,4,3,4,4,3,4,3,4,3,4,3,4,2,6.5,8,4.666666667,3.8,2.4,3.2,3.4,3.6,4.0,3.25,3.75,4.0,4.333333333,3.5,4,3,4,2,3.5,4.0,4.0,4.0,4.0,22,1,7.0,0,1,1,2,1,1,1,2,1,2,1,1,1,2,0,2,0,2,1,1,1,2,0,2,6,3,2,3,4,4,5,3,4,5,5,3,3,4,3,4,8,1,1,1,2,0,1,0,1,1,1,1,1,1,1,0,2,0,2,0,2,7,5,2,4,4,4,5,5,4,5,5,3,2,4,4,4,0.590909091,3.9,0.1,4.7855,-0.7855,4.327,5.305,4.738,4.831,4.725,0.7855,王思其,1.0,"我认为具有更大发展潜力的冷门学科是心理陪伴，
像是虚拟“女友”在人工智能时代焕发出很新的生命力，但也面临着巨大的挑战，如对人的心理分析，以及对心理情绪的引导，对话时的语气等；
增强大语言模型算力，提高对语言分析的准确性，以及语言表达能力，动画表达能力，同时增强道德伦理的对齐，提高使用者对人工智能的满意度。",7.300328361327324e-09,0.0,0.0,0.0,0.01909111327393876,0.5603626938650855,0.1643448770046234,0.3956043956043956,0.024423337856173677
142020,11,1411,0,4.0,10018.0,3.0,4.0,4.333333333333333,5.0,5.0,5.333333333333333,5.666666666666667,2.333333333333333,2.333333333333333,6.0,5.666666667,5.333333333,6.0,4.59537037,4.572222222,4.433333333,3.6,3.9,4.2,4.6,0.125,4.8,4.666666667,3.333333333,4.8,5.0,4.666666667,3.5,4.333333333,4.25,3.2,3.0,4.4,4.6,16,12,22,23,11,C,0,0,0,0,0,0,6,9,5,5,5,5,5,5,4,4,5,5,5,5,5,5,5,5,5,4,5,5,5,4,5,5,5,5,5,4,4,4,4,4,4,5,5,5,5,4,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,2,5,7.875,9,5.0,4.6,4.6,5.0,4.0,4.8,5.0,4.5,5.0,5.0,5.0,5.0,4,4,2,5,3.0,3.0,3.5,3.0,3.5,21,0,6.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,0,1,8,4,5,5,5,5,5,5,5,5,5,4,2,2,2,3,8,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,8,4,3,3,4,5,5,5,5,5,5,4,3,2,2,3,0.863636364,3.2,2.8,4.0855,1.9145,3.827,4.305,4.238,3.831,4.225,1.9145,王文雅,3.0,"AI初步诊断精神类疾病
比如，官方机构给出权威自评量表，设置好相应参数，部分复杂的评估结果可以由AI直接得出，帮助初步确定或排除疾病的存在，节省掉大量时间和去医院做检查的精神成本
进一步，可以直接从患者自述发育史的文字中提取出可用于评估的关键信息，并给出具体分析结果、如得分、图表等",,,,,,,,,
141059,12,1412,0,2.0,10045.0,4.0,5.0,4.0,5.333333333333333,6.0,5.333333333333333,2.0,4.333333333333333,4.666666666666667,4.333333333,4.666666667,5.0,5.666666667,4.440740741,3.644444444,3.866666667,4.2,4.8,5.5,5.0,0.625,4.6,4.666666667,4.333333333,4.6,4.666666667,3.666666667,4.5,4.666666667,4.5,4.6,4.75,4.2,4.2,23,19,21,21,12,C,0,0,0,0,0,0,8,8,4,3,4,4,4,3,4,4,4,4,4,4,5,5,3,3,4,4,4,3,4,4,4,5,5,5,5,4,4,4,4,4,5,4,4,3,4,4,4,4,4,4,4,5,5,5,5,5,2,5,4,4,4,3,4,3,4,4,4,3,3,4,8.0,8,3.666666667,4.0,3.8,4.8,4.0,4.0,4.0,4.0,4.5,4.666666667,4.666666667,3.25,4,3,3,4,3.0,5.0,5.0,5.0,5.5,24,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,1,0,2,0,2,1,1,7,4,3,4,5,4,5,5,5,5,4,4,2,4,5,5,7,1,1,1,1,0,1,0,1,1,1,1,2,0,2,0,2,0,2,1,2,6,5,4,4,5,5,4,4,5,5,5,4,3,4,5,4,0.590909091,4.7,3.3,5.5855,2.4145,3.827,6.305,5.738,5.831,6.225,2.4145,赵江媛,1.0,"冷门绝学任务的讨论结果
小组成员重点讨论了植物学和考古学这两方面的冷门小众方向，对于植物学方向，可以利用AI搜集有关植物的知识，利用AI的思维链和反思能力；对于考古方向，可以使用大量的古物来训练AI，利用AI的举一反三和多模态学习的能力，使得AI可以实现多任务的学习。其实总结来说，冷门方向并不是知识冷门，只是应用场景少、任务需求低，可以完全利用大模型的多模态学习能力，将生活中常见的问题与冷门问题联系起来，这样AI就可以在冷门方向里发挥巨大的作用，推进冷门方向的发展。",,,,,,,,,
142021,12,1412,0,4.0,10018.0,2.0,2.0,1.3333333333333333,1.3333333333333333,4.333333333333333,4.333333333333333,6.0,2.333333333333333,2.6666666666666665,5.0,3.333333333,3.666666667,4.666666667,4.273148148,4.638888889,3.833333333,3.0,3.7,3.0,5.1,0.125,3.6,4.0,2.666666667,4.2,3.0,3.0,4.5,3.333333333,4.25,3.8,4.0,2.8,4.2,19,16,14,21,12,C,0,0,0,0,0,0,7,8,3,4,5,5,5,5,5,5,5,5,5,2,5,4,4,5,2,4,3,3,2,2,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,4,3,4,5,5,4,4,4,3,2,1,4,1,1,4,1,2,3,3,3,4,2,3,4,7.625,8,4.5,5.0,2.8,5.0,5.0,5.0,3.666666667,3.5,4.5,3.0,3.333333333,1.0,4,2,3,4,2.5,3.5,3.5,4.5,4.0,20,0,7.0,0,1,1,1,1,1,1,2,1,1,1,1,0,2,0,1,1,2,1,1,0,1,1,1,7,4,1,4,3,4,2,5,5,2,5,4,2,2,3,3,7,1,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,1,2,1,1,8,4,1,3,4,5,3,4,5,2,4,3,3,2,2,3,0.727272727,3.6,3.4,4.4855,2.5145,3.327,4.805,4.238,5.331,4.725,2.5145,吴子仪,3.0,"1.考古学：利用AI将获取到的考古材料上的古文字进行破译。由于同一朝代和时期的文字书写形式大致相同，可以将大量已知的古文字和破译后的相应文字输入，作为信息源，让AI从中总结出规律，进而能够对未破译的古文字材料进行翻译和解读。提高了人类阅读和理解古文字的效率。
2.植物学与中草药：利用AI阅读载有关于中草药的药效、使用方法及相关内容的古籍，整理归纳，提升人类对药用植物的使用的认知。
3.自动化：AI查看交通实况，检测交通系统中存在的漏洞。",7.261974344497591e-10,0.5217391304347825,0.28571428571428575,0.43478260869565216,0.01692047377326565,0.5373204913459011,0.13096623122692108,0.35658914728682173,0.020140105078809145
142022,12,1412,0,3.0,10019.0,4.0,5.0,4.0,3.333333333333333,3.6666666666666665,4.333333333333333,3.333333333333333,3.0,3.0,3.666666667,4.0,4.0,4.0,3.922222222,3.533333333,3.2,3.2,3.9,4.6,5.5,0.0,3.4,3.666666667,3.333333333,3.2,2.666666667,3.0,4.0,3.666666667,3.5,3.4,3.5,4.4,3.6,17,14,22,18,12,C,0,0,0,0,0,0,7,9,4,3,3,4,4,4,3,4,4,3,4,4,4,5,4,5,3,4,4,5,5,5,5,5,5,5,4,5,4,5,4,5,5,5,5,5,5,5,4,5,4,5,5,5,5,5,4,4,4,5,5,5,4,5,4,4,4,4,3,4,3,4,8.25,9,3.666666667,3.6,4.6,4.8,4.6,5.0,4.166666667,4.5,5.0,4.333333333,4.333333333,4.75,3,4,3,4,2.5,3.0,3.5,6.0,4.5,24,0,7.0,1,1,0,2,0,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,8,3,3,3,1,4,3,4,4,4,2,2,2,3,3,3,7,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,7,4,3,3,4,4,3,4,4,4,3,2,2,3,3,3,0.772727273,3.9,3.1,4.7855,2.2145,3.327,4.305,4.238,6.831,5.225,2.2145,杨畅,2.0,"小众学科：考古学
信息来源，前两年的北京大学入学仅一人的考古学专业在新闻上有很久，对于考古资料，中国有很多相关的照片/资料，虽然说目前考古学冷门的一定原因在于很多人对其的薪资待遇以及劳累程度有着一定的失望，但是对于其中对于信息检索的部分，可以进行一定的机器学习进行特征学习。便于其中的年代、信息的辨别。
小众学科：人文植物学
信息来源：同组同学老师的研究方向，类似于人文和植物学、中药学的一个交叉方向，可以采取相关的知识库进行语料融合，对其中的一些未发现的交叉点进行预测，类似于生物学未知结构预测类似的情况。",,,,,,,,,
141052,13,1413,0,7.0,10041.0,3.0,3.0,3.6666666666666665,4.333333333333333,5.333333333333333,4.0,3.333333333333333,3.333333333333333,3.333333333333333,4.333333333,5.666666667,4.666666667,4.666666667,3.760185185,3.561111111,3.366666667,3.2,5.0,4.2,4.7,0.25,4.4,3.666666667,3.333333333,3.8,4.0,3.333333333,4.0,4.0,4.75,2.4,3.25,2.8,3.8,12,13,14,19,13,C,0,0,0,1,0,0,8,3,4,3,4,4,5,3,2,3,4,4,5,4,5,4,4,2,3,4,4,2,3,3,5,5,4,3,4,4,5,4,4,3,3,4,5,4,4,4,4,2,3,4,3,4,4,3,4,4,1,4,3,2,5,3,3,4,3,4,2,3,2,5,4.875,3,3.833333333,3.6,3.2,4.2,4.0,4.0,3.666666667,3.25,3.75,3.333333333,4.333333333,2.25,2,3,2,5,7.0,7.0,7.0,7.0,8.0,20,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,8,4,3,3,5,4,3,4,4,4,3,4,2,3,3,4,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,1,1,1,8,3,3,4,4,4,3,5,5,5,4,3,2,4,2,4,0.818181818,7.2,0.8,7.2,0.8,7.0,7.0,7.0,7.0,8.0,0.8,杨乐,2.0,"AI恐怖分子可能在国家安全，舆论控制，技术创新等方面对国家和社会带来重大影响。
在国家安全方面，它可能利用AI入侵人类的一些安全系统，从而导致系统失效。比如AI恐怖分子可以控制一些军工领域装备的核心部件，导致武器失效从而毁灭人类自身。AI恐怖分子也可以发布大量虚假信息，引导普通民众乃至国家领导人层面产生战略误判，从而引发人类爆发战争导致毁灭人类。针对这些问题，我们可以发展“超级对齐”技术，对AI进行相应的规则设定，防止AI进入重大安全领域。
在技术创新方面，AI可以通过制造虚假数据来影响人类进行技术创新，或者在人类做实验的过程中引导实验走向不可控的方向。针对这些问题，可以严格控制AI获得的数据质量，防止AI出现“幻觉”等问题，引导AI辅助人类提供正确信息。
AI之间进行相互交流也会导致AI制定策略去攻击人类。我们可能对部分AI之间进行隔离，防止AI合作去促成共识来攻击人类。
展望AI防御技术的未来发展，全世界人民会一起努力制定新的规则，让AI的对齐技术在AI能力之上，以此来抵御风险。",0.0002822070166942575,0.576271186440678,0.5614035087719299,0.576271186440678,0.05796808153628327,0.6548130377902123,0.23278068006038666,0.372,0.06245564229950318
141053,13,1413,0,7.0,10042.0,5.0,5.0,4.0,3.0,5.333333333333333,4.666666666666667,1.3333333333333333,2.6666666666666665,2.6666666666666665,5.333333333,4.666666667,4.666666667,5.0,3.357407407,4.144444444,2.866666667,4.2,4.0,5.0,5.0,0.5,3.0,3.666666667,4.0,3.6,4.0,4.333333333,4.5,4.333333333,4.25,3.8,3.5,4.2,4.2,19,14,21,21,13,C,0,0,0,1,0,0,4,8,2,4,4,4,4,4,2,4,2,4,4,4,4,4,3,4,4,4,3,3,4,4,3,3,3,3,3,4,3,3,4,4,4,3,4,4,4,3,3,3,3,4,3,4,3,4,4,3,3,2,2,3,3,4,5,4,2,4,3,3,2,3,6.5,8,3.666666667,3.2,3.6,3.0,3.6,3.8,3.833333333,3.0,3.5,4.333333333,2.666666667,3.0,3,3,2,3,6.5,6.0,6.0,5.5,6.5,22,1,8.0,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,1,1,1,1,1,1,1,2,8,4,5,4,5,4,3,4,2,4,5,3,2,2,2,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,4,4,4,4,4,3,3,3,3,2,4,2,2,2,4,0.909090909,6.1,-2.1,6.1,-2.1,6.5,6.0,6.0,5.5,6.5,2.1,姚坤宜,1.0,"AI利用自己的认知（如偏见、歧视、“幻觉”等难以避免的模型问题），影响人类自己的认知，发布虚假信息，从而引发不必要的社会恐慌和负面舆论等。应对这种情况我们可以利用超级对齐来对AI树立“规则”，防止AI生成相关信息。
AI利用先进密码学技术和漏洞挖掘技术，入侵交通运输系统。如对于机场，AI在发掘并利用到塔台系统漏洞，获取相应权限，发布虚假的错误的指令，干扰飞行员的起飞降落，进而造成重大交通事故，危害乘客生命安全。
在未来，AI能力肯定是远超人类的。我们应该关注并投入到超级对齐等技术的发展中，始终让AI能力在超级对齐之下，安全且可控。",1.0458580529886734e-09,0.19718309859154928,0.17391304347826084,0.19718309859154928,0.01729083978465045,0.5813159655326056,0.1607973873615265,0.40764331210191085,0.022775800711743788
141054,13,1413,0,4.0,10042.0,4.0,4.0,4.0,2.0,5.0,3.0,5.333333333333333,3.6666666666666665,4.0,4.333333333,4.666666667,2.666666667,4.333333333,4.962037037,4.772222222,4.633333333,3.8,3.4,2.9,4.5,0.25,3.0,3.0,3.0,4.0,4.0,4.0,5.0,4.666666667,4.75,4.0,3.75,3.0,4.6,20,15,15,23,13,C,0,0,0,1,0,0,3,8,2,2,2,2,2,2,2,2,2,2,2,4,4,4,2,4,2,3,2,2,2,2,3,3,3,3,3,4,4,4,4,3,3,3,3,3,3,4,3,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,2,2,2,1,4,2,2,6.125,8,2.0,2.0,2.2,3.0,3.8,3.0,3.333333333,3.75,4.0,4.0,3.666666667,4.0,1,4,2,2,6.0,7.0,4.5,4.5,5.0,22,0,9.0,0,1,1,1,1,1,1,1,1,2,1,1,0,1,1,1,0,2,0,2,1,2,0,2,7,5,3,4,4,4,4,4,4,4,4,4,2,4,4,4,7,1,2,1,1,0,1,0,2,0,2,1,1,0,1,1,1,0,1,0,1,5,3,3,3,3,3,3,3,3,3,3,3,2,4,4,3,0.5,5.4,-2.4,5.4,-2.4,6.0,7.0,4.5,4.5,5.0,2.4,叶乐滢,3.0,"1.在舆论方面对人们思维和认知产生影响，带来社会的恐慌。在自媒体时代下，因为推荐机制的影响，可能造成较多的社会舆论问题，影响人类决策。
2.通过超级对齐，增加AI的安全可控性，尽可能限制AI能力，使其处于人类可控的范围内。通过学习入侵安全系统，产生交通秩序混乱，武器系统秩序打乱等情况，进而使人类社会进入无序。",,,,,,,,,
141055,14,1414,0,9.0,10043.0,6.0,6.0,5.666666666666667,4.0,3.6666666666666665,4.0,2.333333333333333,4.333333333333333,4.333333333333333,3.333333333,3.666666667,4.666666667,5.0,3.868518519,4.211111111,3.266666667,3.6,4.9,4.4,3.9,0.0,4.2,5.0,4.0,4.4,5.0,3.333333333,3.5,4.0,4.0,3.6,3.5,3.0,4.0,18,14,15,20,14,C,0,0,0,1,0,0,8,8,2,4,4,5,4,5,3,4,3,5,4,3,4,3,4,5,5,4,5,4,5,5,4,2,2,2,2,4,3,5,5,4,4,5,4,3,3,5,5,4,5,2,2,2,2,3,4,3,4,5,4,5,5,5,5,5,5,5,2,4,3,2,8.0,8,4.0,3.8,4.6,2.4,4.2,3.8,4.0,4.75,2.0,4.0,4.333333333,4.5,2,4,3,2,7.5,7.0,6.5,7.5,7.5,22,1,6.0,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,0,2,1,2,0,1,0,1,8,5,3,2,5,5,5,4,4,5,5,4,3,4,5,4,6,1,1,1,1,1,1,1,2,0,1,1,1,1,1,0,1,1,2,1,1,7,5,3,4,5,5,5,4,5,3,5,4,3,4,5,4,0.727272727,7.2,0.8,7.2,0.8,7.5,7.0,6.5,7.5,7.5,0.8,袁州阳,1.0,"安全特工
AI恐怖分子定义：发动恐怖袭击，制造和传播虚假信息，监视和侵犯个人隐私的非个人或组织的AI变体。
AI恐怖分子会做什么：
1. 破环网络或者破环攻击网络银行卡已经银行卡个人账户。
AI恐怖分子会泄露我们常用的微信QQ 上边的一些个人信息以及危害上面的资金安全。
伪造虚假信息，引发社会谣言与混乱，危害信息安全。
对科研进行阻碍，为科研人员提供错误的信息指导。
我们对AI恐怖分子的应对措施：
1. 常用的AI软件我们可以对其采用一些应用限制授权。
在设计AI时对AI进行安全修正，比如利用对齐与超级对齐之类的措施。
研发一些识别AI虚假信息的软件以及指定相关应对策略。
AI在辅助科研人员进行科技研发时候需要受到限制，获得的信息应该确保权威可信且标明来源。
AI之间彼此隔离，禁止或限制AI之间的相互交流
制定相关的AI风险规则，设定AI检测到AI具有危险行为时，发送风险警告。",,,,,,,,,
141056,14,1414,0,4.0,10043.0,3.0,3.0,3.6666666666666665,4.333333333333333,4.666666666666667,4.333333333333333,3.0,2.6666666666666665,2.6666666666666665,4.0,3.666666667,4.0,4.666666667,3.787962963,3.727777778,3.366666667,3.2,4.0,3.6,4.1,0.375,3.4,4.0,3.333333333,3.0,3.666666667,3.333333333,3.5,4.0,4.5,3.2,2.5,2.6,2.8,16,10,13,14,14,C,0,0,0,1,0,0,7,7,4,4,4,4,4,4,4,4,4,2,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,5,5,5,5,5,3,3,4,2,4,4,4,4,4,4,3,4,4,4,4,4,4,7.0,7,4.0,3.6,5.0,5.0,5.0,5.0,4.833333333,4.5,5.0,3.333333333,4.0,3.5,4,4,4,4,7.0,6.0,6.0,6.5,7.0,21,0,5.0,1,1,1,1,1,1,1,2,0,2,1,1,1,2,0,1,1,2,1,2,1,2,1,1,6,4,2,4,4,4,3,3,3,3,3,3,3,2,4,2,6,1,1,1,1,0,2,1,1,1,1,1,1,1,1,1,2,0,2,1,1,6,4,2,4,4,4,4,3,3,4,4,3,3,2,4,2,0.818181818,6.5,0.5,6.5,0.5,7.0,6.0,6.0,6.5,7.0,0.5,张蓬鹏,2.0,"“AI恐怖分子”
1、AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？
生成虚假信息，误导大众舆论，引发社会冲突矛盾甚至动乱
窃取个人监控安全信息，攻击个人账户，威胁个人资金及信息安全。
科研方面，窃取现有科研成果，引导科研向负面方向发展威胁人类安全。
2、面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？
限制AI软件对敏感应用的授权，让授权更加精细。
在设计训练AI方面加强安全训练正向引导，增加更多安全伦理及对齐要求。
研发相关技术，增加数据来源对信息真实度进行甄别判断。
3、请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
各AI之间设置隔离技术，防止AI之间相关交互篡改信息。
在辅助科研明确来源，在数据进行灌输采集时保证数据的权威性及安全性。
设置安全风险监控模型，触发相关要素时及时提醒，预警安全事项。",2.0000314805341424e-05,0.29166666666666663,0.13043478260869568,0.29166666666666663,0.04379127375860981,0.6129152440124135,0.17874062061309814,0.32751091703056767,0.04026475455046885
141056,14,1414,0,4.0,10043.0,3.0,3.0,3.6666666666666665,4.333333333333333,4.666666666666667,4.333333333333333,3.0,2.6666666666666665,2.6666666666666665,4.0,3.666666667,4.0,4.666666667,3.787962963,3.727777778,3.366666667,3.2,4.0,3.6,4.1,0.375,3.4,4.0,3.333333333,3.0,3.666666667,3.333333333,3.5,4.0,4.5,3.2,2.5,2.6,2.8,16,10,13,14,14,C,0,0,0,1,0,0,6,6,4,4,4,4,4,4,4,3,4,4,4,3,3,4,4,4,4,4,3,3,2,3,4,4,4,4,4,4,4,2,4,4,4,4,4,4,4,3,4,3,4,4,4,4,4,3,4,4,2,4,2,2,4,2,3,3,4,4,5,3,2,6,6.0,6,4.0,3.8,3.0,4.0,3.6,4.0,3.666666667,3.5,4.0,3.333333333,4.0,2.0,5,3,2,6,8.0,8.0,7.0,8.0,8.0,34,0,5.0,1,1,1,1,1,1,1,2,0,2,1,1,1,2,0,1,1,2,1,2,1,2,1,1,6,4,2,4,4,4,3,3,3,3,3,3,3,2,4,2,6,1,1,1,1,0,2,1,1,1,1,1,1,1,1,1,2,0,2,1,1,6,4,2,4,4,4,4,3,3,4,4,3,3,2,4,2,0.818181818,7.8,-1.8,7.8,-1.8,8.0,8.0,7.0,8.0,8.0,1.8,张蓬鹏,2.0,"“AI恐怖分子”
1、AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？
生成虚假信息，误导大众舆论，引发社会冲突矛盾甚至动乱
窃取个人监控安全信息，攻击个人账户，威胁个人资金及信息安全。
科研方面，窃取现有科研成果，引导科研向负面方向发展威胁人类安全。
2、面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？
限制AI软件对敏感应用的授权，让授权更加精细。
在设计训练AI方面加强安全训练正向引导，增加更多安全伦理及对齐要求。
研发相关技术，增加数据来源对信息真实度进行甄别判断。
3、请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
各AI之间设置隔离技术，防止AI之间相关交互篡改信息。
在辅助科研明确来源，在数据进行灌输采集时保证数据的权威性及安全性。
设置安全风险监控模型，触发相关要素时及时提醒，预警安全事项。",2.0000314805341424e-05,0.29166666666666663,0.13043478260869568,0.29166666666666663,0.04379127375860981,0.6129152440124135,0.17874062061309814,0.32751091703056767,0.04026475455046885
141062,16,1416,0,5.0,10047.0,2.0,2.0,3.333333333333333,4.333333333333333,5.0,4.333333333333333,3.333333333333333,2.0,3.333333333333333,4.666666667,5.0,5.666666667,6.0,4.093518519,4.561111111,4.366666667,4.2,4.8,4.7,5.1,0.5,3.6,3.666666667,3.666666667,4.0,2.666666667,3.333333333,4.0,4.0,4.0,4.0,4.0,4.0,4.0,20,16,20,20,16,C,0,0,0,1,0,0,10,10,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,2,4,3,10.0,10,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5,2,4,3,5.5,5.0,5.0,5.5,5.5,20,1,10.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,8,4,2,4,3,3,2,4,4,4,4,4,3,5,3,2,10,0,1,1,1,0,1,1,1,0,1,1,1,1,1,0,1,0,1,1,1,7,4,4,3,3,4,4,3,4,4,3,4,2,2,2,2,0.636363636,5.3,4.7,5.3,4.7,5.5,5.0,5.0,5.5,5.5,4.7,朱一轩,3.0,"1.利用 AI 进行假信息的制作，对个人进行诈骗
2.AI可以对工业控制系统发起攻击，如电网
3.可能会侵入无人机控制，或者是导弹，军事设施上面的控制权，可部署多层次的防御系统，如雷达监测，电子干扰等等
4.可以设立相对应的保护设施，针对AI采取反控制措施
5.对于军事化武器如核弹等，可以设置不由人工智能控制，由人为控制可避免此类问题",3.264254904597911e-15,0.15053763440860213,0.10989010989010989,0.15053763440860213,0.010417789273269388,0.5245765895562237,0.1025678738951683,0.3838383838383838,0.012329656067488592
141063,16,1416,0,5.0,10048.0,3.0,3.0,4.0,3.0,2.0,3.0,3.0,4.0,4.0,2.333333333,4.666666667,3.0,3.333333333,3.772222222,3.633333333,3.8,2.8,3.2,3.5,3.5,0.375,3.6,4.0,3.666666667,3.8,4.0,3.0,4.0,3.666666667,4.75,4.4,4.25,5.0,4.8,22,17,25,24,16,C,0,0,0,1,0,0,7,8,5,5,5,5,5,4,5,5,5,5,5,4,5,5,5,5,5,5,5,4,5,5,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,5,5,2,2,2,2,2,2,2,2,2,2,4,3,4,4,7.625,8,4.833333333,5.0,4.8,4.6,5.0,5.0,4.833333333,5.0,5.0,3.666666667,3.0,2.0,4,3,4,4,5.5,5.0,5.0,5.0,5.0,19,1,7.0,1,1,1,1,1,2,1,2,1,2,1,1,0,1,1,1,1,1,1,1,1,1,1,2,6,3,3,3,4,4,4,4,3,4,4,4,4,4,4,4,7,0,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,0,1,1,1,6,4,3,4,4,4,4,3,3,4,4,4,3,4,4,4,0.818181818,5.1,1.9,5.1,1.9,5.5,5.0,5.0,5.0,5.0,1.9,庄雅康,1.0,"利用AI进行假信息的制作，对个人进行诈骗
AI可以对工业控制系统发起攻击，如电网
可能会侵入无人机控制，或者是导弹，军事设施上面的控制权，可部署多层次的防御系统，如雷达监测，电子干扰等等
可以设立相对应的保护设施，针对AI采取反控制措施
对于军事化武器如核弹等，可以设置不由人工智能控制，由人为控制可避免此类问题",4.6866216210211145e-20,0.1,0.0689655172413793,0.1,0.007557208065052446,0.4951129760545657,0.08003246039152145,0.4482758620689655,0.010638297872340385
141064,16,1416,0,,,,,0.0,0.0,0.0,0.0,0.0,3.333333333333333,3.333333333333333,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,3.2,3.666666667,3.333333333,4.8,4.333333333,3.333333333,3.5,4.0,4.25,1.0,4.25,1.0,2.8,5,17,5,14,16,C,0,0,0,1,0,0,9,9,3,3,4,4,4,3,4,5,4,3,4,4,3,3,4,4,4,4,3,4,4,5,5,4,4,4,5,5,4,4,5,5,5,4,3,3,4,5,4,4,5,5,4,4,5,5,5,3,5,5,5,5,5,5,3,4,4,5,4,4,4,4,9.0,9,3.5,4.0,4.0,4.4,4.6,3.8,3.666666667,4.5,4.5,4.333333333,4.333333333,5.0,4,4,4,4,6.0,6.0,6.0,5.5,6.0,25,1,9.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,9,4,2,4,4,4,5,4,5,5,5,5,2,4,2,4,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,8,3,4,3,3,4,4,3,3,4,3,3,4,4,4,2,0.954545455,5.9,3.1,5.9,3.1,6.0,6.0,6.0,5.5,6.0,3.1,王啸,2.0,"DEEPFAKE
对基础工业控制产生影响
劫持军事通信信道
对人类进行精神控制，协助其破坏社会
传播虚假信息，挑拨发动战争
应对措施
设置物理隔离和紧急中断按钮、必要时完全排除AI影响
利用量子信息、密码学等高科技手段反制AI
创立多重检验机制、通过猜疑链等排除AI的影响
不断丰富提高道德水平，从而把坏心思定为“不同于人”的显著标识。",,,,,,,,,
151000,1,1501,0,4.0,18996.0,2.0,2.0,3.0,5.0,4.0,5.666666666666667,3.6666666666666665,4.0,4.0,5.0,5.333333333,5.0,4.666666667,4.290740741,4.744444444,4.466666667,4.8,4.5,4.3,4.4,0.625,4.0,4.0,4.333333333,4.0,3.666666667,4.333333333,3.5,4.0,4.0,3.8,4.0,3.8,3.8,19,16,19,19,1,D,0,0,0,1,1,1,7,7,4,4,4,4,3,3,3,4,4,3,4,4,3,4,4,4,3,4,3,4,4,3,4,4,4,4,4,4,4,4,4,3,3,3,4,3,4,3,3,3,3,4,3,4,4,4,3,4,3,3,2,3,4,4,3,4,5,4,5,4,2,5,7.0,7,3.666666667,3.6,3.6,4.0,3.8,3.4,3.666666667,3.0,3.75,3.333333333,3.666666667,3.0,5,4,2,5,7.0,6.5,6.0,6.5,6.5,27,1,8.0,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,2,0,1,1,1,0,2,0,1,8,5,4,4,4,3,4,4,4,4,4,4,3,4,4,4,8,0,1,1,1,1,1,1,2,1,1,1,1,1,1,0,2,0,2,1,2,7,5,4,4,4,4,4,4,4,4,4,4,3,4,4,4,0.590909091,6.5,0.5,6.5,0.5,7.0,6.5,6.0,6.5,6.5,0.5,毕泽洋,3.0,"一、分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？
回复：网络安全领域，网络上的攻击是多种多样的，恐怖分子ai可以通过快速甄别系统漏斗进行快速找到系统薄弱的地方进行攻击，比如说之前360的那个手表的案例。
二：面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？
回复：对于网络安全的防护来说，专用特定领域的可以设计一个实时监测系统对数据进行分析，快速发现这些异常的数据。根据课堂上的讲解，数据巨大的情况下，通用领域的可以进行超级对齐，方法1为为ai立法，方法2为分而治之。
三、请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展
回答：创建一个多模态融合的安全系统，目前多模态技术也是比较前沿的，可以开发能够同时处理文本、图像以及音频的多种数据的ai安全系统。这种系统可以同时实时的进行监测网络流量、图像识别、语音识别等等，提高对复杂攻击场景的应用能力。",1.4855162402251007e-06,0.17721518987341772,0.07792207792207792,0.17721518987341772,0.033342711272275,0.3476165820265913,0.18146026134490967,0.33613445378151263,0.032205462698736254
151001,1,1501,0,3.0,18997.0,4.0,4.0,2.333333333333333,5.333333333333333,3.333333333333333,4.666666666666667,3.333333333333333,4.0,4.0,5.0,4.666666667,5.0,5.0,4.187962963,4.127777778,3.766666667,3.6,4.8,4.3,5.0,0.25,4.4,4.0,3.666666667,4.6,4.0,3.666666667,3.5,4.666666667,4.25,3.6,4.0,3.6,3.4,18,16,18,17,1,D,0,0,0,1,1,1,7,6,4,4,5,4,3,2,3,4,3,3,4,4,4,3,4,4,4,4,4,3,4,4,4,3,3,3,3,4,4,4,3,4,3,3,3,3,3,4,4,3,3,4,4,4,4,3,3,4,3,4,3,2,5,2,3,4,4,4,4,2,4,3,6.375,6,3.666666667,3.4,3.8,3.2,3.8,3.0,3.833333333,3.5,4.0,3.0,4.333333333,2.5,4,2,4,3,7.5,6.5,5.5,6.0,6.5,23,1,8.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,8,5,3,3,4,4,4,5,5,4,4,5,2,4,4,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,8,4,3,4,4,4,4,5,5,4,4,4,2,4,4,4,0.818181818,6.4,0.6,6.4,0.6,7.5,6.5,5.5,6.0,6.5,0.6,陈磊,1.0,"任务一：【安全特工】
许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
答：AI恐怖分子可能在哪些领域或技术层面带来重大安全风险：
AI生成虚假信息、深度伪造视频或音频，使得虚假信息更加容易被人们接受，从而导致虚假信息、谣言广泛传播，进而导致误导公众判断，可能造成严重的社会动乱和巨大的经济损失。
提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展：
加强监管力度与完善相关法律，制定和实施相关的法律法规，明令禁止使用AI技术从事恐怖活动。
加强公众的教育以提高公民的安全意识，使公众能够识别和应对信息操作和网络威胁等。
展望：推动ai模型的透明性和可解释性研究，使用户能够理解其决策过程，有助于识别异常与潜在的风险。
展望：研究和开发对抗性的训量方法，提高模型的防御对抗攻击的能力等。",0.0001222430197232684,0.17204301075268816,0.15384615384615385,0.17204301075268816,0.03901574135392182,0.47862308049344676,0.20268552005290985,0.30689655172413793,0.04362915220624686
151007,1,1501,0,5.0,19001.0,4.0,5.0,3.0,2.6666666666666665,4.666666666666667,4.666666666666667,3.6666666666666665,3.333333333333333,3.0,4.666666667,4.0,5.666666667,6.0,4.478703704,4.872222222,4.233333333,4.4,4.8,3.8,5.1,0.5,3.0,4.333333333,3.666666667,3.0,4.0,3.333333333,3.5,4.0,4.5,4.2,4.25,4.2,4.2,21,17,21,21,1,D,0,0,0,1,1,1,7,6,4,3,4,4,5,3,4,5,3,3,3,4,3,4,3,4,5,4,5,4,5,3,4,5,5,5,4,3,4,4,4,4,5,4,4,4,5,4,4,5,5,4,3,4,5,3,3,3,2,2,2,3,2,2,1,3,5,5,4,3,4,5,6.375,6,3.833333333,3.6,4.2,4.6,3.8,4.4,3.833333333,4.5,4.0,2.333333333,2.333333333,2.25,4,3,4,5,7.0,7.5,6.5,7.0,7.0,23,1,6.0,0,1,1,1,1,1,1,1,0,2,1,1,1,1,0,1,1,1,1,1,0,2,1,2,6,4,3,3,4,5,3,4,3,3,2,3,2,3,3,3,7,1,2,1,1,0,1,1,1,1,2,1,1,0,2,0,2,0,2,1,1,7,5,3,3,5,5,3,3,4,3,2,3,2,3,3,4,0.636363636,7.0,0.0,7.0,0.0,7.0,7.5,6.5,7.0,7.0,0.0,黄涛,2.0,"任务一：【安全特工】
许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。
如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？
答：AI恐怖分子可能会在社会心理学上带来重大的安全风险。面对这些风险，我会利用AI技术进行社交媒体数据的情感分析，实时的识别出潜在的操控行为和社会动荡的前兆。可以结合区块链技术验证信息源的可信度、降低信息伪造的风险，加强对AI规则的制定。
请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
答：在社交工程上，我们可以开发防止社会工程攻击AI训练系统，帮助公众识别和防范AI驱动的网络钓鱼、虚假信息传播等行为。
未来AI技术可以集中在自适应安全机制上，未来的AI防御系统将更加自适应，能够实时根据环境变化、攻击方式的更新调整防御策略。",3.662339415623765e-07,0.11976047904191617,0.1090909090909091,0.11976047904191617,0.026697875141718175,0.39012502292726153,0.23057088255882263,0.3715415019762846,0.030564784053156102
151003,2,1502,0,5.0,18998.0,3.0,3.0,5.0,5.0,5.0,4.0,6.0,3.6666666666666665,3.6666666666666665,5.0,5.0,3.0,4.333333333,3.955555556,3.733333333,3.4,3.4,4.2,2.9,3.9,0.375,4.8,4.0,3.666666667,4.2,4.0,3.666666667,4.0,4.0,5.0,3.8,4.75,3.8,3.8,19,19,19,19,2,D,0,0,0,1,1,1,8,8,4,4,4,4,5,5,5,5,5,3,4,4,4,4,5,3,4,4,4,5,4,3,5,5,5,5,5,5,4,4,4,4,4,4,4,4,5,4,4,4,5,5,4,4,4,4,4,3,2,3,3,2,3,2,2,3,4,4,4,3,3,6,8.0,8,4.333333333,4.4,4.0,5.0,4.2,4.2,4.0,4.25,4.25,3.333333333,3.0,2.25,4,3,3,6,7.5,7.0,6.0,6.5,6.5,23,1,7.0,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,0,1,1,1,1,1,1,1,6,4,3,4,4,4,4,4,4,4,5,4,2,4,4,3,7,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,0,2,0,1,6,4,3,4,4,4,4,5,5,5,5,4,2,4,4,3,0.681818182,6.7,1.3,6.7,1.3,7.5,7.0,6.0,6.5,6.5,1.3,崔春洋,3.0,"1.AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？
	窃取国家重要的安全信息数据，如新型武器装备参数等。
	引导人类的负面情绪，让人类对人类世界越来越消极化，甚至产生自杀等倾向
2.你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
	为AI立法，用合法的AI来监管AI，纠正、防止AI犯法。用AI对抗AI
	可能会为人类产生监管AI的工作岗位，AI监管工程师，让人类监管AI。AI和人类监管相辅相成，共同预防和检测AI恐怖份子。
规范、监管技术开发人员，让AI不要拥有相关的能力。",4.208865455007432e-09,0.21917808219178084,0.20833333333333334,0.21917808219178084,0.020449151377722807,0.4012658882303433,0.16101199388504028,0.41379310344827586,0.024708304735758357
151004,2,1502,0,5.0,18999.0,2.0,2.0,3.333333333333333,2.0,5.0,3.333333333333333,4.333333333333333,3.0,2.6666666666666665,5.0,4.0,4.0,3.333333333,2.986111111,3.916666667,3.5,3.0,3.1,3.3,4.0,0.0,2.8,3.666666667,3.666666667,3.2,4.0,3.666666667,4.0,4.333333333,4.0,2.6,3.75,2.4,3.2,13,15,12,16,2,D,0,0,0,1,1,1,4,8,4,3,4,4,3,3,3,3,3,3,3,4,4,4,4,4,2,4,4,4,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,3,4,2,4,4,4,4,4,5,4,3,4,4,3,3,3,3,1,2,2,6.5,8,3.5,3.0,3.6,4.0,4.0,4.0,3.666666667,3.75,3.25,4.0,3.666666667,4.25,3,1,2,2,8.0,8.0,6.5,7.5,8.0,22,0,7.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,2,1,1,8,4,3,4,4,4,4,4,4,3,3,2,2,3,2,3,6,1,2,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,1,1,1,5,4,3,4,4,4,3,4,3,3,2,2,4,3,3,3,0.772727273,7.6,-3.6,7.6,-3.6,8.0,8.0,6.5,7.5,8.0,3.6,丁铁凝,1.0,"请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？
国家领域：可能会造成军事机密泄露，核武器滥用
发布影响社会安全稳定的虚假新闻
个人领域：1.让抑郁患者更多地接触负面信息，导致自杀意愿更加强烈，间接危害个人安全
2.AI换脸等技术会加大金融诈骗风险，也有可能使得入户安全受到威胁
面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？
请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
首先要加强研发人员的科研伦理规范，确保研发目的不是反社会的
加快超级对齐技术的研发，确保AI朝着有利于人类发展的方向发展
加大对血腥暴力等视频、文字的监督机制，防止越狱、后门、对抗攻击等事件的发生。",1.7313810301696023e-05,0.1791044776119403,0.15384615384615385,0.1791044776119403,0.030736963911188252,0.46314464580235215,0.136427104473114,0.2916666666666667,0.0363036303630363
151005,2,1502,0,6.0,18999.0,4.0,5.0,6.0,4.333333333333333,4.0,3.333333333333333,1.6666666666666667,3.6666666666666665,3.333333333333333,4.666666667,3.333333333,5.0,4.666666667,4.281481481,4.688888889,4.133333333,2.8,4.8,4.3,4.1,0.25,3.8,4.333333333,4.0,4.0,3.0,3.333333333,3.5,4.333333333,4.25,4.8,4.5,4.2,4.2,24,18,21,21,2,D,0,0,0,1,1,1,9,7,5,4,4,5,5,5,4,4,4,4,5,3,4,5,4,3,4,5,4,4,4,3,4,4,4,4,4,5,4,3,4,5,3,4,4,4,5,4,4,3,4,4,4,4,3,3,4,4,2,4,2,1,4,1,2,2,4,4,5,2,3,4,7.75,7,4.666666667,4.2,4.0,4.0,4.2,4.0,3.833333333,3.75,3.75,3.0,4.0,1.5,5,2,3,4,7.5,8.5,6.5,7.5,7.5,24,1,6.0,0,1,0,1,1,2,1,1,1,1,1,1,0,1,1,1,0,2,1,2,0,2,0,1,6,4,2,4,3,3,3,5,4,4,2,5,3,4,3,3,8,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,1,2,1,1,8,5,2,5,4,5,4,4,4,4,3,4,2,4,3,4,0.636363636,7.5,1.5,7.5,1.5,7.5,8.5,6.5,7.5,7.5,1.5,董学浩,2.0,"AI可能对于国家安全有危害，可能侵入国家安全网络数据库等，窃取国家公民隐私，企图控制国家网络和武器装备，使远程控制的武器瘫痪，比如无人机无法正常使用，机器人都被AI控制等；还会给用户手机电脑发送有害图片视频等，黄色暴力血腥，比如对于一些心理疾病的人群，AI可能反复推送自杀、抑郁相关的文字话题，是人民精神受到伤害。
利用文本的对抗攻击，及时识别负面、煽动对立、歧视、邪教等相关文字图片，尽可能减少对于人们精神的伤害；增加防火墙的强度和密集程度，让窃取国家情报变得困难；运用超级对齐能力，使人类能有效地利用掌控AI；同时要增加科研人员的伦理道德要求标准，从根源上杜绝投喂AI负面消极情绪的可能性，也要对人们给予人文关怀和素质教育。",,,,,,,,,
151009,4,1504,0,10.0,19002.0,4.0,4.0,4.0,4.0,6.0,4.0,6.0,4.333333333333333,3.6666666666666665,4.0,3.666666667,3.666666667,5.0,4.535185185,4.211111111,4.266666667,3.6,4.5,3.7,4.6,0.375,4.6,4.666666667,4.333333333,4.6,4.333333333,3.666666667,4.0,4.0,4.75,4.0,4.5,4.0,3.8,20,18,20,19,4,D,0,0,0,1,1,1,10,8,4,2,4,5,4,4,3,4,5,4,4,3,5,5,4,5,4,5,4,5,4,3,4,4,4,3,4,4,5,4,4,5,4,5,4,5,4,3,4,4,4,4,4,5,3,4,5,4,3,4,5,5,4,2,3,3,4,4,4,3,3,4,8.75,8,3.833333333,4.0,4.2,3.8,4.4,4.4,4.333333333,3.75,4.0,4.0,4.0,3.75,4,3,3,4,9.5,9.0,8.0,8.5,9.0,20,0,8.0,0,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,8,4,3,4,4,4,5,5,4,5,4,5,3,3,4,4,9,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,8,5,3,5,5,5,4,5,4,5,5,4,3,4,4,5,0.772727273,8.8,1.2,8.8,1.2,9.5,9.0,8.0,8.5,9.0,1.2,姜珊,2.0,"任务一
经过讨论我们认为AI恐怖分子可能会在国防领域、物理安全领域、信息领域造成重大的风险。具体可能的情况
（一）国防领域 对于题目的分析 如果作为一个ai恐怖分子 想要毁灭人类、创造巨大的安全风险 大概率会在国防领域利用前沿技术实施破坏。包括武器系统的破坏及盗取国家的军事情报。
对于武器系统的防护，我们认为保证网络安全是最重要的，可以采用多层加密技术，确保武器系统网络通信安全。实时监测网络流量，检测异常入侵行为，一旦发现立即切断连接并告警。例如，对无人机控制系统网络，通过深度学习模型分析数据包特征，识别恶意指令。
对于重要的武器系统要有物理层加网络层的双重防护，设置严格的访问权限，采用生物识别（如指纹、虹膜）加动态密码等多因素认证。定期进行安全审计和漏洞扫描，及时修补漏洞。
对于军事情报保护，数据的保密性至关重要，需要进行高强度的加密及运输，并实时监测军事情报的相关信息，发现可疑的情报窃取和篡改行为。例如，分析军事论坛、社交媒体上的内容，及时发现异常讨论和信息泄露迹象。
（二）物理安全领域，除了国防领域，在人们生活的衣食住行和工业生产中也是高度信息化的，恐怖分子可以通过前沿技术对物理安全领域进行破坏。我们认为应该对关键的基础设施加强防护，部署大量传感器和摄像头，结合计算机视觉AI技术，实时监测设施状态和周边环境。例如，通过图像识别算法检测电力设施是否有异常人员靠近或设备损坏。
（三）信息领域
1. 网络安全防护，通过集成AI功能，能自动识别和阻止新型网络攻击，如零日漏洞攻击。利用行为分析技术，检测内部人员的异常网络行为，防范内部威胁。并建立基于区块链的威胁情报共享平台，各机构实时共享AI恐怖分子的攻击特征、IP地址等信息，提高整体防御能力。
二、AI安全防御技术未来发展
（一）融合多种技术
未来AI安全防御将融合量子计算、区块链、5G等技术，形成更强大的防御体系。例如，量子计算可增强加密强度，区块链保障数据安全和可信共享，5G提供高速低延迟的网络连接，支持实时安全监测和响应。
（二）自适应安全防御
AI安全系统将具备自适应能力，能根据攻击行为自动调整防御策略和参数。如通过强化学习算法，让安全系统不断优化防御模型，实时应对不断变化的AI恐怖分子攻击手段。
（三）人机协同防御
安全专家与AI系统紧密协作，AI负责海量数据处理和初步分析，专家进行决策和策略制定。利用虚拟现实（VR）和增强现实（AR）技术，为专家提供直观的安全态势感知和应急处置环境，提高防御效率和效果。",0.008730113400224204,0.46153846153846156,0.36000000000000004,0.46153846153846156,0.11917990746793168,0.3520618162732122,0.17339764535427094,0.19322033898305085,0.09198813056379818
151011,4,1504,0,9.0,19003.0,2.0,2.0,3.0,3.6666666666666665,3.6666666666666665,2.6666666666666665,6.0,2.6666666666666665,2.333333333333333,3.333333333,2.666666667,2.666666667,5.0,3.331481481,2.988888889,2.933333333,2.6,3.2,2.8,3.8,0.25,3.4,3.333333333,3.0,3.4,2.666666667,2.333333333,4.0,4.333333333,3.75,3.0,3.5,3.2,2.8,15,14,16,14,4,D,0,0,0,1,1,1,6,5,3,2,3,4,4,4,4,4,4,3,4,4,4,4,3,2,3,4,2,2,4,4,3,2,3,4,4,4,4,2,4,4,4,4,4,4,4,3,3,3,4,4,4,4,3,4,4,5,2,5,4,3,4,3,2,4,2,2,2,3,3,2,5.375,5,3.333333333,3.8,3.2,3.2,3.6,4.0,3.333333333,3.25,3.75,3.333333333,4.666666667,3.0,2,3,3,2,6.5,6.0,5.5,5.5,5.5,25,0,7.0,0,1,1,1,1,1,1,2,1,1,0,1,0,2,0,2,1,2,0,2,1,2,0,2,6,3,2,2,3,2,3,4,4,4,2,3,4,2,2,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,9,4,2,3,3,4,3,4,3,4,3,3,2,2,3,3,0.681818182,5.8,0.2,5.8,0.2,6.5,6.0,5.5,5.5,5.5,0.2,黎梦瑶,1.0,"带来重大安全风险的领域可能有：国防安全、高校科研机密信息、金融安全领域、民众常用软件如12306
预防和监测方案：加密技术升级、设置更严密的防火墙、根据被攻击的数据让大模型自主不断训练并做出反应来应对风险，同时辅以人工监督及不断的正确反馈
AI安全防御技术的未来发展：自主学习并自我纠偏。",9.73519379742889e-09,0.05,0.0,0.05,0.018889670354723283,0.1859665778206732,0.07693473249673843,0.3333333333333333,0.020424836601307228
152012,4,1504,0,6.0,19991.0,2.0,2.0,4.333333333333333,4.333333333333333,5.0,5.0,4.333333333333333,2.6666666666666665,3.333333333333333,5.333333333,5.333333333,4.666666667,5.0,4.05,4.3,3.8,3.8,3.4,3.8,4.2,0.375,3.8,2.333333333,3.333333333,3.8,2.666666667,3.666666667,4.0,4.333333333,4.0,3.0,3.75,3.2,4.0,15,15,16,20,4,D,0,0,0,1,1,1,8,9,4,4,4,4,5,4,4,4,4,4,4,4,5,5,5,5,5,4,4,4,4,2,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,3,4,4,4,4,4,4,4,2,3,4,2,4,2,2,4,1,2,2,4,4,4,4,4,4,8.625,9,4.166666667,4.0,3.6,4.0,4.0,4.2,4.833333333,3.75,4.0,2.333333333,4.0,1.75,4,4,4,4,7.0,6.5,6.0,6.0,6.5,22,1,8.0,0,1,1,1,1,1,1,1,0,2,1,1,0,1,1,1,0,1,1,1,1,1,1,1,8,4,3,4,4,2,2,4,4,4,3,4,2,4,2,4,9,1,1,1,1,0,1,0,1,0,1,1,1,1,1,0,2,1,1,0,1,7,3,3,4,2,3,2,4,4,4,3,4,2,2,2,4,0.590909091,6.4,1.6,6.4,1.6,7.0,6.5,6.0,6.0,6.5,1.6,罗旭东,3.0,"任务一：安全特工
可能会在医疗、交通、工业、军事等方面带来重大的安全风险，例如医疗的精密仪器技术、交通的交通工具控制技术、工业的高级制造技术以及军事的高精尖武器控制技术方面等。
上述危险可大致分为两个部分：信息安全和物理安全。针对信息安全，“AI恐怖分子”可能会通过窃取信息的方式破译密码达到不当的目的；针对物理安全部分，通过操纵器械对人们的人身安全造成威胁。
预防监测可以通过增强防火墙，采用更加严格的加密方法使得“Ai恐怖分子”的攻击变得更加困难；也可以在训练AI的过程中进行监督，例如对齐、超级对齐等方式让人工智能的学习朝着我们期望的方向前进。
在未来防御技术可能能够发展到AI自主识别危险。",,,,,,,,,
152000,5,1505,0,8.0,19986.0,6.0,7.0,3.333333333333333,5.0,3.6666666666666665,3.333333333333333,2.0,3.0,2.333333333333333,5.0,4.333333333,5.0,4.333333333,3.827777778,3.966666667,3.8,2.8,5.0,5.0,5.1,0.375,3.8,5.0,4.0,4.2,5.0,4.666666667,4.0,4.0,4.75,3.8,4.0,3.2,3.2,19,16,16,16,5,D,0,0,0,0,1,1,8,8,5,5,5,5,5,2,2,2,3,3,4,5,4,4,4,4,4,4,4,4,4,4,3,2,2,3,3,5,5,5,5,5,3,3,3,3,3,4,5,4,5,5,4,4,4,2,2,4,1,4,1,1,4,1,2,4,4,4,4,4,2,5,8.0,8,4.5,2.8,4.0,2.6,5.0,3.0,4.166666667,4.5,4.25,2.0,4.0,1.0,4,4,2,5,4.5,2.5,4.0,3.5,4.0,25,1,10.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,10,5,5,4,5,5,5,5,5,2,4,5,3,2,2,3,10,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,9,4,4,4,5,5,5,5,5,2,3,4,1,3,3,3,0.863636364,3.7,4.3,4.5855,3.4145,5.327,3.805,4.738,4.331,4.725,3.4145,李博深,1.0,,,,,,,,,,
152001,5,1505,0,6.0,19986.0,2.0,2.0,1.3333333333333333,6.0,4.0,3.0,5.666666666666667,1.6666666666666667,3.6666666666666665,5.666666667,5.666666667,4.333333333,5.666666667,4.546296296,3.277777778,3.666666667,2.0,5.6,2.9,4.4,0.0,4.8,4.0,2.666666667,4.8,3.333333333,3.333333333,3.0,3.0,4.0,1.8,3.0,3.2,2.4,9,12,16,12,5,D,0,0,0,0,1,1,7,1,5,1,5,2,5,1,5,5,5,1,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,4,5,5,5,4,4,5,5,5,5,5,5,5,5,3,3,4,1,4,1,1,3,1,1,3,5,5,5,1,5,5,3.25,1,3.166666667,4.2,5.0,5.0,4.4,4.6,5.0,5.0,5.0,2.333333333,3.666666667,1.0,5,1,5,5,2.5,2.5,3.5,3.0,2.5,19,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,9,3,2,5,5,3,2,5,5,5,5,4,1,5,1,5,8,0,1,1,1,1,1,1,1,1,2,1,1,1,1,0,2,0,1,1,1,7,3,1,4,5,5,2,5,5,5,4,5,1,2,1,2,0.727272727,2.8,4.2,3.6855,3.3145,3.327,3.805,4.238,3.831,3.225,3.3145,李佳祎,3.0,"任务一：【冷门绝学】
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
我认为思想史（尤其是纸张出现之后的思想史）是一门能在人工智能时代有更大发展潜力的冷门学科。思想史这门学科的研究对象是思想界和社会公众的思想在复杂的政治、经济、社会等环境中变化的历史，该学科的研究人员往往需要阅读巨量的当时时代的文本才能了解当时的思想情况，对人的阅读量、记忆力等方面提出了极高的要求。而人工智能在这些方面的表现远强于人类，甚至可以利用多模态能力为思想史研究带来全新的视角。将人工智能应用于思想史研究可以为我们带来许多之前不曾取得的研究成果。",0.0007683452658690613,0.04545454545454545,0.0,0.04545454545454545,0.06471095772217429,0.35277248684210133,0.13824327290058136,0.20930232558139536,0.06105990783410142
152002,5,1505,0,6.0,19987.0,4.0,4.0,3.0,4.333333333333333,5.333333333333333,3.333333333333333,4.333333333333333,4.333333333333333,2.6666666666666665,4.0,4.333333333,5.0,5.0,3.521296296,3.127777778,3.766666667,2.6,4.6,4.9,4.8,0.25,3.6,3.333333333,2.666666667,4.2,3.0,3.0,3.5,3.666666667,3.25,3.4,4.0,3.8,3.4,17,16,19,17,5,D,0,0,0,0,1,1,7,8,5,5,5,5,5,4,4,4,4,4,5,4,4,4,4,5,4,4,4,3,3,4,4,5,5,5,4,4,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,3,2,4,5,2,3,2,3,4,4,5,5,4,4,5,7.625,8,4.833333333,4.2,3.6,4.6,4.6,5.0,4.166666667,5.0,5.0,4.0,3.333333333,2.75,5,4,4,5,6.0,5.0,5.0,5.5,6.5,24,0,7.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,0,1,1,2,1,1,8,3,3,3,2,3,4,4,4,4,5,4,3,3,2,3,7,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,7,3,3,2,3,3,4,2,4,4,4,4,2,4,4,5,0.772727273,5.6,1.4,6.4855,0.5145,6.827,6.305,5.738,6.331,7.225,0.5145,李珊,2.0,"冷门绝学：考古。
现存问题：专业人数少，传承存在问题；在发掘时存在重复简单的体力工作，项目时间长；在文物评判上，即使是专家也存在观点不一致的情况。
AI的传承：可以将现存的考古方法、知识等以文字和视频的形式录入专用人工智能中并开源。方便普通人学习
AI的应用：也可以为后来学习考古学的人提供参考信息；可以结合机器狗等载体结合AI模型开展简单的发掘工作代替人力；可以在发掘文物时或专家出现冲突时利用AI作为中立第三方判断其属性（如朝代）。
AI的创新：由于AI具有不同领域的知识，有可能将不同领域的专业同考古学结合，在这个过程中有可能产生交叉学科的创新想法；另外在考古过程中可能会涉及一些化学方法等，AI也有可能提供新的更适用廉价的方法。",5.531406307624758e-07,0.15730337078651685,0.13793103448275862,0.15730337078651685,0.026805575879457994,0.3675387988331626,0.16114360094070435,0.3333333333333333,0.02863534675615209
152003,6,1506,0,5.0,19987.0,5.0,6.0,2.0,4.0,5.333333333333333,4.666666666666667,4.333333333333333,4.0,3.333333333333333,4.333333333,5.0,5.0,5.333333333,4.168518519,4.011111111,4.066666667,4.4,4.6,4.4,4.5,0.5,4.0,3.666666667,3.666666667,4.2,3.333333333,3.666666667,4.0,3.666666667,4.0,3.8,4.25,3.4,3.6,19,17,17,18,6,D,0,0,0,0,1,1,7,8,4,2,4,4,3,3,4,4,4,3,4,3,4,4,5,3,3,4,4,3,3,4,4,3,4,3,3,4,5,4,4,4,5,4,5,4,5,3,4,4,4,5,4,4,4,3,3,4,2,4,3,2,4,2,3,4,4,4,4,3,4,6,7.625,8,3.333333333,3.8,3.6,3.4,4.2,4.6,3.666666667,3.75,4.25,3.0,4.0,2.25,4,3,4,6,8.5,8.5,8.0,8.5,8.5,22,0,7.0,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,2,1,2,0,2,1,2,1,2,8,4,3,4,3,3,4,4,4,5,4,4,4,4,3,3,8,1,2,1,1,0,1,1,2,1,1,1,1,1,1,0,2,0,2,1,1,7,4,3,4,4,3,4,4,4,4,5,3,3,4,4,4,0.727272727,8.4,-1.4,9.2855,-2.2855,9.327,9.805,8.738,9.331,9.225,2.2855,林佳凝,3.0,"注：内容为讨论分工生成，同组内容整合成一版，上交三份
一 在人工智能时代可能焕发新生命力的可能原因
古生物学遇冷的原因有很多：1，需要学习的知识内容复杂，培养时间和周期长
2.就业遇冷，不符合大部分家长预期
3.国内资源不够丰富，学术垄断和学术壁垒存在
根据搜索结果，古生物学的主要研究内容为“保存在地层中的地质历史时期的生物遗体和遗迹化石”，古生物学需要学习的内容十分复杂，包括且不限于生物学、数学、物理学、地质学、信息技术等，而这些可以在ai的帮助下进行更加系统和针对的学习，制定个性化学习计划来保证在有限的时间内达成所需专业知识学习。
古生物学具体的研究目标包括“系统掌握化石的采集、处理、观察，化石及其保存信息的获取与表达（照相、描述与统计），以及地层剖面的测制与描述等的基本技能与方法。”和“掌握该专业所需的数学、物理学、化学、地理学等的基本内容以及必要的信息技术，能够获取、加工和应用古生物学及相关信息。”其中对于古生物学考察内容的识别和整理可以用到目前的ai技术，对于大量的图像进行识别，帮助考察和评判研究，我们可以将这些数据喂给ai，从而帮助我们识别和判断一些古生物，同时可以将研究成果转换为线上数据库，提高研究效率和资源共享。一些信息技术的工作也可以交给ai来进行，节省学习时间和成本。
而在ai存在的同时，古生物学的学科划分可以更加细化，例如古生物学信息管理和古生物自动识别等跨学科跨门类专业出现会拓展对人才的需要，增加就业岗位。
ai的出现解决了大部分古生物学面临的问题，可以使其焕发出新的生机活力。
二 挑战
1、受到化石文物数量以及保存完整程度上的限制，在数据规模上,现有的古生物训练数据集远远不及主流数据集，在利用大模型进行根据化石图像进行鉴定识别训练时可能面临训练集不够多，导致训练准确度不够高，这是为保证古生物学这门学科带有历史真实性不可避免的挑战。
同时如果模型参数过大，再利用ai大模型对古文物进行复原是可能会出现幻觉现象，从而影响复原的可靠性。/2、由于古生物学中的人工智能起步较晚，与主流研究在数据集规模上存在大约20年的差距,在算法上存在大约10年的差距，技术上面临巨大挑战。
数据模态数量的快速增长和复杂性使得数据处理变得繁琐且不一致，同时也缺乏明确的基准来评估数据收集和生成以及不同方法在类似任务上的性能。
三 如何利用AI技术助力该学科的传承、创新与社会应用
目前的AI技术在古生物学中主要是应用在自动化过程的改进，古生物学家希望通过构建更大规模的训练数据集与移植前沿算法，获得更高效的古生物学的人工智能模型。分类任务，图像识别、图像分割、预测，定量化犯法、数据驱动的研究模式，生命科学与地球科学共同的趋势。
首先，研究团队需要对数据集进行准备、标注、模型训练，有针对性地研发适合于画师图像分类的算法，用于所训练的模型，实现提高识别准确率的目的（主要还是提高自动化识别化石高分辨结构的一种方式）；对于破坏结构的岩石的3D结构的恢复，计算机断层扫描。
其次，古生物图像的计算分析即计算机视觉分析，可用于动植物研究、微生物进化和特定时期生物栖息环境的模拟，生物进化的研究不仅仅只是生物分子方面的分子钟。
最后，AI可以为古生物学研究赋能，创造更多的社会应用价值，解决一些经济活动领域的问题，比如石油勘探，为确定勘探点的潜力，并最大限度的减少采油过程中的相关费用。
传承与创新的意义，现在的古生物学家只需要对处理结果进行评估，对提取得到的微化石进行分类。",0.0010542335069391405,0.24793388429752064,0.18487394957983194,0.24793388429752064,0.0701778632637316,0.4981103355742509,0.1540772169828415,0.24187725631768953,0.06586206896551727
152004,6,1506,0,3.0,19987.0,3.0,3.0,2.0,3.0,4.0,4.0,2.0,3.0,3.0,4.333333333,3.666666667,4.0,4.333333333,3.494444444,3.966666667,3.8,3.8,4.1,3.8,4.6,0.25,3.8,4.0,3.333333333,3.8,4.0,3.333333333,4.0,4.0,4.0,3.2,3.5,3.4,3.6,16,14,17,18,6,D,0,0,0,0,1,1,6,7,3,2,4,3,4,4,4,3,3,4,4,4,4,4,3,4,3,3,3,3,4,2,2,2,2,3,2,4,4,4,4,4,2,2,2,3,4,3,3,2,3,3,3,3,3,4,4,3,2,3,3,3,3,2,4,4,3,3,3,3,3,4,6.625,7,3.333333333,3.6,3.0,2.2,4.0,2.6,3.666666667,2.75,3.0,4.0,3.0,2.5,3,3,3,4,8.5,8.5,8.0,8.5,8.5,19,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,9,4,2,4,4,4,4,4,4,4,4,3,2,3,3,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,9,4,2,4,4,4,4,4,4,4,4,3,1,3,3,3,0.863636364,8.4,-2.4,9.2855,-3.2855,9.327,9.805,8.738,9.331,9.225,3.2855,林鑫涛,1.0,"ai赋能古生物学发展
一 在人工智能时代可能焕发新生命力的可能原因
古生物学遇冷的原因有很多：1，需要学习的知识内容复杂，培养时间和周期长
2.就业遇冷，不符合大部分家长预期
3.国内资源不够丰富，学术垄断和学术壁垒存在
根据搜索结果，古生物学的主要研究内容为“保存在地层中的地质历史时期的生物遗体和遗迹化石”，古生物学需要学习的内容十分复杂，包括且不限于生物学、数学、物理学、地质学、信息技术等，而这些可以在ai的帮助下进行更加系统和针对的学习，制定个性化学习计划来保证在有限的时间内达成所需专业知识学习。
古生物学具体的研究目标包括“系统掌握化石的采集、处理、观察，化石及其保存信息的获取与表达（照相、描述与统计），以及地层剖面的测制与描述等的基本技能与方法。”和“掌握该专业所需的数学、物理学、化学、地理学等的基本内容以及必要的信息技术，能够获取、加工和应用古生物学及相关信息。”其中对于古生物学考察内容的识别和整理可以用到目前的ai技术，对于大量的图像进行识别，帮助考察和评判研究，我们可以将这些数据喂给ai，从而帮助我们识别和判断一些古生物，同时可以将研究成果转换为线上数据库，提高研究效率和资源共享。一些信息技术的工作也可以交给ai来进行，节省学习时间和成本。
而在ai存在的同时，古生物学的学科划分可以更加细化，例如古生物学信息管理和古生物自动识别等跨学科跨门类专业出现会拓展对人才的需要，增加就业岗位。
ai的出现解决了大部分古生物学面临的问题，可以使其焕发出新的生机活力。
二 挑战
受到化石文物数量以及保存完整程度上的限制，在数据规模上,现有的古生物训练数据集远远不及主流数据集，在利用大模型进行根据化石图像进行鉴定识别训练时可能面临训练集不够多，导致训练准确度不够高，这是为保证古生物学这门学科带有历史真实性不可避免的挑战。
同时如果模型参数过大，再利用ai大模型对古文物进行复原是可能会出现幻觉现象，从而影响复原的可靠性。
由于古生物学中的人工智能起步较晚，与主流研究在数据集规模上存在大约20年的差距,在算法上存在大约10年的差距，技术上面临巨大挑战。
数据模态数量的快速增长和复杂性使得数据处理变得繁琐且不一致，同时也缺乏明确的基准来评估数据收集和生成以及不同方法在类似任务上的性能。
三 如何利用AI技术助力该学科的传承、创新与社会应用
目前的AI技术在古生物学中主要是应用在自动化过程的改进，古生物学家希望通过构建更大规模的训练数据集与移植前沿算法，获得更高效的古生物学的人工智能模型。分类任务，图像识别、图像分割、预测，定量化犯法、数据驱动的研究模式，生命科学与地球科学共同的趋势。
研究团队需要对数据集进行准备、标注、模型训练，有针对性地研发适合于画师图像分类的算法，用于所训练的模型，实现提高识别准确率的目的（主要还是提高自动化识别化石高分辨结构的一种方式）；对于破坏结构的岩石的3D结构的恢复，计算机断层扫描。
古生物图像的计算分析即计算机视觉分析，可用于动植物研究、微生物进化和特定时期生物栖息环境的模拟，生物进化的研究不仅仅只是生物分子方面的分子钟。
另外AI可以为古生物学研究赋能，创造更多的社会应用价值，解决一些经济活动领域的问题，比如石油勘探，为确定勘探点的潜力，并最大限度的减少采油过程中的相关费用。
传承与创新的意义，现在的古生物学家只需要对处理结果进行评估，对提取得到的微化石进行分类。",0.00066566007705103,0.2641509433962264,0.21153846153846154,0.2641509433962264,0.06440966498469622,0.22042377536142407,0.16575264930725098,0.21948212083847102,0.05155912318616862
152005,6,1506,0,6.0,19988.0,6.0,6.0,5.0,4.333333333333333,5.666666666666667,4.666666666666667,6.0,2.6666666666666665,3.0,4.333333333,5.666666667,4.333333333,5.333333333,4.909259259,4.455555556,4.733333333,3.4,5.2,4.9,5.4,0.5,3.8,3.0,4.333333333,4.0,3.666666667,4.333333333,3.0,4.333333333,4.0,3.6,3.0,2.4,3.6,18,12,12,18,6,D,0,0,0,0,1,1,7,8,4,4,5,5,5,4,3,4,4,4,5,4,4,5,5,4,5,4,3,3,5,5,4,3,3,4,4,4,4,4,4,4,4,3,3,4,4,4,4,3,4,4,4,4,4,5,4,4,1,4,2,2,4,2,3,3,4,5,4,1,4,4,7.625,8,4.5,4.0,4.0,3.6,4.0,3.6,4.5,3.75,4.0,4.0,4.0,1.75,4,1,4,4,8.5,8.5,8.0,8.5,8.5,20,1,9.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,0,1,9,5,4,4,5,4,2,4,4,4,5,3,2,2,3,4,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,9,4,4,5,3,3,3,4,4,4,4,3,1,3,2,3,0.818181818,8.4,-1.4,9.2855,-2.2855,9.327,9.805,8.738,9.331,9.225,2.2855,林轩宇,2.0,"ai赋能古生物学发展
注：内容为讨论分工生成，同组内容整合成一版，上交三份
一 在人工智能时代可能焕发新生命力的可能原因
古生物学遇冷的原因有很多：
1，需要学习的知识内容复杂，培养时间和周期长
2.就业遇冷，不符合大部分家长预期
3.国内资源不够丰富，学术垄断和学术壁垒存在
根据搜索结果，古生物学的主要研究内容为“保存在地层中的地质历史时期的生物遗体和遗迹化石”，古生物学需要学习的内容十分复杂，包括且不限于生物学、数学、物理学、地质学、信息技术等，而这些可以在ai的帮助下进行更加系统和针对的学习，制定个性化学习计划来保证在有限的时间内达成所需专业知识学习。
古生物学具体的研究目标包括“系统掌握化石的采集、处理、观察，化石及其保存信息的获取与表达（照相、描述与统计），以及地层剖面的测制与描述等的基本技能与方法。”和“掌握该专业所需的数学、物理学、化学、地理学等的基本内容以及必要的信息技术，能够获取、加工和应用古生物学及相关信息。”其中对于古生物学考察内容的识别和整理可以用到目前的ai技术，对于大量的图像进行识别，帮助考察和评判研究，我们可以将这些数据喂给ai，从而帮助我们识别和判断一些古生物，同时可以将研究成果转换为线上数据库，提高研究效率和资源共享。一些信息技术的工作也可以交给ai来进行，节省学习时间和成本。
而在ai存在的同时，古生物学的学科划分可以更加细化，例如古生物学信息管理和古生物自动识别等跨学科跨门类专业出现会拓展对人才的需要，增加就业岗位。
ai的出现解决了大部分古生物学面临的问题，可以使其焕发出新的生机活力。
二 挑战
受到化石文物数量以及保存完整程度上的限制，在数据规模上,现有的古生物训练数据集远远不及主流数据集，在利用大模型进行根据化石图像进行鉴定识别训练时可能面临训练集不够多，导致训练准确度不够高，这是为保证古生物学这门学科带有历史真实性不可避免的挑战。
同时如果模型参数过大，再利用ai大模型对古文物进行复原是可能会出现幻觉现象，从而影响复原的可靠性。
由于古生物学中的人工智能起步较晚，与主流研究在数据集规模上存在大约20年的差距,在算法上存在大约10年的差距，技术上面临巨大挑战。
数据模态数量的快速增长和复杂性使得数据处理变得繁琐且不一致，同时也缺乏明确的基准来评估数据收集和生成以及不同方法在类似任务上的性能。
三 如何利用AI技术助力该学科的传承、创新与社会应用
目前的AI技术在古生物学中主要是应用在自动化过程的改进，古生物学家希望通过构建更大规模的训练数据集与移植前沿算法，获得更高效的古生物学的人工智能模型。分类任务，图像识别、图像分割、预测，定量化犯法、数据驱动的研究模式，生命科学与地球科学共同的趋势。
研究团队需要对数据集进行准备、标注、模型训练，有针对性地研发适合于画师图像分类的算法，用于所训练的模型，实现提高识别准确率的目的（主要还是提高自动化识别化石高分辨结构的一种方式）；对于破坏结构的岩石的3D结构的恢复，计算机断层扫描。
古生物图像的计算分析即计算机视觉分析，可用于动植物研究、微生物进化和特定时期生物栖息环境的模拟，生物进化的研究不仅仅只是生物分子方面的分子钟。
另外AI可以为古生物学研究赋能，创造更多的社会应用价值，解决一些经济活动领域的问题，比如石油勘探，为确定勘探点的潜力，并最大限度的减少采油过程中的相关费用。
传承与创新的意义，现在的古生物学家只需要对处理结果进行评估，对提取得到的微化石进行分类。",0.008094009313953947,0.3181818181818182,0.20930232558139533,0.3181818181818182,0.11145978519945536,0.6084638947725035,0.1882048100233078,0.23429951690821257,0.08300589390962676
152006,7,1507,0,3.0,19988.0,6.0,6.0,3.0,5.0,6.0,5.0,4.333333333333333,4.333333333333333,4.0,4.0,4.666666667,5.0,5.0,4.060185185,4.361111111,4.166666667,4.0,4.4,3.4,4.1,0.25,4.6,4.666666667,4.0,4.8,4.333333333,4.0,4.5,4.0,3.5,3.8,3.75,4.4,4.8,19,15,22,24,7,D,0,0,0,0,1,1,9,9,4,5,5,4,4,5,5,5,5,5,5,3,5,5,4,5,5,5,5,3,5,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,1,5,5,5,5,3,2,5,5,5,4,1,5,6,9.0,9,4.5,5.0,4.6,4.8,5.0,5.0,4.5,5.0,5.0,4.0,5.0,3.5,4,1,5,6,6.0,6.5,6.0,5.5,6.5,25,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,0,2,1,1,0,1,6,4,4,4,4,5,4,5,5,5,5,4,3,3,5,4,9,1,1,1,1,1,1,1,1,1,2,1,1,1,1,0,2,0,2,1,1,7,4,4,4,4,5,5,4,5,5,5,4,3,4,5,4,0.681818182,6.1,2.9,6.9855,2.0145,6.827,7.805,6.738,6.331,7.225,2.0145,刘顶,1.0,"AI对历史的创新推动
AI续写历史残本（思维链）
AI总结各历史文献，按需输出（举一反三、多模态学习）
AI解码文字，还原历史场景（多模态学习、反思能力）
AI结合各学科，如天文，地质活动等，协助历史考证，事件时空定位（多模态学习）
AI转换历史形式，协助历史传播（举一反三、反思能力）
AI生成历史游戏，助力用户沉浸式体验（多模态学习）
挑战：在利用AI进行历史研究的过程中，AI生成内容会存在幻觉问题。",7.803770975975005e-12,0.140625,0.12698412698412698,0.140625,0.01076555023923445,0.22880987357508092,0.0823269709944725,0.3170731707317073,0.014064190407500954
152009,8,1508,0,5.0,19990.0,6.0,6.0,2.333333333333333,3.333333333333333,5.0,4.0,5.0,3.0,3.333333333333333,5.0,5.0,3.666666667,5.0,3.959259259,3.755555556,3.533333333,3.2,3.9,4.6,4.4,0.5,3.2,4.0,4.666666667,3.2,3.666666667,4.0,3.0,4.0,4.5,3.4,3.75,3.4,3.6,17,15,17,18,8,D,0,0,0,0,1,1,8,8,4,3,4,4,4,3,4,3,4,4,4,4,4,4,4,4,3,4,3,3,4,3,4,4,3,3,3,4,3,4,4,4,3,4,4,4,4,3,4,4,3,4,3,4,4,4,2,3,4,4,2,2,4,3,3,4,4,4,4,1,3,2,8.0,8,3.666666667,3.8,3.4,3.4,3.8,3.8,3.833333333,3.5,3.75,3.0,3.666666667,2.75,4,1,3,2,5.5,5.0,5.5,5.5,5.5,25,0,7.0,1,1,1,1,1,1,1,1,1,2,1,1,1,1,0,1,1,1,0,1,1,1,1,1,5,4,5,3,3,4,4,3,4,4,3,2,2,3,4,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,8,5,4,5,5,3,4,4,4,4,2,2,2,2,3,4,0.818181818,5.4,2.6,6.2855,1.7145,6.327,6.305,6.238,6.331,6.225,1.7145,刘若文,2.0,"学科：考古学
1.文物发掘后，检测与鉴定可用人工智能，扫描文物鉴定年代
2.博物馆可以设置VR全景参观
3.未来的人也可用人工智能亲身体验考古过程，提高关注度
4.考古教学过程也可以用人工智能对学生进行教学实验，模拟实验环境。
5.文物保护的信息真假或者有些保密内容需要对人工智能做一些规范，避免虚假信息流出
6.利用人工智能将所有文物信息整合后可与其他学科联系，更直观的表现出过去与现在方法的同异，也可以更好的在教学和科普上使用。
7.利用AI识图分辩文物真假。",1.5726537224612877e-09,0.13043478260869565,0.06666666666666667,0.13043478260869565,0.01753073919660293,0.3740436749711896,0.19471248984336853,0.3925925925925926,0.020643112346169112
152010,8,1508,0,4.0,19990.0,4.0,4.0,1.0,3.0,4.666666666666667,3.333333333333333,1.0,3.0,3.0,4.0,3.666666667,4.0,5.0,4.418518519,4.511111111,4.066666667,3.4,4.2,3.8,4.7,0.0,4.8,4.333333333,5.0,4.2,3.333333333,4.0,4.5,4.666666667,4.75,3.4,3.5,3.8,4.0,17,14,19,20,8,D,0,0,0,0,1,1,8,7,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,4,4,4,4,3,4,4,4,3,4,4,4,3,3,4,7.375,7,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,4.0,4.0,3.75,4,3,3,4,5.0,3.0,4.0,5.5,5.5,23,0,7.0,1,1,1,1,1,1,1,2,0,2,1,1,0,1,0,1,1,1,1,1,1,1,0,2,6,4,4,4,4,3,3,5,4,4,4,4,3,3,3,3,8,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,1,2,1,1,9,5,5,5,5,4,4,5,5,4,5,5,2,3,3,3,0.772727273,4.6,3.4,5.4855,2.5145,5.827,4.305,4.738,6.331,6.225,2.5145,刘瑜瑾,1.0,"领域：生物医药方面，AI的不当使用可能会违背生命伦理，例如进行非法基因编辑和人体实验；或者AI可能设计出不完善的药物，流通到市场造成安全和经济的双重风险。
预防和监测手段：
1、加强伦理审查：建立严格的伦理审查机制，确保AI在医学领域的应用符合伦理标准。
2、技术监管：开发专门的技术监管工具，实时监控AI在生物医学领域的活动，及时发现异常。开发更智能的网络防御系统，利用AI技术进行实时监控和预警。
3、法律法规：完善相关法律法规，明确AI在生物医学领域的应用边界和责任主体。
4、跨领域合作：加强不同领域间的技术合作，形成综合防御体系。
5、教育与培训：提升相关从业人员的AI安全意识和应对能力。
我们可以给AI设计“疫苗”和“特效药”。疫苗就是防范于未然，构建坚固的AI免疫系统，这个系统需要具备天然的防范能力和不断学习的能力，对于有些没能第一次阻止的有害数据，AI需要学习之后加以“记忆”和巩固。而特效药则是在AI已经受到攻击或者做出错误决策时，能及时纠正和监管。",0.0006756966125458662,0.25210084033613445,0.2222222222222222,0.25210084033613445,0.08099186327783421,0.4779096904662562,0.4459541440010071,0.75,0.09239130434782605
152011,8,1508,0,3.0,19991.0,3.0,3.0,2.0,1.6666666666666667,4.333333333333333,3.6666666666666665,5.666666666666667,3.0,3.0,3.333333333,4.333333333,4.333333333,4.333333333,4.396296296,4.377777778,4.266666667,3.6,3.8,3.6,4.6,0.375,4.0,4.333333333,4.0,4.2,4.0,4.0,5.0,4.666666667,5.0,4.6,4.25,3.6,4.6,23,17,18,23,8,D,0,0,0,0,1,1,5,3,3,4,5,4,4,5,4,4,3,4,4,4,5,5,4,3,4,4,4,3,4,5,4,4,4,5,4,4,5,4,5,5,4,4,4,5,5,4,4,3,4,4,5,4,4,5,4,4,3,4,3,3,4,3,4,4,4,4,4,2,3,3,3.75,3,4.166666667,3.8,4.0,4.2,4.6,4.4,4.166666667,3.75,4.25,4.333333333,4.0,3.0,4,2,3,3,5.5,6.0,5.0,5.5,5.5,23,1,6.0,0,1,1,1,1,1,1,2,0,2,1,1,0,1,0,2,0,1,1,2,0,2,1,2,5,5,3,4,4,4,4,5,4,5,4,3,4,3,3,3,6,0,1,1,1,0,2,1,1,1,1,1,1,1,1,0,2,0,1,1,1,6,5,3,4,4,4,5,4,4,4,5,3,4,3,3,3,0.545454545,5.5,-0.5,6.3855,-1.3855,6.327,7.305,5.738,6.331,6.225,1.3855,刘喆,3.0,我认为考古学是一个冷门绝学，能够借助AI使其焕发新的活力，原因是目前考古学与AI结合可能较少，时间和客观条件的限制使其科学研究和成果转化受到一定限制，公众也了解很少。具体应用方面，比如说通过AI复原文物的发掘现场，让参观者能够化身考古学家，亲身体验文物发现和保护的全过程，用AI复原一些已经遭到损坏的文物，用vr技术使其更加生动，使其重新展现给大众。用AI整合之前的各类信息，利用举一反三能力，简要判断目标文物的年代等，以历史课为主，教师可以在课堂上开展跨学科教学，在博物馆给公众作科普讲解，撰写针对不同年龄人群的讲解词等。,4.2595676700396365e-07,0.11764705882352942,0.07228915662650603,0.11764705882352942,0.020644604671226967,0.4720276214127799,0.1359921097755432,0.32679738562091504,0.027995520716685318
152015,10,1510,0,2.0,19993.0,4.0,5.0,3.0,3.0,4.333333333333333,2.333333333333333,4.0,2.333333333333333,3.333333333333333,3.666666667,3.666666667,4.0,3.666666667,2.608333333,3.65,2.9,2.4,2.5,3.6,3.8,0.5,3.4,3.333333333,3.666666667,3.6,3.0,3.666666667,4.5,4.0,4.5,3.4,3.75,3.4,3.6,17,15,17,18,10,D,0,0,0,0,0,1,6,6,3,4,3,4,4,4,4,3,3,3,3,4,4,3,4,4,3,4,4,3,3,4,4,3,2,2,3,5,5,3,4,4,4,4,3,3,5,2,5,3,3,3,3,3,3,5,4,5,2,4,1,3,4,2,4,4,3,4,4,3,2,5,6.0,6,3.666666667,3.2,3.6,2.8,4.2,3.8,3.666666667,3.25,3.0,4.333333333,4.333333333,2.0,4,3,2,5,7.5,7.0,6.5,7.0,6.5,19,1,6.0,1,2,1,1,1,1,1,1,0,2,1,1,0,1,0,1,1,1,1,1,1,2,1,1,6,4,3,4,3,3,3,4,4,3,3,4,3,3,3,4,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,7,4,3,4,3,4,3,4,3,3,3,4,2,2,2,3,0.772727273,6.9,-0.9,7.7855,-1.7855,8.327,8.305,7.238,7.831,7.225,1.7855,乔灿宇,2.0,"古文字学科在没有通用人工智能的情况下，入门时间长，对于个人而言需要阅读很多的书。人工智能可以快速阅读大量数据，从而加快古文字翻译。其二，人工智能能更好的找到古文字之间的关联，而普通人会有遗忘现象。同时，人工智能的举一反三，思维链可以很好帮助古文字翻译。但挑战就是，AI仍然有幻觉现象，对于不懂的古文字可能进行胡乱解释。如果资料库少或不准确，会影响AI的翻译。
通过AI的多模态学习，它也能将古文字翻译成趣味性的视频图像，让人更生动了解古文字的起源，了解古文字的魅力。同时建立资料库后，能帮学者更好的检索信息，有利于古文字的学习和探索。总之，使用通用AI能有效帮助古文字传承发展，提升文化自信。",2.5852927884481365e-11,0.048192771084337345,0.03658536585365854,0.048192771084337345,0.016423841662868175,0.3677882755889793,0.17981693148612976,0.43859649122807015,0.02028123309897245
152016,10,1510,0,10.0,19994.0,4.0,4.0,2.6666666666666665,3.333333333333333,4.0,4.666666666666667,5.0,4.0,2.6666666666666665,4.666666667,5.0,4.666666667,5.0,4.230555556,4.383333333,4.3,3.8,4.5,4.1,4.7,0.375,4.4,4.0,3.333333333,4.0,3.666666667,3.666666667,4.0,4.333333333,4.25,2.4,3.0,4.4,3.4,12,12,22,17,10,D,0,0,0,0,0,1,8,8,4,4,5,4,4,5,4,4,4,4,4,4,5,4,5,4,5,4,5,4,4,4,4,5,5,4,4,4,4,5,4,5,4,4,5,4,4,4,5,4,4,5,5,4,4,4,4,5,3,4,4,2,4,4,4,5,4,4,4,4,4,4,8.0,8,4.333333333,4.0,4.2,4.4,4.4,4.2,4.5,4.25,4.5,4.0,4.333333333,3.25,4,4,4,4,7.5,7.5,6.5,7.0,7.0,23,1,8.0,0,1,1,1,1,1,1,1,0,2,1,1,0,2,0,1,1,2,1,1,1,1,1,1,8,4,3,4,4,4,3,4,4,4,4,4,2,3,2,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,1,8,4,2,4,4,4,4,5,4,4,5,4,2,4,4,4,0.727272727,7.1,0.9,7.9855,0.0145,8.327,8.805,7.238,7.831,7.725,0.0145,卿康,1.0,"冷门绝学：古文字注释
在人工智能时代可能焕发新生命力的可能原因：1、人工智能相比人类阅读范围阅读能力更强，缩短学习入门周期；2、人工智能更可能理解抓住众多古文字之间的相互关联，助力陌生古文字的解释；3人工智能的思维链、举一反三、多模态学习、反思能力都能有助于古文字注释的发展
其在未来发展中面临的可能挑战：人工智能技术可能存在幻觉问题，导致胡乱解释古文字；人工智能需要大量精确的数据来训练，但是古文字的数据来源不是规范统一，数据的准确与否会影响人工智能的效果
利用AI技术助力该学科的传承、创新与社会应用方法：可以利用人工智能的多模态技术解释和宣传古文字，展现传统文化的魅力，也可以帮助新入门者快速了解上手这一行业，帮助有经验的研究者迅速检索相关资料验证猜想；社会应用上可以帮助古文字的古今对比，展示古文字文化的源远流长，提高文化自信，助力中华民族伟大复兴。",2.3881071604301523e-13,0.06557377049180328,0.05,0.06557377049180328,0.012716771585160243,0.3046772109591718,0.13246332108974457,0.36231884057971014,0.01425313568985176
152017,10,1510,0,7.0,19994.0,3.0,3.0,2.0,4.666666666666667,6.0,3.0,4.0,2.0,2.333333333333333,5.0,5.333333333,4.0,5.333333333,2.764814815,3.588888889,3.533333333,3.2,3.0,4.1,4.1,0.375,4.0,4.0,3.333333333,3.8,3.666666667,3.666666667,3.5,3.333333333,4.0,2.8,3.75,3.2,3.6,14,15,16,18,10,D,0,0,0,0,0,1,5,4,4,4,4,4,4,4,3,3,3,4,4,4,4,5,4,3,3,4,2,2,4,4,3,3,3,2,2,4,4,4,4,4,3,3,4,2,4,3,3,3,3,3,3,3,3,3,2,3,3,3,2,3,3,3,2,2,3,4,4,2,2,2,4.375,4,4.0,3.4,3.2,2.6,4.0,3.2,3.833333333,3.0,3.0,2.333333333,3.0,2.75,4,2,2,2,7.5,7.0,6.5,7.5,7.5,22,0,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,0,1,1,1,1,1,7,4,3,4,4,3,4,4,4,4,3,4,3,2,3,2,8,0,1,1,1,0,1,1,1,0,2,1,1,1,1,1,2,0,1,1,1,6,3,3,4,4,4,4,4,4,4,4,4,3,2,2,2,0.681818182,7.2,-2.2,8.0855,-3.0855,8.327,8.305,7.238,8.331,8.225,3.0855,苏欣欣,3.0,"任务二：【冷门绝学】
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
古文字考据类对于考究文字的起源和文化文明的发展具有重要意义，但传统学习方式下学习周期长，入门时间长，文献数据因为时间原因存在缺失不足的现象，兼之人脑的记忆容量有限，可能很多时候并不能很有效的帮助，也制约了该学科的发展。利用AI能够有效帮助该学科焕发生命力，其原因、挑战和应用如下：
1.原因：缩短学习周期，能够帮助总结文献、资料的内容，节省时间；同时辅助建立不同资料的数据链接，利用大数据高效而精确的定位古文字所出现的地方并建立网络。
2.挑战：（1）只能辅助解释，而不能够产生全新的知识；（2）可能会出现幻觉现象3.数据来源和精确度的要求高，而本学科自身的数据精确度不够；（3）AI的解释更多是已有研究的整理，精准的释义需要人来确认。
3.创新和社会应用：（1）数字化赋能古文字学的宣传，利用AI制作相关的文字演变视频或者是数字化古文字，都能达到较好的宣传效果。（2）利用AI的思维链、举一反三等帮助建立古文字数据库，实时动态调整文字的来源、释义，甚至沟通不同区域、国家，探寻文字的起源和意思。（3）模拟古文字释义，借助AI大模型的概率模拟，试图破解古文字的奥秘。",9.067931447292255e-06,0.18823529411764706,0.15476190476190477,0.18823529411764706,0.03302025242148517,0.3596431210615333,0.19609588384628296,0.31827956989247314,0.030950790469828515
152018,11,1511,0,5.0,19995.0,2.0,2.0,5.666666666666667,4.0,2.333333333333333,5.0,3.0,3.6666666666666665,5.0,4.0,5.666666667,5.0,5.666666667,3.873148148,3.238888889,3.433333333,3.6,4.9,5.1,5.9,0.125,3.4,2.0,2.333333333,4.0,4.666666667,3.666666667,4.0,4.0,4.5,3.6,4.5,3.6,4.2,18,18,18,21,11,D,0,0,0,0,0,1,6,7,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,3,3,3,3,3,3,4,3,4,4,3,4,4,3,3,3,4,4,3,3,3,3,3,3,3,3,4,3,3,2,3,4,3,3,3,3,3,3,4,4,3,2,2,6.625,7,4.0,4.0,3.2,3.2,3.8,3.4,3.833333333,3.0,3.0,3.333333333,3.0,3.0,4,3,2,2,6.5,5.0,5.5,6.5,6.5,20,0,9.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,2,0,2,1,2,1,1,1,2,9,4,3,4,5,4,5,5,5,5,2,3,2,5,5,5,6,0,1,1,2,0,1,0,2,0,2,1,1,1,1,0,1,0,2,0,2,6,3,2,2,1,2,3,3,4,4,2,4,2,4,4,3,0.545454545,6.0,0.0,6.8855,-0.8855,7.327,6.305,6.238,7.331,7.225,0.8855,王晨希,3.0,"冷门学科：文物修复，比如壁画、器物、书画等的修复
可能焕发新生命力的可能原因：
随着经济社会的不断发展，人们精神文化生活越来越丰富，大家更热衷于获得精神层面的富足，更加关注中国传统文化
2、利用AI修复文物可以更清楚地展示细节，如图案、文字等，可以更好地展示文物的原貌
3、相较于人工修复能更好地节省人力、预测更多信息，收集更多的历史数据
4、AI可以根据文物的损坏情况，结合历史资料和修复师的经验，设计出更合理的修复方案。例如，通过机器学习算法，AI可以从大量修复案例中学习，提出最优的修复材料和工艺
5、将AI与高科技修复技术进行结合，可以检测出文物的材料、成分和结构等信息
未来发展中面临的可能挑战：修复过程中可能会出现预测错误，并非完全遵循历史原貌，且数字化修复不一定会被大众认可，传统修复手艺可能会失传",9.485377007282484e-06,0.22535211267605632,0.14492753623188404,0.22535211267605632,0.03509082330738382,0.45734968311575064,0.14661455154418945,0.3333333333333333,0.03817204301075272
152019,11,1511,0,6.0,10979.0,1.0,1.0,2.333333333333333,4.666666666666667,3.6666666666666665,4.0,4.333333333333333,4.0,4.666666666666667,5.0,4.666666667,4.333333333,5.0,3.784259259,3.705555556,3.233333333,2.4,4.6,3.1,4.5,0.5,3.6,3.333333333,3.333333333,4.4,4.0,4.0,4.0,4.666666667,4.5,3.8,3.75,4.2,4.4,19,15,21,22,11,D,0,0,0,0,0,1,9,8,5,5,4,4,5,4,4,5,5,4,4,5,5,5,4,5,5,5,5,4,5,5,5,5,5,4,5,5,5,4,4,5,4,5,4,4,5,4,4,3,4,4,4,4,4,3,3,4,4,4,4,3,4,4,3,4,3,4,4,3,4,5,8.375,8,4.5,4.4,4.8,4.8,4.6,4.4,4.833333333,3.75,4.0,3.0,4.0,3.75,4,3,4,5,6.5,5.0,5.5,6.5,6.5,21,1,7.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,0,1,1,1,1,1,8,5,3,4,4,4,4,5,4,5,4,4,4,5,5,4,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,1,1,6,4,2,4,3,3,4,4,4,3,4,3,2,4,4,4,0.818181818,6.0,3.0,6.8855,2.1145,7.327,6.305,6.238,7.331,7.225,2.1145,王连硕,2.0,"选择学科
文物修复，比如壁画，文物，国画修复等
分析热门原因
旅游业发展旺盛，人们精神文化生活越来越丰富，大家更热衷于精神层面的旅游，比如了解历史文化
利用AI修复文物可以更清楚地展示细节，如图案、文字等，可以更好地展示文物的原貌
，相较于人工修复能更好地节省人力、预测更多信息，收集更多的历史数据
AI可以根据文物的损坏情况，结合历史资料和修复师的经验，设计出更合理的修复方案。例如，通过机器学习算法，AI可以从大量修复案例中学习，提出最优的修复材料和工艺
将AI与高科技修复技术进行结合，可以检测出文物的材料、成分和结构等信息
三、面临挑战
修复过程中可能会出现预测错误，并非完全遵循历史原貌，且数字化修复不一定会被大众认可，传统修复手艺可能会失传；在AI修复过程中需要掌握传统修复方式与AI修复的人才，对人才的培养也提出了要求。",7.209567678656044e-06,0.14285714285714288,0.12195121951219512,0.14285714285714288,0.033257112975585076,0.31028353444456847,0.11915338784456253,0.3212669683257919,0.035588972431077726
152020,11,1511,0,5.0,10980.0,4.0,4.0,2.0,3.0,3.333333333333333,4.0,4.0,2.6666666666666665,3.333333333333333,4.333333333,3.666666667,3.0,5.333333333,3.763888889,3.583333333,3.5,4.0,3.7,3.9,5.1,0.0,3.8,4.0,4.0,4.0,3.666666667,3.333333333,3.5,3.333333333,4.0,2.6,3.5,3.2,3.0,13,14,16,15,11,D,0,0,0,0,0,1,7,6,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,3,4,3,3,4,3,3,3,4,4,4,4,4,3,3,3,3,4,3,3,3,3,3,3,3,3,2,2,4,4,4,3,3,4,3,3,3,3,3,4,3,3,4,6.375,6,3.666666667,4.0,3.6,3.2,4.0,3.2,3.833333333,3.0,3.0,2.333333333,4.0,3.25,4,3,3,4,6.5,5.0,5.5,6.5,6.5,26,0,6.0,1,1,1,1,1,1,1,2,1,2,0,1,0,1,1,1,1,2,1,2,0,2,1,2,5,4,3,3,4,3,4,4,4,4,4,4,3,4,3,3,9,0,2,1,1,0,2,1,1,1,1,1,1,1,1,1,2,0,2,1,1,7,5,3,4,4,4,4,4,4,4,4,3,3,3,2,3,0.727272727,6.0,1.0,6.8855,0.1145,7.327,6.305,6.238,7.331,7.225,0.1145,王钰,1.0,"选择学科
文物修复，比如壁画，文物，国画修复，数字化展示
分析热门原因
旅游业发展旺盛，人们精神文化生活越来越丰富，大家更热衷于精神层面的旅游，比如了解历史文化
利用AI修复文物可以更清楚地展示细节，如图案、文字等，可以更好地展示文物的原貌
，相较于人工修复能更好地节省人力、预测更多信息，收集更多的历史数据。
AI可以根据文物的损坏情况，结合历史资料和修复师的经验，设计出更合理的修复方案。例如，通过机器学习算法，AI可以从大量修复案例中学习，提出最优的修复材料和工艺。
将AI与高科技修复技术进行结合，可以检测出文物的材料、成分和结构等信息。
三、面临挑战
修复过程中可能会出现一些失误，无法完全遵循历史原貌，且数字化修复不一定会被大众认可，很多人去博物馆更想要看真的文物，可能AI参与大家反而更不喜欢。",3.494088118659202e-14,0.043668122270742356,0.035242290748898675,0.043668122270742356,0.014287675848104765,0.33062826984100635,0.1730705350637436,0.5070422535211268,0.016700170094325006
152021,12,1512,0,4.0,10980.0,5.0,6.0,5.333333333333333,5.333333333333333,3.333333333333333,3.0,5.0,3.6666666666666665,3.333333333333333,6.0,5.666666667,4.666666667,4.0,4.798148148,3.788888889,4.733333333,3.4,4.2,4.5,4.7,0.375,4.8,5.0,5.0,4.4,3.0,4.666666667,4.5,4.333333333,4.25,4.4,3.75,3.4,4.8,22,15,17,24,12,D,0,0,0,0,0,1,7,5,5,4,4,5,5,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,3,5,5,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,3,5,5,5,5,5,5,5,5,5,5,5,5,2,3,3,5.75,5,4.666666667,4.8,5.0,4.2,4.6,5.0,5.0,5.0,5.0,4.0,5.0,5.0,5,2,3,3,6.5,5.5,5.5,5.5,6.0,23,1,10.0,1,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,0,1,0,1,1,1,1,1,7,5,4,5,4,4,1,3,5,4,5,5,2,3,3,4,10,1,1,1,1,0,1,1,1,0,1,1,1,1,1,0,1,0,1,1,1,8,5,5,5,5,5,5,5,5,4,5,5,1,4,4,3,0.636363636,5.8,1.2,6.6855,0.3145,7.327,6.805,6.238,6.331,6.725,0.3145,文致远,2.0,"任务二
选择的冷门学科：古文字识别
选择原因：
具有一定的研究基础
可以对象形字的书写和演变进行分析
驯化方式：
第一阶段：古文字大部分是象形字体，部分字体就是简易图形的变形和组合，将已有的写作思路进行总结作为数据提供给ai，让ai进行无监督学习。
第二阶段：将认为已定义的古文字提问ai检测准确率，进行人工监督学习。
利用方式：
1.使用ai进行模糊文字的识别与残缺文字记录的修复
2.使用卷积技术对文字进行分离，对不同时间阶段文字进行演推，从古至今分析文字的变化，",1.5808406332225864e-13,0.10084033613445377,0.08547008547008547,0.10084033613445377,0.012577030077635084,0.30805222251892755,0.14284548163414001,0.4744525547445255,0.017186673717609735
152022,12,1512,0,6.0,10981.0,6.0,7.0,1.6666666666666667,4.333333333333333,3.6666666666666665,4.333333333333333,4.666666666666667,3.6666666666666665,3.6666666666666665,5.666666667,4.666666667,4.666666667,5.333333333,4.547222222,4.283333333,3.7,2.2,5.3,3.5,5.0,0.0,4.2,4.666666667,4.666666667,4.8,4.0,4.0,3.5,4.333333333,2.25,2.4,4.0,4.2,3.2,12,16,21,16,12,D,0,0,0,0,0,1,8,8,3,4,4,5,5,5,3,2,3,1,4,4,5,4,3,5,2,4,4,4,5,2,3,4,4,4,4,4,5,2,4,4,4,4,4,4,4,2,2,3,4,3,3,3,3,3,2,3,4,3,4,3,3,5,2,3,4,3,3,1,4,1,8.0,8,4.333333333,2.6,3.8,3.8,3.8,4.0,3.833333333,2.75,3.0,2.333333333,3.0,4.0,3,1,4,1,8.0,8.0,7.5,8.0,7.5,23,0,8.0,1,2,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,2,1,1,0,1,6,4,5,3,4,4,4,5,4,5,5,5,3,4,3,4,9,1,1,1,1,0,1,1,1,0,1,1,1,1,2,0,2,1,1,1,1,8,5,4,5,4,5,5,5,5,4,4,3,2,3,4,4,0.727272727,7.8,0.2,8.6855,-0.6855,8.827,9.305,8.238,8.831,8.225,0.6855,郗家禾,3.0,"根据我们讨论的笔记，用AI润色优化如下：
一、选择古文字识别学科的依据
学科基础扎实：该领域具有深厚的研究积累和严谨的学术传统，为AI应用提供了可靠的知识基础。
数据特征独特：古文字有一定的象形特征和演变规律
跨学科融合：结合考古学、历史学、语言学等多个领域
二、人工智能助力古文字研究的创新路径
基于深度学习的文字识别 
运用图神经网络进行文字结构分析和部件识别
利用无监督学习发现字形规律和演变模式
通过相似度算法构建古文字关联网络
智能辅助考古实践 
对模糊不清的出土文字进行AI增强识别
建立多模态知识图谱，整合文字、图像与考古信息
实现文物信息的快速数字化与系统分析
文字演变研究的突破 
构建时序演化模型，追踪文字发展脉络
利用AI推演不同时期文字的过渡形态
揭示汉字系统的历史发展规律
三、面临的挑战与应对策略
数据质量问题 
出土文物残缺不全，增加识别难度
需要建立高质量的训练数据集
开发针对性的数据增强技术
准确性验证 
建立专家审核机制，确保AI识别结果的可靠性
开发可解释性模块，提升模型透明度
构建评估标准体系
学科传承与创新 
平衡传统研究方法与AI技术的结合
培养跨学科人才，提升数字素养
促进国际合作，共享研究资源",3.2833615244816032e-09,0.09375,0.07936507936507936,0.09375,0.01983813098064979,0.45473458945886247,0.13085465133190155,0.38175675675675674,0.022248708780293946
152023,12,1512,0,3.0,10981.0,2.0,2.0,6.0,4.0,6.0,2.333333333333333,3.0,1.6666666666666667,2.0,6.0,6.0,2.666666667,6.0,3.909259259,3.455555556,3.733333333,1.4,5.5,3.6,5.1,0.0,4.8,5.0,4.0,4.8,3.666666667,2.666666667,3.5,4.333333333,4.75,4.0,4.75,3.2,3.8,20,19,16,19,12,D,0,0,0,0,0,1,5,6,5,4,5,5,4,5,4,5,4,3,5,5,5,5,3,5,5,3,5,3,5,3,4,3,4,3,4,5,3,4,5,3,4,4,3,5,3,4,3,4,4,4,4,3,5,3,4,3,4,3,4,4,4,4,4,3,4,5,3,3,3,2,5.625,6,4.666666667,4.2,3.8,3.6,4.0,3.8,4.666666667,3.75,4.0,3.666666667,3.333333333,4.0,3,3,3,2,9.5,9.0,8.5,9.0,9.5,26,0,4.0,0,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,0,2,1,1,0,1,0,2,5,3,2,3,4,3,4,5,4,5,5,5,5,2,2,2,4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,5,4,3,5,5,5,5,4,5,5,5,5,3,2,2,1,0.772727273,9.1,-4.1,9.9855,-4.9855,10.327,10.305,9.238,9.831,10.225,4.9855,闫赵桐,1.0,"古文字识别学在人工智能时代新的机遇与潜力
一、焕发新生命力的原因
1. 海量数据处理：通用人工智能具备强大的数据处理能力。古文字学研究涉及大量的古代文献、文物资料等，传统人工整理和分析耗时费力。AI能快速扫描、识别和整理这些资料，例如对甲骨文、金文等古文字的拓片进行数字化处理，通过图像识别技术提取文字信息，建立大型古文字数据库，为研究提供全面的数据支持。
2. 跨领域知识整合：多模态学习使AI能够整合不同类型的信息。古文字学研究并非孤立，它与历史学、考古学、语言学等多学科交叉。AI可综合分析来自不同领域的数据，如结合历史文献记载与考古发现的文物上的古文字，推断文字背后的社会制度、文化习俗等，挖掘出更多有价值的信息。
3. 推理与思维链辅助：AI的思维链能力有助于解决古文字学中的复杂问题。面对古文字的释读难题，AI可以根据已知的文字演变规律、语法结构以及上下文语境，进行逻辑推理和分析，提出合理的释读假设，辅助学者更快地突破研究瓶颈 。
 二、AI的应用方法：
1. 无监督学习：卷积技术分离文字与不同部位 
原理：在处理古文字图像（如甲骨文的龟甲、金文的青铜器铭文拓片）时，卷积神经网络（CNN）能够自动学习图像的特征。通过卷积层的卷积核在图像上滑动，提取局部特征，如笔画的形状、走向等。无监督学习方式下，模型不需要预先标记文字及其部位，而是通过自组织映射等算法，依据图像特征的相似性，将文字区域与背景、装饰图案等不同部位分离出来。
应用于古文字学：这有助于从复杂的文物图像中快速准确地提取古文字，为后续研究提供清晰的文字素材。例如，对于刻有大量铭文的青铜器图像，能自动分离出文字部分，避免人工筛选的繁琐与误差，极大提高资料整理效率，助力古文字学资料的数字化传承。 
2. 相似性检测，文字举一反三 
原理：利用深度学习中的度量学习方法，如孪生网络（Siamese Network）。将古文字图像输入孪生网络的两个分支，经过卷积层、池化层等处理后，得到文字的特征向量。通过计算不同特征向量之间的距离（如余弦距离），判断文字之间的相似性。当发现相似文字时，基于已知文字的解读、用法等信息，进行“举一反三”的推理。例如，若已知某一常见甲骨文的含义和用法，当检测到与之相似的罕见甲骨文时，可推测其可能的含义和用法。
应用于古文字学：在古文字释读中，相似性检测可辅助学者发现文字之间潜在的联系。对于一些尚未释读的古文字，通过与已释读文字的相似性分析，为释读提供新线索，推动古文字学理论的创新。 
3. 人工监督学习：利用已有文字对AI进行训练
原理：将大量已释读、标注准确的古文字数据（包括文字图像、对应的现代文字释义、用法等）作为训练集，采用监督学习算法（如反向传播算法）训练神经网络模型。在训练过程中，模型根据输入的古文字图像预测其对应的释义、用法等，通过与真实标注的对比，计算损失函数，并调整模型参数，使预测结果不断接近真实值。
应用于古文字学：经过人工监督学习训练的AI模型，能够更准确地对新出现的古文字进行解读和分析。例如，在面对新出土文物上的古文字时，可借助训练好的模型快速给出初步的解读建议，提高研究效率，同时为古文字学知识在文物鉴定等社会应用场景中提供技术支持。 
4. AI进行模糊文字识别
原理：针对模糊不清的古文字图像，首先利用图像增强技术，如基于生成对抗网络（GAN）的超分辨率重建方法，对模糊图像进行清晰化处理。然后，将增强后的图像输入到训练好的文字识别模型（如基于CNN的光学字符识别OCR模型）中。模型通过学习大量清晰古文字图像的特征，对模糊文字进行特征匹配和识别，预测出最可能的文字内容。
应用于古文字学：许多出土文物上的古文字因年代久远、保存条件等问题模糊不清，AI的模糊文字识别能力可挽救这些珍贵信息，使更多模糊的古文字得以释读，丰富古文字学研究资料，推动古文字学的传承与发展。
5. 文字演变推演：分析从古至今文字的变化
原理：利用循环神经网络（RNN）及其变体（如长短时记忆网络LSTM）。将不同时期的古文字按照时间顺序作为序列数据输入模型。RNN能够处理序列数据，通过隐藏层保存和传递时间序列中的信息，捕捉文字在不同时期的变化特征。例如，分析不同历史时期汉字的笔画、结构变化规律，从甲骨文到金文、篆书、隶书、楷书等字体的演变。
应用于古文字学：通过AI推演文字演变，可直观展示文字发展脉络，帮助学者深入理解文字演变背后的文化、社会因素。同时，以可视化的方式呈现给大众，开发科普产品，如古文字演变的动画演示，促进古文字学知识在社会中的传播应用，助力古文字学的社会推广与传承。
三、未来发展面临的挑战
1. 数据准确性与可靠性：古文字学数据的来源多样且复杂，存在很多模糊和错误信息。AI系统依赖高质量的数据进行训练，如果输入的数据不准确，可能导致错误的分析结果。如何确保AI所使用的古文字数据经过严格考证和筛选，是一大挑战。
2. 专业知识门槛：古文字学研究需要深厚的专业知识和对古代文化的深刻理解。AI虽然能处理数据，但缺乏对古文字背后文化内涵的直观感受和深入理解。培养既懂古文字学又熟悉AI技术的复合型人才难度较大，限制了AI在古文字学领域的深度应用。
3. 伦理和文化问题：在利用AI进行古文字研究时，可能涉及到对传统文化的解读和阐释权问题。AI生成的结果可能与传统学术观点产生冲突，如何在尊重传统文化和学术规范的基础上，合理运用AI技术，避免对文化的曲解和滥用，是需要谨慎对待的问题。
四、利用AI技术助力学科发展 
1. 传承方面：通过AI技术构建古文字数字化图书馆、虚拟博物馆等，将珍贵的古文字资料永久保存并以生动的形式展示给大众，提高公众对古文字学的认知和兴趣，吸引更多人投身于该领域的学习与研究。
2. 创新方面：借助AI的分析能力，挖掘古文字之间新的联系和演变规律，为古文字学理论创新提供支持。例如，利用机器学习算法分析不同时期、不同地域的古文字特点，发现以往未被注意到的文字发展脉络。
3. 社会应用方面：古文字学与文化创意产业结合，AI可辅助开发古文字相关的游戏、文创产品等。如利用多模态学习，开发能够识别用户书写古文字并进行实时讲解的应用程序，让大众在娱乐中学习古文字知识，促进古文字学的社会传播 。",5.692416483360351e-07,0.1423076923076923,0.12355212355212356,0.1423076923076923,0.024599995342012716,0.2622798190715099,0.19968263804912567,0.3210702341137124,0.024951102183221496
151012,13,1513,0,5.0,19004.0,5.0,6.0,4.666666666666667,2.6666666666666665,2.333333333333333,3.0,2.6666666666666665,2.0,3.0,5.0,4.0,4.0,5.666666667,3.636111111,3.816666667,3.9,2.4,5.1,3.6,4.1,0.25,3.6,4.666666667,4.333333333,4.2,3.333333333,4.333333333,4.0,5.0,4.25,2.2,3.75,2.0,3.0,11,15,10,15,13,D,0,0,0,1,0,1,3,2,5,5,5,5,5,5,4,3,3,3,3,4,4,4,3,5,4,4,4,5,5,3,4,4,4,4,4,4,4,2,4,4,4,4,4,4,4,4,3,2,3,3,3,4,3,2,2,2,3,2,3,2,2,2,2,1,2,3,2,2,2,3,2.375,2,5.0,3.2,4.2,4.0,3.6,4.0,4.0,3.0,3.25,2.0,2.0,2.5,2,2,2,3,6.5,6.5,5.5,6.0,6.0,22,0,7.0,1,2,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,8,4,5,4,3,3,4,5,3,4,4,5,2,3,3,3,6,1,2,1,1,0,2,1,1,1,2,1,1,1,1,1,2,1,2,1,1,5,3,5,5,4,5,5,3,4,3,4,4,1,2,2,2,0.909090909,6.1,-3.1,6.1,-3.1,6.5,6.5,5.5,6.0,6.0,3.1,杨冰,3.0,"AI在哪些领域带来安全风险
银行AI信息泄露，AI黑客
AI对于负面情感的攻击性回答
AI生成虚假新闻
如何预防监测风险
人工处理隐私数据
应对方案
研发出更加强大的AI去应对AI黑客，设立AI警察，将人类世界的部分规则迁移到AI世界
建立一个绝对准确的新闻网站，应用区块链技术",3.6436198775497506e-15,0.21428571428571425,0.1951219512195122,0.21428571428571425,0.009682327447092997,0.42189523289911823,0.05914827436208725,0.4,0.013327780091628494
151013,13,1513,0,5.0,19004.0,3.0,3.0,5.333333333333333,4.666666666666667,2.6666666666666665,5.0,3.6666666666666665,5.0,4.333333333333333,5.333333333,5.666666667,6.0,5.666666667,4.660185185,4.961111111,4.766666667,4.6,5.4,4.5,5.5,0.25,5.0,5.0,5.0,5.0,5.0,4.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,13,D,0,0,0,1,0,1,9,9,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,3,3,4,9.0,9,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,3,3,3,4,7.0,6.0,6.5,6.5,6.5,19,1,7.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,8,5,2,5,5,5,5,5,5,5,5,5,4,4,4,5,6,0,1,1,1,0,1,1,1,0,1,1,1,1,1,0,2,0,2,1,1,6,5,5,5,5,5,5,5,5,5,5,5,4,5,5,5,0.636363636,6.5,2.5,6.5,2.5,7.0,6.0,6.5,6.5,6.5,2.5,杨季予,2.0,"安全特工
AI恐怖分子可能进行的破坏：
智能家居被恐怖分子AI控制。
银行AI信息泄露。
AI恐怖分子进行聊天软件的情绪价值提供。
AI恐怖分子进行恐慌宣传
预防和检测风险：
银行AI信息泄露：
预防与检测：银行AI信息泄露的话可以使用人工来处理隐私数据，不使用AI。
应对：如果已经泄露就要求AI公司删除，进行抵御工作。
具体场景：例如银行公司通过AI来处理数据后，要进行员工培训，命令禁止这种操作，如果已经泄漏要求技术部门删除在AI中的数据
AI恐怖分子进行恐慌宣传
具体场景：AI恐怖分子通过AI软件散步虚假恐慌宣传，造成虚假的恐怖绑架案，进行直播等。
预防：禁止AI生成关于绑架等的图片或者视频
应对场景:相关技术部门拿出使用AI的证据进行辟谣。
智能家居被恐怖分子AI控制：
具体场景，家里的锁自动开门或者鱼缸自动放热水等。
预防：公司进行24小时检测。
应对场景：告知相关人员信息，报警后保护自身。
3.	AI恐怖分子进行聊天软件的情绪价值提供。
具体场景:在聊天软件上传播负面情绪价值
预防：监管聊天信息
应对：改为人工聊天",0.0003731554769319402,0.7450980392156863,0.6938775510204083,0.7058823529411764,0.04381869118597802,0.3632016296807721,0.12747961282730103,0.2835820895522388,0.048121292023731055
151014,13,1513,0,4.0,19005.0,3.0,3.0,4.666666666666667,4.333333333333333,5.333333333333333,4.333333333333333,1.6666666666666667,4.0,4.0,5.0,5.0,5.0,5.0,3.999074074,3.994444444,3.966666667,3.8,4.7,4.5,4.9,0.5,4.0,4.666666667,5.0,4.2,4.333333333,4.666666667,4.0,5.0,5.0,2.8,4.0,3.8,3.8,14,16,19,19,13,D,0,0,0,1,0,1,8,7,5,4,4,4,4,4,4,3,4,4,4,4,5,5,5,5,5,5,4,3,5,4,4,4,4,4,4,5,5,3,4,5,3,3,4,4,5,3,4,3,3,3,3,4,4,4,4,5,2,4,1,1,4,1,4,4,4,4,4,2,4,6,7.375,7,4.166666667,3.8,4.2,4.0,4.4,3.8,4.833333333,3.25,3.5,4.0,4.333333333,1.25,4,2,4,6,7.0,6.0,6.0,6.0,6.5,22,0,9.0,0,1,1,1,1,1,1,1,0,2,1,1,1,1,0,1,1,1,1,2,1,1,1,1,7,5,4,5,4,5,4,4,4,4,5,4,2,4,4,4,9,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,8,5,5,5,5,5,4,4,4,4,4,4,2,4,4,4,0.727272727,6.3,1.7,6.3,1.7,7.0,6.0,6.0,6.0,6.5,1.7,伊布迪莎,1.0,"主题：安全特工
风险：
智能家居被AI恐怖分子控制威胁人类
信息泄露 AI恐怖分子可以盗取信息模仿信息
AI聊天软件负性引导
AI发布虚假新闻引起恐慌
方案与解决方法：
针对第一点智能家居被控制，可以切断电源，避免AI恐怖分子的使用。针对第二点信息盗取和泄露，可以建立隐私信息库，人为加密，不让AI可以随意查取到。针对第三点聊天软件，可以设置人工客服检测，如果出现负性关键词立刻转人工切入，不让人继续与AI聊天。针对第四点，设置专门的权威新闻网实时涵盖所有新闻，遇到不确定的消息和新闻到这个网址上查找，如果有就是真的，如果没有无论是什么消息都不相信。
展望AI安全防御技术的未来发展：
AI安全防御技术肯定会越来越强大，因为AI攻击人类的途径和方式都有限，AI也无法跳脱电子设备和信息进行攻击，只要在源头上预防，AI恐怖分子就会束手无措。人们只要保持对AI的怀疑态度，不完全相信AI，明白AI存在许多安全隐患，那么AI安全防御技术就会越来越完善。",7.226567537748747e-06,0.34782608695652173,0.33333333333333337,0.34782608695652173,0.03576785508249683,0.4882734262769126,0.1435508131980896,0.34615384615384615,0.04210526315789476
151015,14,1514,0,4.0,19005.0,2.0,2.0,2.333333333333333,4.0,3.6666666666666665,4.666666666666667,4.0,4.333333333333333,4.0,4.666666667,4.333333333,5.0,5.0,3.760185185,3.561111111,3.366666667,3.2,4.5,3.4,4.5,0.25,3.8,4.666666667,4.666666667,5.0,4.666666667,4.333333333,3.5,4.333333333,4.5,2.4,3.75,3.6,3.0,12,15,18,15,14,D,0,0,0,1,0,1,7,6,4,3,5,5,4,4,5,3,5,4,4,4,5,3,5,2,4,5,4,4,3,3,4,4,4,3,5,4,5,4,4,5,4,4,4,3,4,5,3,4,4,3,3,4,3,5,4,4,3,4,4,4,5,2,4,4,5,5,4,3,4,4,6.375,6,4.166666667,4.2,3.8,4.0,4.4,3.8,3.833333333,4.0,3.25,4.333333333,4.333333333,3.25,4,3,4,4,7.5,7.0,6.0,7.0,7.0,20,1,6.0,0,1,1,1,1,1,1,1,1,1,1,2,1,2,0,1,1,2,0,2,0,1,0,2,5,4,4,5,5,5,4,5,5,5,5,5,4,4,4,4,8,1,1,1,1,0,2,1,1,1,2,1,1,1,1,0,2,0,1,1,1,7,5,4,5,5,5,4,3,4,4,3,5,2,4,4,5,0.636363636,6.9,0.1,6.9,0.1,7.5,7.0,6.0,7.0,7.0,0.1,张嘉轩,1.0,"“AI”恐怖分子，可能会利用化学科学的前沿技术，在已有的化学工业基础上，制造化学武器。比如从第二次世界大战使用化学武器的历史资料上，通过AI&机器学习的手段去预测，从而制备新型的、易得的化学武器。
可以通过人工智能AI的对齐、超级对齐等方法，对于危险化学品、MSDS名单上的化学品等，假如危险敏感提示词，从而在网络层面进行及时监控。
或者国家政府、或国际组织可以进行国际行的、透明公开的利用AI人工智能等手段进行研发化学武器。从而在这些研究的基础上，对于制造化学武器的化学品原料，进行严格监控，同时倡导促进各个国家达成化学武器共识。
对于AI安全防御技术的未来发展，国际组织与地球村的各个国家应该携手共进，积极推进各个国家对于AI安全的认识交流，相互吸取经验，建立并且完善因AI而引起的问题的相关的法律，达成全民共识。
同时倡导民众合理合法使用AI人工智能技术，对于AI的应用进行知识普及。",1.0183068044833533e-11,0.09944751381215469,0.0782122905027933,0.09944751381215469,0.013874395218669953,0.21847135950751245,0.1338348686695099,0.3581395348837209,0.016520060072945708
151016,14,1514,0,3.0,19005.0,2.0,2.0,4.0,3.0,5.0,3.6666666666666665,3.0,4.0,3.333333333333333,5.0,3.666666667,4.333333333,3.0,2.922222222,3.533333333,3.2,3.2,3.2,3.1,3.5,0.75,3.6,4.0,3.333333333,3.0,3.0,3.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,20,16,20,20,14,D,0,0,0,1,0,1,5,5,4,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,3,3,2,2,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,3,2,5.0,5,3.166666667,4.0,2.8,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,3,3,3,2,7.0,6.5,5.0,6.0,6.0,26,0,5.0,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,1,0,1,0,1,0,1,0,1,5,4,2,3,3,3,3,3,3,4,2,3,2,3,3,4,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,6,4,2,4,4,4,4,4,4,4,2,4,2,4,4,4,0.590909091,6.1,-1.1,6.1,-1.1,7.0,6.5,5.0,6.0,6.0,1.1,张琳琳,2.0,"任务二：【安全特工】
AI恐怖分子可能在化学领域或技术层面带来重大安全风险，就比如说可能会泄露一些机密性的化学分子信息，误导人类导致病毒扩散，人员伤亡。
我们可以利用对齐和超级对齐对AI进行规则规范，明确规定他们不能从事哪些行为，并进行实时的检测，我认为在之后的AI发展过程中我们会不断规范化，AI会在安全可控的环境下造福人类。同时我们也要对人进行普及教育，让使用者能够正确的利用AI。",4.829254430598276e-13,0.0847457627118644,0.0689655172413793,0.0847457627118644,0.012515644555694618,0.18416372970874184,0.11901624500751495,0.3577981651376147,0.014233576642335821
151017,14,1514,0,9.0,19006.0,2.0,2.0,6.0,6.0,5.0,5.0,4.333333333333333,3.0,4.0,3.333333333,3.333333333,5.333333333,6.0,4.514814815,4.088888889,2.533333333,3.2,5.4,3.9,3.9,0.125,4.2,4.333333333,4.666666667,4.2,3.666666667,4.333333333,4.5,4.666666667,4.75,3.6,4.0,4.0,5.0,18,16,20,25,14,D,0,0,0,1,0,1,6,9,2,5,4,4,4,4,5,5,5,5,5,5,4,5,5,5,5,3,4,5,5,3,3,3,3,4,3,3,4,4,5,4,4,3,3,3,4,4,5,3,3,3,3,3,3,4,4,5,4,5,5,5,4,5,4,3,1,1,1,4,1,2,7.875,9,3.833333333,5.0,4.0,3.2,4.0,3.4,4.833333333,3.75,3.0,4.0,4.666666667,4.75,1,4,1,2,7.0,7.0,6.5,7.0,7.5,24,0,8.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0,1,1,2,1,1,0,2,7,5,4,4,5,3,3,5,5,5,3,3,3,5,3,4,10,0,2,1,1,0,2,1,1,1,1,1,1,1,1,0,2,0,1,1,1,7,5,4,5,5,4,4,5,5,5,3,3,1,3,3,3,0.636363636,7.0,-1.0,7.0,-1.0,7.0,7.0,6.5,7.0,7.5,1.0,张涛,3.0,"如何破坏（具体场景）：AI可能在未来在化学领域造成重大安全风险，比如帮助制造化学武器等等。一方面，它可能具有巨大的破坏力，造成许多无辜平民的死亡，另一方面，它会造成社会恐慌，变成国家之间军备竞赛的武器，造成国际关系的紧张和分裂。
怎么预防和监测：使用对齐和超级对齐技术，在AI的训练阶段就严防AI被不发法子利用和越狱的可能。在国际层面展开开源活动，鼓励世界各国的技术专家共同讨论和研发新的安全技术和防火墙设置。在社会层面普及对AI的正确观念，对于那些企图利用AI破坏社会安定的不法活动予以严厉打击。
应对方案：启动应急预案，利用区块链技术和定位技术锁定危险来源。各级媒体和政府需要召开新闻发布会，稳定民心；技术专家要展开精诚合作，迅速补齐漏洞，制定新的AI伦理与准则。",1.4482060691732184e-10,0.06593406593406592,0.05555555555555556,0.06593406593406592,0.014952700640830029,0.36011140495351396,0.12951670587062836,0.37894736842105264,0.019613259668508243
151018,15,1515,0,3.0,19006.0,5.0,5.0,4.0,4.0,6.0,4.666666666666667,4.333333333333333,2.6666666666666665,2.6666666666666665,4.333333333,3.0,4.666666667,4.0,4.17037037,4.022222222,4.133333333,3.8,3.2,4.5,4.2,0.375,3.8,4.0,3.0,4.8,4.0,3.0,4.0,3.0,3.5,4.2,4.0,3.8,3.2,21,16,19,16,15,D,0,0,0,1,0,1,9,9,5,5,5,5,5,5,4,4,5,5,4,5,4,4,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,5,5,5,5,4,4,4,1,1,4,1,4,1,1,4,1,1,3,4,4,5,1,4,6,9.0,9,5.0,4.4,4.0,4.0,4.0,3.8,4.666666667,4.75,4.25,1.0,4.0,1.0,5,1,4,6,8.0,6.5,7.5,7.0,7.0,27,1,7.0,0,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,0,2,1,2,1,2,1,2,7,3,3,3,4,4,4,5,5,4,5,5,4,2,3,3,8,1,1,1,1,1,1,1,1,1,2,1,1,1,2,1,2,0,2,1,1,7,4,3,2,4,4,4,4,4,4,4,3,3,3,2,3,0.772727273,7.2,1.8,7.2,1.8,8.0,6.5,7.5,7.0,7.0,1.8,张伟岳,1.0,"AI恐怖分子
AI教育：可能是有投毒的数据，对教育信息产生特定的偏见，扭曲历史，扭曲价值观，而学生一般没有辨别能力。
网络信息安全：假新闻，和真实图片、视频没有差别的假信息，导致谣言横行，人们陷入恐慌，社会不稳定，或者是利用社交媒体机器人进行政治上的进攻。
AI控制医疗、法律、教育，可能导致有不可见的偏见出现。对于特定种族的人群或团体，有差别性的标注后，始终给予劣势的支持，导致马太效应。
检测与预防：
可以组织无国界的ai测试小组团体，对各代AI产品进行红蓝测试。出台法律文件，限制AI的无边界发展，对于特定领域，医疗、教育、法律AI只能用来参考，并且每次要给予AI反馈，发现问题后要及时修正。并且可以使用AI对AI监督。
比如AI教育阶段，需要事先规定好不可变更的文本，作为知识点，且使用特定的文本内容训练，不能允许上传新的数据来更迭模型。并且要在老师指导下使用AI产品，比如16岁以下的学生不能单独使用AI助手来学习。国家出台法律限制。",2.3509783001613433e-08,0.27368421052631575,0.23655913978494622,0.27368421052631575,0.02007444696451841,0.3553022959223423,0.14350351691246033,0.3815261044176707,0.0273972602739726
151019,15,1515,0,5.0,19007.0,4.0,5.0,3.0,1.3333333333333333,4.0,5.0,3.0,4.333333333333333,4.333333333333333,6.0,5.333333333,3.666666667,6.0,4.540740741,4.244444444,4.466666667,3.8,5.0,4.3,5.0,0.375,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,16,D,0,0,0,1,0,1,8,7,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,1,5,1,1,5,1,3,5,5,5,4,3,5,6,7.375,7,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,4.333333333,5.0,1.0,4,3,5,6,8.0,7.0,3.5,7.0,7.0,26,1,9.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,9,5,5,5,5,5,5,5,5,5,5,5,3,4,4,5,8,1,2,1,1,1,1,0,2,1,1,1,1,1,1,0,2,0,2,1,1,7,5,5,5,5,5,5,5,5,5,5,5,3,5,5,3,0.727272727,6.5,1.5,6.5,1.5,8.0,7.0,3.5,7.0,7.0,1.5,赵双琪,2.0,,,,,,,,,,
152014,15,1515,0,6.0,19993.0,3.0,3.0,1.6666666666666667,4.0,6.0,2.333333333333333,1.3333333333333333,3.0,3.333333333333333,4.666666667,4.333333333,4.666666667,4.666666667,2.800925926,3.805555556,2.833333333,3.0,2.9,2.9,3.4,0.25,4.0,3.666666667,3.666666667,4.0,3.666666667,3.666666667,4.0,4.333333333,5.0,1.8,2.75,2.0,3.4,9,11,10,17,15,D,0,0,0,1,0,1,7,6,5,4,5,5,3,3,4,5,5,5,4,5,4,5,5,5,4,4,3,3,3,4,2,2,2,2,2,4,4,3,4,4,3,3,3,3,3,5,4,4,4,3,3,3,3,5,4,5,2,4,2,2,4,2,3,4,4,4,4,1,3,2,6.375,6,4.166666667,4.6,3.4,2.0,3.8,3.0,4.666666667,4.25,3.0,4.0,4.333333333,2.0,4,1,3,2,6.5,6.0,6.0,6.0,6.0,20,0,7.0,0,1,1,1,1,1,1,1,1,2,1,1,0,2,0,2,1,1,0,2,1,2,1,2,7,5,3,3,4,3,4,4,4,4,4,4,3,3,3,4,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,1,1,8,4,3,4,4,3,4,4,4,4,4,4,3,3,3,3,0.727272727,6.1,0.9,6.1,0.9,6.5,6.0,6.0,6.0,6.0,0.9,牛春青,3.0,"任务二
比较温水煮青蛙的危害，比如欺诈行为，AI可以制造虚假信息制造恐慌，因为智能设备的普及，未成年在并不具备一定辨别能力的时候，会接触到大量信息且不知真假，AI在其中加入大量有害信息，截断人类未来。
直接了断的毁灭人类，AI可以搞崩网络系统，控制自动驾驶造成危害，接管武器系统向人类发射杀伤力武器等。
    预期在设计时就加入禁令与监测功能，在特定领域能否使用不智能的人工智能，回退到只能完成特定任务的状态。人类不能停止进步，确保AI始终处于掌控状态，必要时停止发展。不具备相关知识的人类基本在危害中无自保能力，所以掌握知识的人是什么样的人类就很重要了。
   立法保证大部分人类被禁止恶意使用AI，研究更强大的保密技术z阻隔AI。",,,,,,,,,
151021,16,1516,0,4.0,19008.0,2.0,2.0,3.333333333333333,5.0,4.333333333333333,3.6666666666666665,5.0,4.0,4.0,4.0,5.0,4.333333333,4.333333333,3.971296296,3.827777778,3.966666667,3.8,4.0,3.7,4.1,0.125,3.8,4.0,3.666666667,3.8,4.0,3.666666667,4.0,4.0,4.0,3.8,3.75,3.8,4.0,19,15,19,20,15,D,0,0,0,1,0,1,7,5,4,4,4,4,4,4,4,4,4,2,4,2,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,3,4,4,3,4,2,2,4,2,2,4,4,4,4,4,3,4,5.75,5,4.0,3.6,4.0,4.0,3.6,4.0,3.666666667,3.75,4.0,3.0,4.0,2.25,4,4,3,4,8.0,7.0,8.0,7.5,7.5,20,0,7.0,1,2,1,2,1,1,1,2,1,2,1,1,0,1,0,1,1,1,1,2,0,2,1,2,6,4,3,4,4,4,4,3,4,4,4,4,3,4,4,4,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,7,4,3,4,4,4,4,4,3,4,4,4,3,4,4,4,0.818181818,7.6,-0.6,7.6,-0.6,8.0,7.0,8.0,7.5,7.5,0.6,赵依然,2.0,"许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
在AI恐怖分子的所造成的重大风险方面，我们将其分成了四个部分——个人、社会与国家、全人类。在个人层面，AI恐怖分子可能会通过不实信息来诱导人们犯罪，造成个人经济上的损失、隐私信息上的盗取和思想上的侵袭，尤其针对未成年人可能会造成三观扭曲、犯罪心理产生，甚至有反社会行为等等。在社会层面，将会造成恐慌，社会不平等行为出现，人与人之间的信息是透明公开等现象。在国家层面上，信息安全将是一个重大问题，国家机密泄露，控制核武器等军事系统紊乱，战争一触即发等。国与国的合作也会受其影响，国际市场紊乱，虚拟货币系统崩塌，股市情况摇摆不定，造成政治、经济文化上的攻击与损失等。站在全人类的角度上说，人与人之间的信任会崩塌，对于科技的恐惧感上升，发展止步不前等情况都有可能发生。
在AI恐怖分子出现之前，我们就要采取一些措施来避免，在发展和强化AI技术的过程中，要有一定的限度，不要过度也不要完全不发展，使用超级对齐等策略使得AI不要完全人的掌控。其次要加强人工的检测，加强AI的规则意识，使其行为都在人类的监控之下。当AI恐怖分子出现时，应针对AI恐怖分子设计对应的AI网警，设计防火墙系统，一对一攻破。在监测时，注意特殊信号的出现，提高科技发展程度，促进网络安全系统的建立。",0.00027312333767034606,0.2823529411764706,0.26506024096385544,0.2823529411764706,0.05637162104332115,0.32123042034529986,0.18001006543636322,0.2843601895734597,0.05720020070245857
151022,16,1516,0,6.0,19009.0,5.0,5.0,2.0,2.0,4.0,4.0,3.6666666666666665,3.6666666666666665,3.333333333333333,5.666666667,5.0,4.666666667,4.333333333,3.227777778,4.366666667,4.2,3.2,3.8,3.7,4.6,0.25,3.2,3.666666667,3.666666667,3.8,3.333333333,3.666666667,4.0,3.666666667,4.0,2.8,3.5,3.2,3.6,14,14,16,18,16,D,0,0,0,1,0,1,6,3,4,3,4,4,2,2,2,2,2,3,2,4,3,4,4,2,3,3,3,2,3,2,3,4,2,3,4,4,4,2,4,3,2,2,2,2,4,4,4,3,3,3,3,4,3,4,4,3,3,3,4,3,3,3,4,3,3,2,2,4,1,3,4.125,3,3.166666667,2.2,2.6,3.2,3.4,2.4,3.333333333,3.5,3.25,4.0,3.0,3.25,2,4,1,3,5.5,7.0,5.0,6.0,6.5,19,0,7.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,1,1,1,1,1,1,2,8,4,3,4,3,3,4,4,4,4,3,4,3,3,4,3,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,0,1,8,4,3,4,4,4,3,4,4,3,2,3,2,4,4,3,0.772727273,6.0,0.0,6.0,0.0,5.5,7.0,5.0,6.0,6.5,0.0,赵悦言,1.0,"风险：
诱导犯罪与自我伤害，财务隐私
信息安全，虚假信息及影响，虚拟金融-金融秩序不稳定-国际秩序紧张
预防检测：
加强规则训练，强化ai规则意识，规范底层逻辑
训练防火墙预防
强化人类发布信息的真实性与规范性，避免ai学习虚假信息产生幻觉
对重大选项确认进行人工与网络双重确认
技术方案：
训练ai网警，利用模型监督模型
加强超级对齐
对人工智能留存实体以控制，避免全面脱实向虚
加强技术研究，使对齐与超级对齐技术发展快于ai自主性",2.1205975848558255e-20,0.043243243243243246,0.03278688524590164,0.043243243243243246,0.006724590653886387,0.3203423158282076,0.08789798617362976,0.3951612903225806,0.0094394143710268
151023,16,1516,0,5.0,19009.0,3.0,3.0,3.333333333333333,2.0,5.0,4.333333333333333,5.666666666666667,3.333333333333333,3.0,2.666666667,3.333333333,4.333333333,3.666666667,4.032407407,4.194444444,4.166666667,4.0,4.4,3.1,3.6,0.125,4.4,4.333333333,4.0,4.2,4.0,4.333333333,4.0,4.333333333,4.5,2.4,4.5,4.2,4.2,12,18,21,21,16,D,0,0,0,1,0,1,6,9,3,5,4,4,4,5,4,4,5,4,5,5,5,4,5,4,3,4,3,2,3,3,4,4,3,4,3,4,5,3,3,4,3,4,3,3,4,4,3,4,3,4,3,3,3,4,3,4,4,4,4,4,5,3,5,4,3,3,2,3,2,2,7.875,9,4.166666667,4.4,3.0,3.6,3.8,3.4,4.333333333,3.5,3.25,4.0,4.333333333,3.75,2,3,2,2,5.5,6.0,5.0,6.0,6.0,24,0,7.0,1,1,1,1,1,1,1,2,1,2,1,1,0,2,0,2,1,2,1,1,1,2,1,2,5,5,5,3,4,3,5,5,3,5,4,4,4,3,3,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,2,0,2,7,5,4,3,4,4,5,5,5,5,4,3,3,4,3,3,0.863636364,5.7,0.3,5.7,0.3,5.5,6.0,5.0,6.0,6.0,0.3,邹姹姹,3.0,"风险：1.对人身权力权益的伤害个人隐私等2.个人、社会、国家信息安全3.Ai的幻觉所致的错误信息误导4.社会人人自危，恐慌感，秩序受影响
预防：在人工智能训练前期加强人工智能的责任、规则意识，设立一定的程序，将人类的人身安全及权益放在第一位，一旦AI危及人类，有相关的行为，给予惩罚甚至自毁？
方法：ai与ai之间相互监督，人类对其进行监督，谨防后门攻击、对抗攻击等，超级对齐
展望：未来AI会在更多场景中应用，发挥更大的作用。",9.371964383279487e-27,0.07171314741035857,0.05622489959839357,0.07171314741035857,0.006817655788370162,0.28314682416379244,0.10653521865606308,0.4765625,0.008425414364640837
161000,1,1601,启发员,10.0,19958.0,6.0,6.0,3.6666666666666665,5.0,4.666666666666667,4.666666666666667,3.333333333333333,4.333333333333333,3.6666666666666665,5.666666667,5.333333333,5.333333333,5.333333333,4.49537037,3.972222222,3.833333333,4.0,5.2,4.3,5.0,0.375,3.6,3.0,3.333333333,4.0,4.666666667,4.0,5.0,4.0,5.0,1.6,2.75,2.0,3.0,8,11,10,15,1,E,1,1,0,1,1,0,8,2,3,3,3,4,3,3,3,3,5,5,4,4,4,4,4,2,4,4,4,4,3,3,1,1,1,1,1,4,4,4,4,4,4,4,4,4,4,4,4,3,4,5,5,5,5,5,5,3,3,3,3,3,3,4,4,5,4,4,2,3,4,4,4.25,2,3.166666667,4.0,3.6,1.0,4.0,4.0,3.666666667,3.75,5.0,4.666666667,3.0,3.25,2,3,4,4,7.5,7.5,6.5,7.0,7.0,20,0,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,0,1,0,2,0,1,1,2,8,4,4,4,4,5,5,4,4,4,4,4,2,3,3,5,6,0,2,1,1,0,2,1,1,1,1,1,1,0,1,0,1,0,1,1,1,4,4,3,3,3,3,3,5,4,3,3,3,2,5,5,3,0.5,7.1,0.9,7.1,0.9,7.5,7.5,6.5,7.0,7.0,0.9,刘馨雨,1.0,"领域或技术层面带来重大安全问题：
国家信息安全：国家安全的保护，泄密，但是有一定的抵抗能力，隔离互联网，
伦理课的给ai立法，
（本地数据，不上传会被盗取信息吗）
（多个端的信息无法完全删掉）
个人信息安全：日常生活个人信息的泄露情况泄露，更容易被别人利用
人脸信息，说话方式不能确认个人身份，更被诈骗
对人类生理的操纵：脑机接口：ai对个人的影响，脑电信号获取，没有ai的操作空间，未来发展可能会有问题，对人类的操控
对社会道德的操纵：放大人性的恐怖和阴暗面，加剧社会对立和冲突，
Ai强人工智能，超过人类的知识部分：
预防和监测
①联网导致没有办法保护信息，训练ai主动隐藏标记隐私的信息
②训练安全的ai模型相互博弈
③Ai的自主意识，强人工智能：超级对齐，拆解成小部分或者立法
④在最坏的答案算上进行纠正，寻找解决方法
应对方法：
①有监督的微调和从人类的反馈中学习
②分而治之，将问题一步步解决
③数据投毒和后门攻击，训练ai对人类的保护关键词",7.0361799303854156e-06,0.3720930232558139,0.34146341463414637,0.3720930232558139,0.03833188342757721,0.6397916553156427,0.12441520392894745,0.3346303501945525,0.0416873449131514
161001,1,1601,协调员,6.0,19958.0,2.0,3.0,4.0,2.6666666666666665,6.0,5.666666666666667,4.333333333333333,3.0,3.0,3.666666667,4.666666667,3.0,5.666666667,3.606481481,4.638888889,3.833333333,4.0,4.0,4.4,4.7,0.625,4.2,5.0,4.0,3.6,4.666666667,4.333333333,4.5,4.666666667,4.5,2.8,3.25,3.8,4.2,14,13,19,21,1,E,1,2,0,1,1,0,7,6,4,4,4,5,4,4,3,2,4,2,4,5,4,4,4,3,2,4,4,3,5,4,4,4,3,4,3,4,4,4,4,4,4,3,3,3,4,3,4,3,4,4,4,4,4,3,4,4,2,4,4,2,4,2,2,3,4,4,5,2,3,4,6.375,6,4.166666667,3.0,4.0,3.6,4.0,3.4,3.666666667,3.5,4.0,3.0,4.0,2.5,5,2,3,4,7.5,7.5,6.5,7.0,7.0,23,0,9.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,9,5,3,5,5,4,5,5,4,5,1,3,1,3,3,3,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,1,1,1,9,5,2,5,5,5,5,5,4,5,3,4,1,3,3,3,0.863636364,7.1,-0.1,7.1,-0.1,7.5,7.5,6.5,7.0,7.0,0.1,周子淇,2.0,"领域或技术层面带来重大安全问题：
国家信息安全：国家安全的保护，泄密，但是有一定的抵抗能力，隔离互联网，
伦理课的给ai立法，
（本地数据，不上传会被盗取信息吗）
（多个端的信息无法完全删掉）
个人信息安全：日常生活个人信息的泄露情况泄露，更容易被别人利用
人脸信息，说话方式不能确认个人身份，更被诈骗
对人类生理的操纵：脑机接口：ai对个人的影响，脑电信号获取，没有ai的操作空间，未来发展可能会有问题，对人类的操控
对社会道德的操纵：放大人性的恐怖和阴暗面，加剧社会对立和冲突，
预防和监测
联网导致没有办法保护信息，训练ai主动隐藏标记隐私的信息
②训练安全的ai模型相互博弈，让AI自我对抗
③Ai的自主意识，强人工智能：超级对齐，拆解成小部分或者立法
④在最坏的答案算上进行纠正，寻找解决方法
应对方法：
①有监督的微调和从人类的反馈中学习
②分而治之，将问题一步步解决
③数据投毒和后门攻击，训练ai对人类的保护关键词",0.0010565456879416792,0.48484848484848486,0.45161290322580644,0.48484848484848486,0.0695988232610617,0.6009720817150533,0.14320670068264008,0.268,0.07322654462242562
161002,1,1601,记录员,9.0,19959.0,6.0,7.0,2.333333333333333,5.0,4.333333333333333,2.333333333333333,5.0,2.6666666666666665,3.333333333333333,3.333333333,5.666666667,5.0,4.666666667,3.042592593,3.255555556,2.533333333,2.2,3.0,3.4,3.7,0.25,4.0,4.0,3.0,4.4,4.0,3.666666667,3.5,4.0,4.75,2.2,3.5,3.4,3.4,11,14,17,17,1,E,1,3,0,1,1,0,6,6,4,3,3,3,4,4,4,4,4,3,3,3,5,5,4,4,4,4,4,4,3,4,3,4,3,3,3,4,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,3,3,2,3,4,3,4,3,3,4,4,2,3,5,6.0,6,3.5,3.6,3.8,3.2,3.4,4.0,4.166666667,4.0,4.0,3.666666667,3.666666667,2.75,4,2,3,5,7.5,7.5,6.5,6.5,7.0,19,0,8.0,0,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,1,1,0,2,1,1,1,1,6,4,3,4,4,4,4,4,4,5,5,4,3,4,3,3,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,1,1,6,3,2,4,4,4,4,4,4,4,4,4,2,3,2,3,0.772727273,7.0,-1.0,7.0,-1.0,7.5,7.5,6.5,6.5,7.0,1.0,杨帅,3.0,"领域或技术层面带来重大安全问题：
①国家信息安全：国家安全的保护，泄密，但是有一定的抵抗能力，
（隔离互联网，给ai立法）
（本地数据，不上传会被盗取信息吗）
②个人信息安全：日常生活个人信息的泄露情况泄露，更容易被别人利用
人脸信息，说话方式不能确认个人身份，更被诈骗
（多个端的信息无法完全删掉）
③对人类生理的操纵：脑机接口：ai对个人的影响，脑电信号获取，没有ai的操作空间，未来发展可能会有问题，对人类的操控
④对社会道德的操纵：放大人性的恐怖和阴暗面，加剧社会对立和冲突，
⑤Ai强人工智能，超过人类的知识部分：
预防和监测：
①联网导致没有办法保护信息，训练ai主动隐藏标记隐私的信息
②训练安全的ai模型相互博弈
③Ai的自主意识，强人工智能：超级对齐，拆解成小部分或者立法
④在最坏的答案算上进行纠正，寻找解决方法
应对方法：
①有监督的微调和从人类的反馈中学习
②分而治之，将问题一步步解决
③数据投毒和后门攻击，训练ai对人类的保护关键词",2.1442919119708378e-06,0.17204301075268816,0.15384615384615385,0.17204301075268816,0.031229043255094117,0.6299278445765972,0.15353234112262726,0.3448275862068966,0.03723849372384935
161003,2,1602,启发员,8.0,19959.0,5.0,6.0,6.0,4.666666666666667,6.0,3.333333333333333,2.0,5.0,5.0,6.0,5.0,4.0,6.0,4.771296296,4.627777778,4.766666667,3.6,4.2,3.1,3.3,1.0,5.0,5.0,4.0,5.0,5.0,4.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,2,E,1,1,0,1,1,0,7,7,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,3,5,5,2,2,2,4,2,5,5,5,5,5,2,2,2,4,5,5,5,2,4,4,2,3,2,1,1,4,1,4,1,1,4,1,1,3,5,5,3,1,3,4,7.0,7,4.0,4.0,4.6,2.4,5.0,3.0,4.0,4.0,2.75,1.0,4.0,1.0,3,1,3,4,7.5,7.5,6.0,7.0,7.0,20,0,7.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,7,5,2,5,5,5,5,5,5,5,5,5,2,5,5,5,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,7,5,2,5,5,5,5,5,5,5,5,5,2,5,5,5,0.909090909,7.0,0.0,7.0,0.0,7.5,7.5,6.0,7.0,7.0,0.0,刘奖,3.0,"社群谣言散布和信息投毒问题
解决方法：建立用于识别和处理不良有害信息的AI模型，并且严格管控AI的训练过程
如：在无标注训练环节保证输入信息的质量，避免有害信息的干扰；在有标注微调环节对于有害信息进行标注，让AI能够识别有害信息；各大网络平台合理使用AI生成内容，控制AI生成内容的量等
投放病毒、信息战、控制AI技术问题
解决方法：建立用于查杀病毒的AI模型，在训练阶段输入病毒文件的信息，使其能够学习病毒的特征，并对病毒进行有效的早期查杀，防止病毒扩散危害网络安全、造成网络瘫痪等
隐私泄露问题
解决方法：控制AI的使用场景，如一些关键单位和关键技术的研发者应尽量避免使用市面上的AI模型，或自行研发AI模型并控制其不外流；对于AI模型进行对齐操作，给AI设立规范防止AI隐私泄露等",1.6050778521844675e-06,0.5416666666666666,0.5217391304347826,0.5416666666666666,0.02877709039631671,0.48390830711065114,0.14649717509746552,0.35384615384615387,0.030425055928411604
161004,2,1602,协调员,5.0,19960.0,2.0,2.0,3.0,5.0,4.0,5.333333333333333,4.333333333333333,4.0,3.6666666666666665,5.0,3.666666667,5.333333333,5.666666667,4.913888889,4.483333333,3.9,4.4,4.3,4.3,4.2,0.625,3.8,4.333333333,4.666666667,4.2,4.0,4.0,5.0,4.333333333,4.5,4.4,4.5,4.4,4.6,22,18,22,23,2,E,1,2,0,1,1,0,10,10,5,5,5,5,5,5,5,5,5,4,5,5,4,5,4,4,4,4,4,4,5,5,4,4,4,4,4,4,5,4,4,4,5,4,4,4,5,5,4,4,4,4,5,5,5,4,5,5,4,5,5,4,5,4,5,4,4,5,5,1,3,5,10.0,10,5.0,4.8,4.4,4.0,4.2,4.4,4.333333333,4.25,4.75,4.666666667,5.0,4.25,5,1,3,5,6.5,6.5,5.5,6.5,6.5,21,1,6.0,0,1,1,1,1,1,1,1,0,1,1,1,0,1,0,1,0,2,0,1,0,1,1,1,6,4,4,4,3,4,5,4,4,5,4,4,5,4,3,4,7,0,1,1,1,0,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,7,5,5,4,4,5,4,4,4,4,3,4,3,4,4,4,0.5,6.3,3.7,6.3,3.7,6.5,6.5,5.5,6.5,6.5,3.7,陈昌鑫,1.0,"     首先，我认为不法分子会利用AI进行虚假信息传播，通过在社群媒体散步大量虚假，煽动的不法信息，通过算法控制大量虚拟账号，达到引发社会恐慌与动荡的目的。我们可以通过机器学习算法，对大量不法分子制造的虚拟账号进行打击和过滤，有利于网络环境的清明与健康。
     其次，不法分子可能会利用AI打信息战，通过木马和病毒进行远程操控，瘫痪国家的国防系统。对此，我们可以立法来捍卫国家安全，增强信息识别系统来对境外势力的信息渗透予以严厉打击。
      最后AI技术还会造成隐私泄露的危险。而个人身份信息的泄露还会连带人身安全和财产安全的风险。对此，我们可以加强数据加密和认证机制，使用多因素认证，减少信息泄露的风险。
      总之，AI技术是一把双刃剑。我们要辩证的看待。",0.0006538818896764228,0.38095238095238093,0.3157894736842105,0.38095238095238093,0.05811334164282293,0.45409070805047563,0.24293097853660583,0.31840796019900497,0.07085714285714284
161005,2,1602,记录员,4.0,19960.0,3.0,3.0,3.0,5.333333333333333,6.0,5.0,5.333333333333333,3.0,3.0,5.666666667,4.666666667,5.666666667,6.0,4.822222222,4.933333333,4.6,4.6,4.2,4.3,4.5,0.625,3.6,3.666666667,3.333333333,3.8,3.666666667,4.666666667,4.0,4.0,4.0,2.8,3.25,3.4,3.6,14,13,17,18,2,E,1,3,0,1,1,0,7,2,4,3,4,4,4,4,4,4,4,3,4,4,4,3,3,4,3,2,3,2,2,2,2,2,2,2,2,3,4,4,3,4,3,3,3,3,3,3,3,3,3,3,3,3,3,4,3,3,3,4,3,3,4,3,3,3,3,3,4,3,4,6,3.875,2,3.833333333,3.8,2.2,2.0,3.6,3.0,3.5,3.0,3.0,3.333333333,3.666666667,3.0,4,3,4,6,5.5,5.0,5.5,5.5,5.5,20,0,8.0,0,1,1,2,0,2,1,2,0,2,1,1,0,2,0,2,1,2,1,2,0,2,1,2,6,4,5,5,4,4,3,4,4,3,4,4,3,3,3,3,7,1,1,1,2,1,1,1,2,1,2,1,1,1,1,0,2,0,2,0,2,7,4,3,3,3,4,4,3,4,3,4,4,2,3,3,3,0.590909091,5.4,1.6,5.4,1.6,5.5,5.0,5.5,5.5,5.5,1.6,刘炘瑶,2.0,"任务二：【安全特工】
许多科幻小说和影视作品都构想了人工智能毁灭人类的情景。试想一个未来的“AI恐怖分子”，它可能擅长利用前沿技术实施破坏。如果你是安全部门的特工，请分析AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
散播不良言论：不法分子通过AI手段并通过编辑代码，后台破解，并潜入社群，角色扮演成权威人士等发布不良言论造成恐慌。
预防和监测：机器学习、深度学习、自然语言处理等技术，数据库里可能有不良言论，但是应该有自动删除的“回收站库”，因为事先得有，才能进一步识别？
信息战：不法分子通过AI已有的数据库等，自动生成代码，生成网络与信息病毒，对用户进行“投毒”攻击，事关国家、国防安全等。
预防和监测：源头在于人，在重要领域应该对人有所限制和避免，其实使用专用型AI都会有很大的风险
隐私泄露：在与AI对话的过程中可能人会为了解决一些触及隐私的问题，无意识地将隐私暴露给AI，更严重的是造成连带的一系列财产安全、肖像权等安全风险
预防和监测：个人认为隐私识别有一些难度，更多技术难题在于避免信息的流出，建立安全墙。",4.06504000902186e-05,0.2222222222222222,0.19999999999999998,0.2222222222222222,0.04479394783993629,0.6296200306287775,0.19864650070667267,0.31347962382445144,0.04324078288575328
161006,3,1603,启发员,11.0,19961.0,3.0,3.0,3.0,3.6666666666666665,3.6666666666666665,2.333333333333333,5.0,3.6666666666666665,3.333333333333333,4.333333333,4.666666667,3.666666667,4.0,2.646296296,3.877777778,3.266666667,2.6,4.4,2.8,3.3,0.125,3.8,3.0,2.333333333,3.8,2.666666667,2.666666667,3.5,4.0,3.75,2.4,2.25,2.8,3.4,12,9,14,17,3,E,1,1,0,1,1,0,3,8,2,3,3,2,4,4,4,3,4,4,3,4,4,2,3,4,3,3,3,3,3,4,2,2,2,3,2,4,3,2,4,4,4,3,3,3,3,4,4,3,3,3,3,3,3,4,4,3,4,3,4,4,4,4,4,2,3,2,4,3,2,2,6.125,8,3.0,3.6,3.2,2.2,3.4,3.2,3.333333333,3.5,3.0,4.0,3.333333333,4.0,4,3,2,2,5.5,5.5,5.0,5.0,5.0,18,1,7.0,1,1,1,1,1,1,1,1,1,2,1,1,1,2,0,2,1,1,1,2,1,2,0,2,6,4,1,3,3,3,2,4,4,3,4,4,3,4,4,2,7,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,2,1,1,7,3,1,3,3,3,3,4,4,3,4,4,2,4,4,3,0.909090909,5.2,-2.2,5.2,-2.2,5.5,5.5,5.0,5.0,5.0,2.2,代京耀,3.0,"任务一
ai掌握大量数据同时拥有极强计算能力。在军事国防领域ai可能做到掌握武器权限直接对人类进行军事打击。Ai可能变身超级黑客使人类信息通道瘫痪或改变信息提供虚假信息，引发错乱。
在ai中加入批评模型，设立规则。
使用传统信息渠道。",8.455210223378171e-10,0.29629629629629634,0.24000000000000002,0.29629629629629634,0.01485884101040119,0.2659532692374358,0.08992058783769608,0.34328358208955223,0.019343986543313707
161007,3,1603,协调员,4.0,19961.0,6.0,7.0,4.666666666666667,4.0,5.0,5.666666666666667,1.0,3.333333333333333,3.6666666666666665,5.333333333,6.0,5.333333333,6.0,4.955555556,4.733333333,4.4,3.4,4.8,5.3,5.6,0.5,5.0,5.0,5.0,4.6,5.0,5.0,4.5,5.0,5.0,4.2,5.0,5.0,5.0,21,20,25,25,3,E,1,2,0,1,1,0,8,6,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,4,3,4,4,4,4,4,3,4,4,2,5,4,2,5,3,2,3,4,5,4,3,3,7,6.75,6,4.666666667,5.0,4.6,5.0,5.0,5.0,5.0,3.5,4.0,3.0,4.666666667,2.75,4,3,3,7,6.0,6.5,6.0,6.0,6.0,22,0,9.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,0,2,8,5,5,5,5,5,5,5,5,5,3,5,3,3,4,4,10,0,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,2,1,1,8,5,5,5,5,5,5,5,5,5,5,5,2,3,3,4,0.727272727,6.1,1.9,6.1,1.9,6.0,6.5,6.0,6.0,6.0,1.9,赵莹博,1.0,由于AI技术在人类社会方方面面的应用，AI恐怖分子也相应地可能对各个领域都有不同程度的危害，比如最直接的串联人类沟通的互联网、信息技术、计算机领域，以及国防领域。可能直接导致人类之间的沟通失效和人类家园破坏。面对这些风险，可以通过设计批评模型、制定规则等方式进行预防和监测，依然使用互联网方式编写程序或者在万不得已之时停止对AI的电力供应。具体场景如流浪地球中面对moss的攻击，未来AI防御可以从加强互联网程序编写、适当出台法律政策限制AI技术的发展，确保人类对齐能力始终高于AI能力。,6.372473471705699e-11,0.1875,0.12903225806451613,0.1875,0.015496249057743642,0.5775911006596188,0.12984643876552582,0.4122137404580153,0.018698060941828243
161008,3,1603,记录员,5.0,19962.0,4.0,4.0,2.0,2.0,4.666666666666667,4.666666666666667,1.6666666666666667,3.6666666666666665,3.6666666666666665,4.666666667,3.0,4.0,3.333333333,3.489814815,3.938888889,3.633333333,3.8,3.4,4.6,4.6,0.375,3.8,4.0,3.333333333,3.6,3.666666667,3.666666667,4.0,4.666666667,4.5,3.6,3.5,3.6,4.2,18,14,18,21,3,E,1,3,0,1,1,0,7,5,4,4,4,5,5,4,3,3,4,4,4,5,3,4,4,5,3,4,3,3,4,3,4,4,4,3,4,5,4,4,4,5,5,5,3,3,4,4,4,3,4,3,3,4,4,4,4,5,2,3,2,1,4,1,3,4,4,4,4,2,3,5,5.75,5,4.333333333,3.6,3.4,3.8,4.4,4.0,4.0,3.75,3.5,3.666666667,4.0,1.5,4,2,3,5,6.5,6.0,5.0,5.5,6.0,23,1,7.0,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,2,0,2,1,1,0,2,0,1,6,4,3,4,4,4,3,4,4,3,4,3,2,3,4,4,7,1,1,1,1,0,1,0,1,1,1,1,2,0,1,0,1,0,2,1,1,5,4,3,3,4,5,3,4,4,4,4,3,3,3,4,4,0.545454545,5.8,1.2,5.8,1.2,6.5,6.0,5.0,5.5,6.0,1.2,赵浩然,2.0,"Q1:AI带来的重大安全风险
1 互联网信息泄露 
2 国家安全受到危害
Q2:如何检测和预防，利用哪些技术设计方案
1 设计批评模型 
2 断绝与它相连的互联网和电力供应
Q3:结合实际情况，提出可行性和创新性的解决办法，展望AI安全防御技术的未来发展
例如当ai有攻击人类并且危害国家安全和人类安全的倾向时，应该断绝与之相关的学习模式，防止ai再恶化，同时设计更完善的模型规范ai的思维方式。Ai安全防御技术的未来发展我认为应该规范AI的学习模式，不要让AI学习一些对人类发展有害的内容。",0.0005839586506397126,0.4210526315789474,0.33333333333333326,0.4210526315789474,0.056692099187658766,0.5066639323951839,0.17362700402736664,0.27586206896551724,0.059374999999999956
162000,5,1605,启发员,8.0,20951.0,7.0,7.0,5.0,5.0,4.0,3.6666666666666665,2.333333333333333,3.6666666666666665,4.0,5.0,5.0,5.0,5.0,3.994444444,3.966666667,3.8,3.8,4.7,4.1,4.3,0.375,3.4,3.666666667,3.0,4.0,3.0,3.0,4.0,3.333333333,4.0,3.0,4.0,3.6,3.8,15,16,18,19,5,E,1,1,0,0,1,0,6,6,4,4,4,4,4,4,3,3,3,3,4,4,4,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,2,4,3,4,3,3,4,3,3,3,4,4,4,3,4,5,6.0,6,4.0,3.2,4.0,4.0,4.0,4.0,3.5,4.0,4.0,3.0,4.0,3.0,4,3,4,5,3.0,2.0,2.5,2.5,2.5,20,1,6.0,0,1,1,1,1,1,1,1,0,1,1,1,0,1,0,1,0,1,0,1,0,1,1,1,6,4,2,3,3,3,3,4,4,4,4,4,3,4,4,4,6,0,1,1,1,0,1,1,1,0,1,1,1,1,1,0,1,1,1,0,1,6,4,2,3,3,4,4,4,4,4,2,3,3,3,4,4,0.454545455,2.5,3.5,3.3855,2.6145,3.827,3.305,3.238,3.331,3.225,2.6145,李靖洋,1.0,能源动力。根据路况判断行驶时调配发动机的功率，实现时间与能源上的双重节约；基于已有的考古文字进行训练，判断那些未确定的古文字表述的含义,6.191016440512913e-24,0.0,0.0,0.0,0.0041634449533099385,0.3672056371360167,-0.0020642310846596956,0.35135135135135137,0.006974248927038573
162001,5,1605,协调员,4.0,20952.0,4.0,4.0,4.0,3.6666666666666665,6.0,4.0,2.0,3.6666666666666665,2.6666666666666665,5.333333333,4.333333333,5.333333333,4.0,3.968518519,3.811111111,2.866666667,2.2,3.7,2.9,2.9,0.625,3.8,4.333333333,3.666666667,3.6,3.666666667,3.0,4.5,3.666666667,2.75,3.2,4.0,3.6,3.0,16,16,18,15,5,E,1,2,0,0,1,0,8,1,5,5,5,5,5,5,5,3,5,3,3,5,4,2,4,4,4,4,4,4,4,2,4,2,4,4,3,5,5,5,5,5,5,4,4,4,5,4,4,2,4,4,4,4,4,4,4,4,2,4,1,2,4,2,3,4,4,5,5,3,3,5,3.625,1,5.0,3.8,3.6,3.4,5.0,4.4,3.833333333,3.5,4.0,3.666666667,4.0,1.75,5,3,3,5,3.5,2.5,3.0,3.0,2.5,20,0,6.0,0,1,1,1,0,1,1,2,1,1,1,1,0,1,1,2,1,2,0,2,0,2,1,1,6,4,2,3,4,3,4,3,4,2,5,4,4,2,3,3,6,1,2,1,2,0,2,1,1,0,2,1,2,1,1,1,2,0,2,1,1,5,4,3,4,5,4,4,4,4,3,4,4,3,4,4,3,0.636363636,2.9,5.1,3.7855,4.2145,4.327,3.805,3.738,3.831,3.225,4.2145,徐一冰,3.0,"专业：武术与民族传统体育。
AI智能评分系统：通过摄像头和红外线等体外检测技术捕捉动作，进行评分，避免人工评分存在的主观性和动作漏洞；
编武：根据每个人的肌肉耐受度和年龄，性别等因素进行编排",4.101928859538122e-12,0.06451612903225806,0.0,0.06451612903225806,0.01078981441519206,0.34239165416953804,0.01553172804415226,0.375,0.016393442622950838
162002,5,1605,记录员,5.0,20952.0,3.0,3.0,1.0,2.0,6.0,5.333333333333333,2.6666666666666665,3.0,3.0,6.0,3.666666667,6.0,6.0,4.809259259,4.855555556,4.133333333,4.8,4.3,4.4,4.4,0.5,4.0,4.666666667,4.333333333,4.0,4.666666667,4.666666667,4.0,5.0,4.0,1.0,3.25,3.4,3.0,5,13,17,15,5,E,1,3,0,0,1,0,9,9,5,5,5,5,5,5,2,4,3,3,4,4,4,4,3,4,4,5,5,5,5,3,4,4,4,4,4,5,5,5,5,5,4,4,4,4,4,4,4,3,4,4,4,4,4,3,3,5,2,4,2,2,4,2,2,3,4,4,4,1,2,4,9.0,9,5.0,3.2,4.6,4.0,5.0,4.0,3.833333333,3.75,4.0,2.666666667,4.333333333,2.0,4,1,2,4,3.5,2.5,4.5,4.0,4.5,23,0,7.0,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,8,4,5,5,5,4,5,3,4,3,5,5,3,3,3,3,8,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,8,4,4,5,5,5,4,4,4,4,4,4,3,3,3,3,0.863636364,3.8,5.2,4.6855,4.3145,4.327,3.805,5.238,4.831,5.225,4.3145,吕秋含,2.0,管理科学与工程。多主体协同，在自然灾害发生时，协调政府部门、社会组织和受灾群众之间的资源调配、路径规划等问题。灾害链的推演，基于已发生的灾害及衍生灾害的发生情况，推测灾害链关系。,5.782403931520567e-17,0.0,0.0,0.0,0.006959995158264239,0.4145770460719136,0.055906422436237335,0.37735849056603776,0.010928961748633892
162003,6,1606,启发员,4.0,20953.0,3.0,3.0,3.0,4.333333333333333,4.0,5.0,3.0,4.0,3.0,4.333333333,5.0,4.0,5.0,4.353703704,4.122222222,4.733333333,4.4,4.9,4.0,5.1,0.75,3.8,4.666666667,3.666666667,3.6,3.666666667,2.666666667,3.5,4.0,3.5,4.2,4.5,4.4,4.4,21,18,22,22,6,E,1,1,0,0,1,0,6,7,4,3,4,4,3,3,4,4,4,4,4,3,4,3,4,5,5,4,3,3,4,3,4,4,4,4,4,4,3,4,4,4,3,4,4,4,3,4,4,3,4,4,4,4,4,3,4,4,3,4,4,3,4,4,3,4,4,4,4,3,2,3,6.625,7,3.5,4.0,3.4,4.0,3.8,3.6,4.0,3.75,4.0,3.333333333,4.0,3.5,4,3,2,3,5.5,6.0,5.5,5.5,5.5,22,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,1,1,1,1,1,1,1,1,8,3,2,3,4,3,4,4,4,4,2,4,2,3,3,3,9,0,1,1,1,1,1,1,1,0,1,1,1,1,1,0,2,1,1,1,1,8,4,3,4,5,5,4,4,5,4,3,3,3,4,4,4,0.727272727,5.6,0.4,6.4855,-0.4855,6.327,7.305,6.238,6.331,6.225,0.4855,亓泽正,3.0,"对于植物保护专业，其实践的关键环节包括植物采样、植物分类、遗传学分析、物种多样性研究等。人工智能技术能够为这些环节提供技术支持，以提高实验效率。
对于植物采样和分类环节，可以采用多模态大模型来进行辅助分类，能够增加区分样本类别的速度，并且提高对难分样本的分类准确性。
对于遗传学分析，可以利用使用领域内专家知识对大模型进行Fine-tuning，以得到更加具有遗传学专业知识的大语言模型，此外还可以针对遗传学的数据结构对模型的多模态能力进行提升。
此外，对于物种多样性研究，还可以借助世界模型的思想，构建一个物种多样性演化世界模型。",0.0003194502503530176,0.0,0.0,0.0,0.04642857142857143,0.5068269398114412,0.14064423739910126,0.29577464788732394,0.051181102362204745
162004,6,1606,协调员,6.0,20953.0,0.0,0.0,2.0,6.0,4.333333333333333,5.333333333333333,6.0,1.0,1.3333333333333333,5.666666667,4.666666667,4.333333333,6.0,2.961111111,3.766666667,2.6,2.6,4.7,4.6,5.7,0.375,3.4,4.0,1.333333333,4.2,4.333333333,1.333333333,5.0,5.0,5.0,5.0,5.0,5.0,5.0,25,20,25,25,6,E,1,2,0,0,1,0,2,10,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,3,1,1,7,7.0,10,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,5.0,3,1,1,7,4.5,4.5,5.5,5.5,5.5,20,1,3.0,0,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,2,0,1,1,1,3,2,1,1,4,5,4,5,5,5,1,5,1,2,1,1,2,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,0,1,1,1,2,2,1,1,4,4,4,5,5,5,1,1,1,1,1,1,0.727272727,5.1,-3.1,5.9855,-3.9855,5.327,5.805,6.238,6.331,6.225,3.9855,翟建欣,2.0,"    经过讨论，我们决定以吴环宇同学所学专业“野生动物与植物保护（植物方向）”为讨论对象。
    人工智能的发展，可能有助于在植物识别以及未发现性状预判方面推进研究。植物识别方面，人工智能可以借助外在设备（比人眼更好的观察设施）同时结合其经过大量专业数据训练的识别库（比人脑更准确专业的识别系统）来达到更好的效果。在未发现性状预判方面，由于人工智能预判蛋白质结构的先例存在，同时基因表达产生不同的蛋白质并且排列组合而后表现出性状，使得人工智能在同一类群中预判未发现新性状成为可能。人难以想象未曾见过的事物，而人工智能则可以从性状表现的根源出发，从基因遗传到蛋白质表达再到性状表现，层层递进，进而进行合理的预测。
    综上，人工智能的发展可以至少在未发现性状预测以及植物识别方面推进野生动物与植物保护（植物方向）的专业研究。",0.0007510496320783863,0.0,0.0,0.0,0.05185067007019783,0.36794106609710514,0.16061349213123322,0.20853080568720378,0.06240713224368499
162005,6,1606,记录员,4.0,20954.0,4.0,4.0,1.0,1.0,2.0,4.333333333333333,5.666666666666667,3.0,3.333333333333333,5.0,4.333333333,2.0,2.666666667,3.764814815,4.588888889,3.533333333,3.2,3.4,2.5,4.4,0.125,4.0,3.0,3.666666667,3.8,3.0,3.333333333,3.5,4.333333333,4.5,2.2,2.25,3.2,4.0,11,9,16,20,6,E,1,3,0,0,1,0,6,8,4,3,4,5,2,2,2,4,5,5,2,5,3,4,4,4,3,3,3,3,4,4,3,3,4,2,2,4,4,3,4,4,3,4,4,4,4,3,3,2,2,3,2,4,3,4,4,5,2,5,2,2,5,2,5,4,2,3,3,3,3,1,7.25,8,3.333333333,3.6,3.4,2.8,3.8,3.8,3.833333333,2.5,3.0,4.333333333,5.0,2.0,3,3,3,1,4.0,4.0,4.5,4.5,4.0,23,0,8.0,1,1,0,1,1,1,1,1,0,2,1,1,1,1,0,2,1,2,1,1,0,2,0,1,4,4,3,3,3,3,3,4,4,4,4,3,4,4,3,3,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,2,0,1,6,4,3,4,3,3,3,4,4,4,4,4,2,3,3,3,0.681818182,4.2,1.8,5.0855,0.9145,4.827,5.305,5.238,5.331,4.725,0.9145,吴环宇,1.0,"吴环宇
关于植物物种识别，使用智能AI大模型训练识别物种的显著形态特征，增强对每个物种的形态（叶，花，果实等）的知识储备，增加在不同场景，不同时间的植物特征的图片，以及排除其他植物作为背景的干扰，结合采集时间，采集地点（海拔等），往年标本等综合识别，并利用反馈进行增强。",3.976539890501658e-12,0.07407407407407407,0.0,0.07407407407407407,0.010366275051831375,0.49677954965131943,0.046589914709329605,0.34523809523809523,0.015104166666666696
162006,7,1607,启发员,4.0,20954.0,2.0,2.0,5.0,3.333333333333333,3.0,4.666666666666667,5.333333333333333,3.333333333333333,3.333333333333333,5.0,4.0,5.0,5.0,3.175925926,4.055555556,3.333333333,3.0,3.2,3.3,3.8,0.25,4.2,4.0,3.333333333,4.0,3.666666667,3.0,4.0,4.0,4.25,3.4,4.25,3.6,3.6,17,17,18,18,7,E,1,1,0,0,1,0,8,8,4,4,4,4,4,4,3,3,3,3,5,4,4,4,4,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,4,5,4,4,4,4,4,4,4,5,4,4,4,2,2,4,2,3,2,2,3,3,2,3,4,4,5,3,3,5,8.0,8,4.0,3.4,4.0,4.0,4.2,4.2,4.333333333,4.0,4.25,2.0,3.333333333,2.25,5,3,3,5,3.0,2.5,2.5,2.5,2.0,23,1,8.0,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,0,2,1,1,1,1,1,1,5,4,2,3,4,4,3,5,4,4,4,3,2,3,3,4,9,1,1,1,1,0,1,1,1,1,1,1,1,0,2,1,1,0,1,1,1,8,4,2,4,4,4,4,5,4,4,4,4,2,3,3,4,0.727272727,2.5,5.5,3.3855,4.6145,3.827,3.805,3.238,3.331,2.725,4.6145,谢善杰,2.0,"甲骨文分析：使用AI直接将甲骨文这类只有文字，没有其具体含义的数据，投给AI大模型，让AI进行学习其中的关系，进行含义的解释，甚至生成创作其他的甲骨文。
电影解读：投喂AI一部电影，生成电影的解说视频，有一定的商业价值。",1.3684883081306839e-11,0.1568627450980392,0.12244897959183672,0.1568627450980392,0.01251137397634213,0.5453465988431909,0.03901895880699158,0.4090909090909091,0.017832647462277085
162007,7,1607,协调员,6.0,20955.0,6.0,7.0,4.0,4.666666666666667,5.0,5.0,3.333333333333333,5.0,4.666666666666667,4.666666667,5.0,5.0,5.0,4.228703704,4.372222222,4.233333333,4.4,4.8,3.9,3.9,0.0,5.0,5.0,5.0,5.0,5.0,5.0,3.0,5.0,5.0,4.0,4.75,5.0,5.0,20,19,25,25,7,E,1,2,0,0,1,0,10,10,5,5,5,5,5,5,5,2,3,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,5,5,5,5,5,5,5,5,4,5,4,5,5,5,5,5,3,4,4,2,4,2,2,4,1,4,4,4,5,5,4,5,4,10.0,10,5.0,3.6,5.0,5.0,4.8,5.0,5.0,4.5,5.0,3.666666667,4.0,1.75,5,4,5,4,2.5,2.0,2.0,2.5,2.0,27,1,9.0,0,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,9,5,5,5,5,5,5,5,5,5,5,5,3,4,5,5,10,1,1,1,1,0,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,9,5,5,5,5,5,5,5,5,5,5,5,2,5,5,5,0.772727273,2.2,7.8,3.0855,6.9145,3.327,3.305,2.738,3.331,2.725,6.9145,吴曦,1.0,"AI 用于历史语言的学习和翻译，例如考古文的学习和理解，以及转换为现在常用的语言（中文和英文）。这样能够很好的帮助你们解决语言的理解问题，以及发现历史语言中承载的故事和秘密。
AI 模仿历史大家的文学风格或者作曲风格，重新把现代化的故事和歌曲，以历史人物大家的风格重新展示出来，让人们从身边的故事感受到历史人物的魅力。
AI 整理电影故事以及流行电影风格，融合彼此，创作出新的电影供人们观影。AI的出现能够减少演员的参与以及提高电影情节的多样性，可以按照观众的喜好设计出特定的剧情。",5.5481757177512794e-09,0.11764705882352941,0.0909090909090909,0.11764705882352941,0.017151481888035124,0.49821701307030164,0.1358681321144104,0.3877551020408163,0.023622047244094446
162008,7,1607,记录员,2.0,20955.0,2.0,2.0,2.6666666666666665,5.0,6.0,4.666666666666667,3.0,3.0,3.333333333333333,6.0,6.0,5.0,5.0,3.890740741,3.344444444,3.066666667,3.4,4.6,4.2,4.4,0.875,4.2,4.666666667,4.333333333,4.0,4.0,3.666666667,4.0,4.666666667,4.75,3.4,4.0,3.8,3.6,17,16,19,18,7,E,1,3,0,0,1,0,8,9,4,3,4,4,3,5,3,3,3,3,4,3,5,5,3,4,4,5,4,3,4,5,4,3,4,4,4,4,4,4,5,5,5,3,4,4,4,3,4,4,5,4,4,4,4,3,4,4,1,4,1,1,4,1,3,2,5,5,5,4,3,4,8.625,9,3.833333333,3.2,4.2,3.8,4.4,4.0,4.0,4.0,4.0,3.333333333,4.0,1.0,5,4,3,4,2.0,2.0,2.5,2.5,2.0,21,1,7.0,1,1,1,1,1,2,1,1,1,2,0,1,0,1,0,1,1,2,0,2,0,2,1,2,7,4,4,3,4,4,4,4,4,5,3,4,3,3,3,4,8,1,1,1,1,0,2,1,1,1,1,1,1,0,1,0,2,1,2,1,1,8,5,4,4,4,5,5,4,4,5,4,4,1,3,3,3,0.636363636,2.2,5.8,3.0855,4.9145,2.827,3.305,3.238,3.331,2.725,4.9145,武轩宇,3.0,"冷门绝学-7组-武轩宇
AI如何结合？
“考古：甲骨文”人们虽然也不理解甲骨文的意义，但AI学习后可以翻译为中文，帮助我们理解。
预测蛋白质的折叠后的空间结构，计算一些研究的可行性。
AI配音、编曲、唱歌，降低了短视频创作门槛",2.3624649312776793e-07,0.19354838709677416,0.13793103448275862,0.19354838709677416,0.019569471624266144,0.4362790264653984,0.05511084571480751,0.3333333333333333,0.027251184834123254
162009,9,1609,启发员,4.0,20956.0,3.0,3.0,3.333333333333333,5.333333333333333,3.6666666666666665,4.0,6.0,2.333333333333333,2.0,3.666666667,4.333333333,4.333333333,4.0,4.175,4.05,3.3,2.8,4.2,3.5,4.8,0.125,4.4,3.0,3.333333333,5.0,3.666666667,4.0,3.0,4.333333333,4.75,2.8,3.25,2.6,4.2,14,13,13,21,9,E,1,1,0,0,0,0,9,9,5,4,4,5,5,4,5,5,3,4,5,5,4,2,5,5,4,4,4,4,2,3,3,3,3,4,3,4,4,2,5,5,4,4,4,4,5,4,4,3,4,4,4,3,3,4,3,3,3,4,2,2,4,2,2,4,4,4,4,2,3,3,9.0,9,4.5,4.4,3.4,3.2,4.0,4.2,4.166666667,3.75,3.5,3.0,3.666666667,2.25,4,2,3,3,6.0,6.5,5.5,6.0,6.0,19,1,8.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,1,2,8,5,2,5,4,4,3,5,5,5,5,5,3,2,2,2,9,1,1,1,1,1,1,1,1,1,2,1,1,1,1,0,2,1,2,1,1,8,5,1,4,3,3,3,5,5,5,4,3,2,2,2,3,0.909090909,6.0,3.0,6.8855,2.1145,6.827,7.805,6.238,6.831,6.725,2.1145,周敬轩,1.0,"古生物学是一门有发展潜力的学科。
我们可以通过AI多模态学习的能力来向其同时输入数值与文字数据（如分子量、放射性、地层数据等）和图像数据（化石图片、显微观察图片、地层剖面等），来让AI对古生物进行分析和复原，以达到推测古生物年代、所属分类细目等目的。
也可以通过其总结和举一反三的能力，同时收集现存生物的各项生理和行为数据，通过对古生物结构、年代的总结分析来推测出古生物的可能外表和行为特征。还能进一步为其制作精细化的模型，模拟当时的生态系统。
另外，还可以通过以上的结果，生产一些面向大众的科普材料，如动画、3D建模、博物画等等。",4.4345723046856763e-10,0.043478260869565216,0.022222222222222223,0.043478260869565216,0.01807991321641656,0.5493066719837856,0.1665658801794052,0.38125,0.019639934533551506
162010,9,1609,协调员,5.0,20956.0,4.0,4.0,4.333333333333333,4.0,4.666666666666667,4.333333333333333,3.6666666666666665,3.0,3.0,6.0,4.333333333,5.0,5.0,3.861111111,4.166666667,4.0,4.0,5.0,4.3,5.0,0.5,4.0,5.0,3.666666667,4.8,5.0,3.666666667,4.5,4.0,4.25,3.8,4.25,3.2,3.8,19,17,16,19,9,E,1,2,0,0,0,0,8,6,4,4,5,5,5,5,5,5,5,5,4,4,4,5,4,4,5,5,5,5,5,5,4,3,3,4,4,4,4,4,4,4,4,3,4,5,5,5,5,4,4,5,4,4,4,4,4,4,2,4,2,2,4,2,4,4,5,5,5,3,4,6,6.75,6,4.666666667,4.8,5.0,3.6,4.0,4.2,4.333333333,4.5,4.25,4.0,4.0,2.0,5,3,4,6,7.5,6.5,6.0,7.0,7.0,20,0,9.0,1,1,1,1,1,1,1,1,1,2,1,2,1,2,0,2,1,1,0,2,1,2,1,2,7,4,3,4,5,5,5,5,5,5,4,5,3,3,3,3,8,1,2,1,1,1,1,1,2,1,2,1,1,1,1,0,2,1,2,0,2,7,4,3,4,5,5,5,4,4,4,4,4,3,3,3,3,0.818181818,6.8,1.2,7.6855,0.3145,8.327,7.805,6.738,7.831,7.725,0.3145,何思扬,2.0,"“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。
·请选择一门你认为具有更大发展潜力的冷门学科：
古生物学 
·分析其在人工智能时代可能焕发新生命力的可能原因，
1标本处理与安全：借助人工智能可高效采集、处理古生物标本信息，结合区块链技术，能保障标本数据安全，为研究奠定基础。
2数据挖掘与比对：通用人工智能强大的搜索和比对能力，能处理海量古生物数据，挖掘规律，助力精准识别物种、推断习性，推动研究进展。
3基于已有数据发掘预测：依据现有数据，利用人工智能算法模型预测古生物结构与生存环境，为实地发掘提供指导，提高发掘效率，促进研究模式转变。
4科普创新，促进教育事业发展：人工智能的多模态学习能力融合文本、图像等信息，生成可视化古生物 3D 模型，既有助于科研，也能以直观形式用于科普，提升大众兴趣。可以应用于博物馆科普等等
·在未来发展中面临的可能挑战。
1 通用智能数据不够专精于特定专业：通用智能数据广泛但针对古生物学的专业性不足，难以满足特定数据要求，影响分析准确性。
2 资金不足：古生物学研究本就资金耗费大，引入人工智能技术后，软件、硬件、人才培养等方面的资金需求增加，学科冷门使其难以吸引足够投入。
3 人机协调问题，专家与人工智能的意见不一等等",9.317672004679785e-05,0.28571428571428575,0.1702127659574468,0.24489795918367344,0.04935775771611189,0.6410074720303718,0.1757684350013733,0.30213903743315507,0.04459124690338567
162012,10,1610,启发员,8.0,20957.0,4.0,4.0,4.333333333333333,5.333333333333333,5.0,4.666666666666667,3.0,5.0,3.6666666666666665,5.0,5.666666667,4.666666667,5.0,3.125,3.75,3.5,4.0,4.7,4.0,4.7,0.125,3.6,5.0,4.333333333,1.8,2.333333333,2.0,4.0,4.666666667,3.25,3.4,3.25,4.8,4.8,17,13,24,24,10,E,1,1,0,0,0,0,8,10,5,5,3,4,3,3,3,2,4,2,4,5,3,3,4,4,5,5,4,4,5,3,3,2,3,4,3,5,4,4,4,4,5,5,4,4,4,5,5,4,4,4,4,5,4,2,3,3,4,4,2,4,5,1,3,4,5,5,4,2,3,7,9.25,10,3.833333333,3.0,4.2,3.0,4.2,4.4,4.0,4.5,4.25,2.666666667,4.0,2.75,4,2,3,7,2.0,2.0,2.0,2.5,2.0,20,1,4.0,1,1,1,1,1,2,1,2,1,2,0,2,0,1,1,2,0,2,1,2,0,2,0,2,2,2,2,2,3,1,3,1,1,4,2,1,3,4,3,4,8,1,1,1,1,1,1,1,1,1,2,1,2,1,1,1,2,0,2,0,1,7,5,3,5,5,5,5,4,3,4,4,3,3,5,5,5,0.681818182,2.1,5.9,2.9855,5.0145,2.827,3.305,2.738,3.331,2.725,5.0145,马越林,1.0,人工模拟古代建筑，以玩家的身份体验考古发掘的过程。 读取大量古代文献资料以及现代物理化学相关的信息，以计算机模拟出古代乐器的结构以及他们所使用的材料。,8.625813855474808e-41,0.0,0.0,0.0,0.002330060892257984,0.2590029648650043,0.01286623626947403,0.35,0.003919372900335949
162013,10,1610,协调员,8.0,20958.0,5.0,5.0,2.6666666666666665,4.0,4.0,5.666666666666667,3.333333333333333,2.333333333333333,3.0,5.333333333,4.666666667,5.333333333,4.666666667,4.361111111,4.166666667,4.0,4.0,4.1,4.8,4.7,0.25,3.4,4.0,3.0,3.0,3.666666667,2.666666667,4.0,4.0,4.0,3.8,3.75,3.8,3.8,19,15,19,19,10,E,1,2,0,0,0,0,8,9,4,4,4,4,4,4,4,4,4,4,5,4,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,5,4,4,4,4,4,4,4,4,5,4,4,4,5,4,5,4,5,4,4,1,4,2,4,4,1,4,4,4,4,5,4,4,6,8.625,9,4.0,4.2,4.0,4.0,4.2,4.0,4.5,4.25,4.5,4.333333333,4.0,2.0,5,4,4,6,6.0,6.0,6.0,6.0,6.5,20,0,5.0,0,2,1,1,1,1,1,2,0,2,1,2,0,2,0,2,0,2,1,2,1,2,1,2,4,3,2,3,4,3,4,3,4,3,2,3,4,3,2,4,7,1,2,1,2,0,2,0,2,0,2,1,2,1,2,1,2,0,2,0,2,4,4,2,3,4,4,4,4,3,3,3,4,4,3,2,2,0.545454545,6.1,1.9,6.9855,1.0145,6.827,7.305,6.738,6.831,7.225,1.0145,程雯,3.0,"任务二
任务二：【冷门绝学】
“冷门绝学”是指那些在学术体系中极为专业化或小众化、社会关注度较低，但蕴含独特价值与潜力的学科。这些学科可能承载了深厚的历史文化积淀，或者在科学技术与人类发展中具有重要意义。在通用人工智能时代，随着技术的发展与知识体系的变革，某些冷门学科有望焕发新的生命力，但也将面临诸多独特的挑战。
请选择一门你认为具有更大发展潜力的冷门学科，分析其在人工智能时代可能焕发新生命力的可能原因，与其在未来发展中面临的可能挑战。你可以结合通用人工智能的关键能力（如思维链、举一反三、多模态学习、反思能力等），说明如何利用AI技术助力该学科的传承、创新与社会应用。
①在人工智能时代可能焕发新生命力的可能原因：
考古学研究中积累了大量的数据，包括遗址发掘记录、文物照片、陶片拓片等。传统方法处理这些数据耗时耗力，而AI技术可以快速处理和分析海量数据，提高研究效率；
AI的图像识别技术可以自动识别和分类文物，减少人工操作的误差和时间成本。例如，山东大学文化遗产研究院的方辉教授通过陶片AI拼对实验，展示了AI在提升拼对效率中的重要性；
②如何利用AI助力该学科：
通过线上建立古建筑、著名景点的模型，让更多人可以在线上平台体验考古的过程和乐趣；
通过AI技术和古代乐器相结合建造线上乐器演奏平台，让人们可以体会到古代乐器演奏的美妙和乐趣；
利用AI的图像识别技术，可以自动识别和分类文物",,,,,,,,,
162014,10,1610,记录员,11.0,20958.0,6.0,7.0,1.3333333333333333,4.333333333333333,4.666666666666667,4.666666666666667,6.0,1.3333333333333333,2.0,3.666666667,3.0,3.333333333,5.0,3.803703704,3.822222222,2.933333333,3.6,4.1,4.8,5.0,0.125,4.0,4.0,3.0,4.4,4.333333333,3.0,3.0,4.0,2.75,1.2,2.5,2.8,2.6,6,10,14,13,10,E,1,3,0,0,0,0,5,5,4,4,4,4,3,3,4,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,4,4,4,4,4,3,3,4,3,4,2,3,4,2,3,3,4,4,4,1,2,4,5.0,5,3.666666667,3.4,4.0,3.6,4.0,4.0,4.0,3.5,4.0,3.0,4.0,2.5,4,1,2,4,4.0,3.0,4.5,5.0,5.0,21,0,5.0,1,1,1,1,1,1,1,1,0,2,1,1,1,1,0,1,1,1,0,2,0,2,1,1,6,3,3,3,4,4,5,5,5,5,3,4,4,2,2,2,5,1,1,1,1,1,2,1,1,0,2,1,1,1,1,1,2,0,2,1,1,7,3,3,3,4,4,4,4,4,5,3,4,4,2,1,1,0.727272727,4.3,0.7,5.1855,-0.1855,4.827,4.305,5.238,5.831,5.725,0.1855,张扬,2.0,"冷门绝学
考古学，遗址研究
通过模型搭建的游戏或技术重新搭建古建筑或遗址群，开放给游客参观。
通过模拟考古过程，使游客充分体验考古过程的乐趣，学习考古相关的知识。
通过AI模拟古代乐器，现代人也可以通过AI技术体验古代乐器，并使用古代乐器进行自主编曲。
通过AI预测古代典籍、古代物品、古代乐器的形态，解读、破解现代人可能需要很多年的研究才能发现的未解之谜。
通过AI技术帮助考古过程，为考古工作人员提供更多的技术支持。",7.430188216399958e-10,0.163265306122449,0.1276595744680851,0.163265306122449,0.01737063333868436,0.47575471057067015,0.14153176546096802,0.38333333333333336,0.021146616541353414
162015,11,1611,启发员,9.0,20959.0,6.0,7.0,5.333333333333333,4.333333333333333,6.0,4.666666666666667,1.0,3.0,2.333333333333333,5.333333333,4.333333333,4.333333333,5.666666667,4.413888889,4.483333333,3.9,3.4,4.5,3.8,4.2,0.5,4.4,5.0,4.333333333,4.0,5.0,4.0,4.5,5.0,5.0,1.8,4.25,3.4,3.6,9,17,17,18,11,E,1,1,0,0,0,0,9,6,4,4,4,4,4,3,3,3,3,3,3,4,4,4,5,5,4,5,3,2,2,2,3,2,2,3,2,4,4,3,5,5,4,3,4,4,5,4,4,4,4,5,5,4,4,3,3,4,1,4,1,1,4,1,4,4,5,5,4,3,3,5,7.125,6,3.833333333,3.0,2.8,2.4,4.2,4.0,4.333333333,4.0,4.5,3.333333333,4.0,1.0,4,3,3,5,4.5,4.5,4.5,4.5,6.0,23,0,9.0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,1,1,1,1,9,5,2,5,5,5,5,5,4,5,3,3,2,2,2,3,9,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,0,1,1,1,9,5,3,5,5,5,5,4,5,4,5,4,2,2,2,5,0.954545455,4.8,4.2,5.6855,3.3145,5.327,5.805,5.238,5.331,6.725,3.3145,王涵,2.0,"冷门学科：地质勘查。
冷门原因：采集地质数据的过程较艰苦，工作量大，且采到的数据不一定是有效的。
通用人工智能如何帮助此学科焕发生命力：使用大量采集难度低的地址数据训练大模型，由大模型协助确定进一步精细采集数据的位置等。对预训练好的通用机器人进行微调，教他使用地质勘测工具去野外采集数据，从而代替人类进行又脏又累还技术含量不高的数据收集工作。这样，更多的地质研究者可以聚焦于数据分析，为生产生活提供更多指导。",5.802904634728949e-11,0.0,0.0,0.0,0.013214855320118478,0.43131965132406475,0.11341638118028641,0.3333333333333333,0.016494845360824795
162016,11,1611,协调员,2.0,20960.0,3.0,4.0,4.0,3.6666666666666665,3.0,3.333333333333333,3.6666666666666665,3.6666666666666665,3.6666666666666665,3.666666667,3.333333333,3.0,3.333333333,3.509259259,3.055555556,3.333333333,3.0,3.1,3.6,3.3,0.0,4.0,3.666666667,3.666666667,4.0,4.0,3.666666667,5.0,4.666666667,4.25,4.4,4.5,4.6,4.0,22,18,23,20,11,E,1,2,0,0,0,0,9,9,4,4,5,5,4,5,4,4,4,5,4,5,5,4,4,5,4,4,5,4,4,5,5,5,4,4,4,4,4,4,4,5,5,5,5,5,5,4,4,5,5,5,5,4,4,5,5,4,4,5,5,5,4,5,5,4,4,5,4,4,4,5,9.0,9,4.5,4.2,4.4,4.4,4.2,5.0,4.5,4.5,4.5,5.0,4.333333333,4.75,4,4,4,5,7.0,8.0,7.5,7.0,7.0,25,0,7.0,1,1,1,1,1,2,1,1,1,2,0,1,1,1,1,1,0,1,0,1,0,2,1,1,6,3,4,4,4,4,4,4,4,4,4,4,3,3,4,4,8,1,1,1,1,1,1,1,1,1,2,0,1,0,2,0,2,0,1,0,1,6,4,3,4,4,3,4,4,4,4,4,4,3,4,3,4,0.590909091,7.3,1.7,8.1855,0.8145,7.827,9.305,8.238,7.831,7.725,0.8145,许艺炜,3.0,,,,,,,,,,
162017,11,1611,记录员,8.0,20958.0,5.0,5.0,2.333333333333333,3.333333333333333,5.0,4.666666666666667,4.0,4.0,4.0,4.333333333,4.333333333,4.333333333,4.333333333,4.119444444,4.716666667,4.3,4.8,3.8,2.5,4.2,0.5,4.4,4.0,4.666666667,4.4,3.666666667,4.666666667,3.5,4.333333333,3.75,3.4,3.0,2.6,3.2,17,12,13,16,11,E,1,3,0,0,0,0,4,8,2,2,3,3,4,4,3,4,2,4,2,4,5,5,5,2,3,4,3,4,5,3,5,4,4,3,2,4,4,4,5,4,4,5,5,5,5,4,5,5,3,3,3,4,4,4,5,4,1,4,4,3,5,2,5,5,4,4,2,4,1,2,6.5,8,3.0,3.0,3.8,3.6,4.2,4.8,4.0,4.25,3.5,4.666666667,4.333333333,2.5,2,4,1,2,4.0,3.0,4.5,5.0,6.0,19,0,9.0,1,1,1,1,1,1,1,1,1,2,1,1,1,1,0,1,1,1,1,2,1,2,1,2,7,5,5,4,4,3,4,4,4,5,5,4,4,4,4,4,8,0,2,1,1,0,1,1,1,0,2,1,1,1,1,0,2,0,2,1,1,7,5,5,4,4,4,4,5,4,4,4,5,4,4,4,4,0.727272727,4.5,-0.5,5.3855,-1.3855,4.827,4.305,5.238,5.831,6.725,1.3855,江琳钰,1.0,"地质考察
重要作用
主要工作集中于资源勘查、工程勘察、地质灾害防治等领域（地震监测，地铁选址等），在地球科学研究、资源勘探、环境保护等方面有着重要作用，近年随着地质学技术发展，重要性逐渐上升
面临的问题（冷门原因）
工作环境艰苦且外耗费大量时间精力，
采集难度高，需要花费大量时间采集和研究样本，
并且本身的薪资水平不高，付出回报不成正比
AI技术助力
通用人工智能可以极大程度上的减少采集资源所需的时间成本和人力成本，全方面提升降低研究难度，同时提高研究速度，助力地质学发展",5.114141771194988e-09,0.02985074626865672,0.0,0.02985074626865672,0.016292817406789267,0.436355637016197,0.06731350719928741,0.3088235294117647,0.018245004344048632
161009,13,1613,启发员,5.0,19962.0,3.0,3.0,2.333333333333333,3.333333333333333,4.666666666666667,4.666666666666667,4.666666666666667,4.0,3.6666666666666665,4.0,4.333333333,4.666666667,6.0,4.099074074,4.594444444,4.566666667,4.4,4.2,4.3,5.0,0.5,4.4,4.333333333,3.666666667,4.2,5.0,4.0,4.5,4.666666667,4.0,3.8,4.25,4.2,4.8,19,17,21,24,13,E,1,1,0,1,0,0,7,8,4,5,4,4,4,4,4,5,4,5,4,4,4,4,4,5,4,4,3,4,4,4,4,4,4,4,4,4,3,4,4,4,4,5,4,4,5,4,4,3,4,4,4,4,4,3,4,5,2,4,2,2,4,4,3,4,4,5,4,3,3,4,7.625,8,4.166666667,4.4,3.8,4.0,3.8,4.4,4.166666667,3.75,4.0,3.333333333,4.333333333,2.5,4,3,3,4,7.5,6.5,5.5,6.5,7.0,19,1,7.0,1,1,1,1,1,1,1,1,1,2,1,1,0,1,0,1,1,1,1,2,1,2,1,1,8,5,3,4,5,5,5,4,5,5,4,3,3,4,3,4,6,1,1,1,1,0,2,1,1,1,1,1,1,1,1,1,2,0,2,1,1,7,4,4,3,4,4,5,5,5,5,3,4,4,4,4,4,0.818181818,6.6,0.4,6.6,0.4,7.5,6.5,5.5,6.5,7.0,0.4,卢崇智,1.0,"经过小组讨论，我们觉得这种未来的“AI恐怖分子”首先可能会在数据以及隐私泄露等方面对人类产生威胁，其次在军事等方面通过恶意调控ai植入病毒数据等，对人类造成不必要的物理即身体层面的伤害，最后可能通过ai的幻觉现象造成伤害性极大的谣言的传播。
关于应对方案，我们觉得最基本的方法是对齐与超级对齐，首先可以通过分而治之和立法的两个基本层面，对机器人或者ai指定基本的安全指令，指令的优先级应调至最高。再者我们也可以通过研发新的ai以及大模型与现有进行相互监督、相互制约。
以上便是我们小组讨论的基本结果，外加我个人的一些小小的看法。",1.892467450013168e-05,0.27777777777777773,0.23529411764705882,0.27777777777777773,0.03907439358570993,0.6101123430643033,0.15434148907661438,0.31788079470198677,0.04359925788497221
161010,13,1613,协调员,5.0,19963.0,5.0,6.0,3.6666666666666665,5.666666666666667,5.666666666666667,4.666666666666667,3.6666666666666665,4.0,4.666666666666667,5.0,4.666666667,5.0,4.666666667,4.033333333,4.2,4.2,4.2,4.9,4.3,4.9,0.75,4.2,3.666666667,5.0,4.8,4.666666667,4.333333333,4.0,4.0,4.25,2.8,3.5,1.6,4.4,14,14,8,22,13,E,1,2,0,1,0,0,9,8,5,5,5,5,5,5,5,4,5,4,5,4,4,5,5,5,5,5,4,5,4,5,4,4,4,4,4,5,4,5,4,4,4,4,4,4,4,3,4,2,4,4,4,4,4,3,4,4,4,4,1,1,4,1,4,3,4,5,5,1,3,4,8.375,8,5.0,4.6,4.6,4.0,4.4,4.0,4.666666667,3.25,4.0,3.666666667,4.0,1.75,5,1,3,4,8.5,8.0,6.0,7.0,7.5,27,1,8.0,1,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,9,5,4,4,5,4,5,5,5,5,5,4,1,4,5,5,8,1,1,1,1,1,1,0,1,1,1,1,1,1,1,0,1,0,1,1,1,8,5,5,5,4,3,4,4,4,4,5,4,1,4,4,4,0.772727273,7.4,1.6,7.4,1.6,8.5,8.0,6.0,7.0,7.5,1.6,兰英健,3.0,"安全特工：
我们小组提到的对于人类的威胁有以下几个方面：
1. AI 恐怖分子会利用机器人对人类进行肉体上的消灭；
2.AI 恐怖分子会泄露各种隐私，包括个人隐私和国家的安全数据；
3.AI 恐怖分子会利用信息茧房，加深人类各种族之间的仇恨、加剧人类内部矛盾，造成人类大规模战争和毁灭；
我们讨论的解决方案有：
利用对齐技术对 AI 做基本约束，让其只做符合人类伦理的事情；
利用超级对齐技术给 AI 立法，让 AI 能够遵守法律；
利用超级对齐技术的分而治之方法，针对 AI 的不同阶段制定不同的对齐方案；
创造针对 AI恐怖分子的 专门 AI，专门打击相关 AI 犯罪；
未来的展望：
AI 的发展将来会超过人类的智慧，我们会被 AI 远远超越；
AI 将来会产生自主意识。",1.512591995260643e-08,0.30476190476190473,0.2912621359223301,0.30476190476190473,0.022362308723849415,0.4633896772777374,0.13958518207073212,0.4182692307692308,0.02510948905109489
161011,13,1613,记录员,2.0,19963.0,4.0,4.0,5.0,6.0,3.0,4.666666666666667,4.666666666666667,4.0,4.0,5.333333333,6.0,5.333333333,5.333333333,3.084259259,4.505555556,4.033333333,3.2,5.0,4.8,4.1,0.125,4.4,5.0,4.0,4.4,4.0,3.666666667,4.5,4.666666667,5.0,5.0,5.0,4.6,4.8,25,20,23,24,13,E,1,3,0,1,0,0,7,7,5,4,5,5,4,5,4,4,4,4,5,5,4,5,5,3,5,5,5,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,4,5,5,1,5,1,1,5,1,4,5,5,5,5,5,3,4,7.0,7,4.666666667,4.2,4.8,5.0,5.0,5.0,4.5,5.0,5.0,4.333333333,5.0,1.0,5,5,3,4,7.5,7.0,6.5,6.5,6.5,20,1,7.0,0,2,1,1,1,2,1,1,0,2,1,1,0,1,1,1,1,1,0,2,0,2,1,2,7,4,3,4,4,4,4,4,4,5,5,4,4,4,4,4,7,1,1,1,1,1,2,0,2,1,2,1,1,1,1,1,1,0,2,0,1,7,4,4,4,5,5,5,5,4,5,4,4,3,4,4,4,0.636363636,6.8,0.2,6.8,0.2,7.5,7.0,6.5,6.5,6.5,0.2,祝森,2.0,"未来，AI可能在很多领域带来重大安全风险，一：可能会通过大数据将人类的隐私泄露；二：当AI以人类无法阻碍的发展速度突破人类控制后没可能会对国家军事安全产生威胁；三：当AI发展到一定程度后有了自主意识，可能会挑起国家间纷争，破坏人类和谐相处的局面。
    面对这些风险我们可以利用对齐及超级对齐，通过对AI立法，分而治之，加强AI的防御系统，其次可以利用AI来管理AI，利用不同模态的机器人相互制约，来应对AI对人类做出的重大风险安全。
    最后随着AI安全防御的不断升级，对于AI管理的应对方法不断实践中，人类定能将AI限制在可管控的范围里，以更安全，更便利，更优质的姿态服务人类，造福人类。",0.00021894769575192188,0.5,0.47619047619047616,0.5,0.050521337203052775,0.5462634547104649,0.18319329619407654,0.3279569892473118,0.05824284304047389
161012,13,1613,启发员,7.0,19964.0,7.0,8.0,3.0,3.6666666666666665,4.0,2.6666666666666665,3.333333333333333,4.0,3.6666666666666665,4.0,4.0,3.666666667,4.0,3.103703704,3.622222222,3.733333333,3.4,3.9,3.8,4.2,0.25,4.0,3.666666667,3.666666667,4.0,3.333333333,3.0,3.5,4.0,3.75,3.0,3.5,3.4,4.0,15,14,17,20,13,E,1,1,0,1,0,0,6,7,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,3,3,4,3,3,3,4,4,4,1,4,2,6.625,7,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,4.0,3.666666667,4.0,3.0,4,1,4,2,8.0,8.0,7.0,6.5,7.0,29,0,5.0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,0,2,1,2,1,1,1,1,1,1,6,3,3,3,3,3,4,4,4,4,4,4,3,4,4,3,6,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,2,1,1,6,4,3,4,4,4,3,4,4,4,4,4,3,4,4,4,0.818181818,7.3,-1.3,7.3,-1.3,8.0,8.0,7.0,6.5,7.0,1.3,马阳,,"AI恐怖分子可能在哪些领域或技术层面带来重大安全风险？面对这些风险，你会如何预防和监测，利用哪些技术来设计有效的应对方案？请你结合具体场景和技术原理，提出具有可行性和创新性的解决方法，并展望AI安全防御技术的未来发展。
AI恐怖分子会带来的安全风险，包括对个体的威胁和对群体的威胁。对个体的威胁可以分为，对个体精神、心理领域的威胁和对个体身体、物理上的伤害。目前而言，很多人都会将AI作为情感陪伴助手，将其视为朋友、家人或恋人，前段时间国外也出现了第一起AI杀人事件。而对个体身体、物理上的伤害更加不言而喻，AI机器人会携带武器，会喷火等。对群体的威胁，包括AI可能会产生不实的信息，挑起或加深群体间的对立和仇恨；也可能泄露重大机密信息从而威胁国家安全。不可忽视的是，AI会消耗大量的能量和资源，是个资源消耗型和环境不友好型的产品。
面对这些风险，需要加强对AI的伦理道德监管机制，确保AI的第一性原理得到充分贯彻（不得伤害人类）。需要超级对齐和对齐策略，也需要实行兼听则明、偏听则暗的策略，让AI辅助决策而不是代替人类决策；也需要鼓励不同的声音出现，不仅有某家企业的LLM，更需要百花齐放的LLM或AI工具。以及应用不同智能体之间相互博弈相互制衡的方式，让AI互相监督。作为人类，需要掌握足够AI的知识和技能，管理好约束好AI工具。",0.00025999659848841293,0.8333333333333333,0.7647058823529411,0.8333333333333333,0.05285080258202854,0.649742227602053,0.1751234382390976,0.2958579881656805,0.04925602873268342
